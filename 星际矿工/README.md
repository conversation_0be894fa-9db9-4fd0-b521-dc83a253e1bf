# 星际矿工 (Space Miner)

一款基于HTML5的太空主题挖掘游戏，支持移动端和桌面端，具有丰富的升级系统和精美的视觉效果。

## 🎮 游戏特色

### 核心玩法
- **太空探索**: 驾驶飞船在星际空间中自由飞行
- **资源挖掘**: 挖掘各种稀有度的矿物资源
- **飞船升级**: 使用收集的资源升级飞船性能
- **多星球探索**: 访问不同类型的星球，发现独特的矿物

### 技术特点
- **跨平台支持**: 完美支持桌面端和移动端
- **静态部署**: 纯前端实现，可部署到任何静态服务器
- **本地存储**: 使用localStorage保存游戏进度
- **响应式设计**: 自适应不同屏幕尺寸
- **高性能渲染**: 优化的Canvas渲染引擎
- **粒子系统**: 丰富的视觉特效

## 🚀 快速开始

### 环境要求
- 现代浏览器（支持HTML5 Canvas和localStorage）
- 无需服务器，可直接打开index.html运行

### 安装运行
1. 下载或克隆项目文件
2. 直接在浏览器中打开 `index.html`
3. 或使用本地服务器运行（推荐）

```bash
# 使用Python简单服务器
python -m http.server 8000

# 使用Node.js serve
npx serve .

# 访问 http://localhost:8000
```

## 🎯 游戏操作

### 桌面端控制
- **移动**: WASD 或 方向键
- **挖掘**: 空格键 或 鼠标左键
- **推进**: Shift键
- **暂停**: P键
- **菜单**: M键 或 Esc键
- **保存**: Ctrl+S

### 移动端控制
- **移动**: 虚拟摇杆
- **挖掘**: 挖掘按钮
- **推进**: 推进按钮
- **菜单**: 点击菜单按钮

## 🛠️ 游戏系统

### 资源系统
- **水晶 💎**: 主要货币，用于购买升级
- **能量 ⚡**: 飞船能量，用于移动和挖掘
- **升级点 🔧**: 特殊升级货币

### 矿物类型
- **普通 (白色)**: 基础矿物，价值1水晶
- **不常见 (蓝色)**: 价值3水晶
- **稀有 (紫色)**: 价值8水晶
- **史诗 (橙色)**: 价值20水晶
- **传说 (红色)**: 价值50水晶

### 升级系统
- **挖掘效率**: 提高挖掘速度和伤害
- **能量容量**: 增加最大能量值
- **移动速度**: 提高飞船移动速度
- **自动挖掘**: 解锁自动挖掘功能

### 星球类型
- **岩石星球**: 平衡的矿物分布
- **冰冻星球**: 富含稀有矿物
- **气态星球**: 特殊能量矿物
- **火山星球**: 高价值但危险
- **水晶星球**: 传说级矿物丰富

## 📱 移动端优化

### 触摸控制
- 虚拟摇杆控制飞船移动
- 专用按钮进行挖掘和推进
- 优化的触摸响应

### 界面适配
- 响应式布局设计
- 移动端专用UI元素
- 优化的字体和按钮大小

### 性能优化
- 自适应画质设置
- 智能粒子数量控制
- 优化的渲染循环

## 💾 存储系统

### 自动保存
- 每30秒自动保存游戏进度
- 页面关闭时自动保存
- 支持手动保存

### 数据结构
```javascript
{
  version: "1.0.0",
  resources: {
    crystals: 0,
    energy: 100,
    maxEnergy: 100,
    upgradePoints: 0
  },
  upgrades: {
    miningEfficiency: { level: 1, maxLevel: 10 },
    energyCapacity: { level: 1, maxLevel: 10 },
    movementSpeed: { level: 1, maxLevel: 10 },
    autoMining: { level: 0, maxLevel: 5 }
  },
  progress: {
    currentPlanet: 0,
    unlockedPlanets: [0],
    achievements: []
  },
  settings: {
    sfxVolume: 70,
    musicVolume: 50,
    quality: "medium"
  }
}
```

## 🎨 视觉效果

### 粒子系统
- **爆炸效果**: 矿物被摧毁时的爆炸
- **挖掘火花**: 挖掘过程中的火花效果
- **收集光环**: 资源收集时的光环
- **推进尾焰**: 飞船推进时的尾焰
- **星尘效果**: 背景装饰粒子

### 动画效果
- 平滑的飞船移动
- 矿物旋转和脉冲动画
- 星球自转效果
- UI过渡动画

## 🔧 开发信息

### 技术栈
- **HTML5**: 游戏结构
- **CSS3**: 样式和动画
- **JavaScript ES6+**: 游戏逻辑
- **Canvas API**: 图形渲染
- **Web Audio API**: 音效系统
- **localStorage**: 数据存储

### 文件结构
```
星际矿工/
├── index.html          # 主页面
├── styles.css          # 样式文件
├── js/
│   ├── main.js         # 主程序
│   ├── gameLogic.js    # 游戏逻辑
│   ├── renderer.js     # 渲染引擎
│   ├── input.js        # 输入管理
│   ├── ui.js           # UI管理
│   ├── gameObjects.js  # 游戏对象
│   ├── particles.js    # 粒子系统
│   ├── storage.js      # 存储系统
│   └── utils.js        # 工具函数
└── README.md           # 说明文档
```

### 性能优化
- 对象池管理粒子
- 视锥剔除优化渲染
- 智能帧率控制
- 内存使用监控

## 🎵 音效系统

### 音效类型
- 挖掘音效：不同频率的蜂鸣音
- 收集音效：上升音调提示
- 升级音效：成功提示音
- 错误音效：低频警告音

### 音量控制
- 独立的音效和音乐音量控制
- 支持静音模式
- 音量设置持久化保存

## 🏆 成就系统（规划中）

### 挖掘成就
- 首次挖掘
- 收集1000水晶
- 发现传说矿物

### 探索成就
- 访问所有星球
- 飞行总距离
- 游戏总时长

### 升级成就
- 首次升级
- 解锁自动挖掘
- 达到最高等级

## 🐛 已知问题

- 在某些移动设备上可能存在性能问题
- 音效在iOS Safari上可能需要用户交互才能播放
- 极少数情况下localStorage可能失效

## 🔄 更新计划

### v1.1.0
- [ ] 添加背景音乐
- [ ] 实现成就系统
- [ ] 添加更多星球类型
- [ ] 优化移动端性能

### v1.2.0
- [ ] 多人模式支持
- [ ] 云存档功能
- [ ] 更多升级选项
- [ ] 任务系统

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

欢迎提交问题报告和功能请求！如果您想贡献代码，请：

1. Fork 本项目
2. 创建您的功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交您的更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开一个 Pull Request

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 项目地址：[GitHub Repository]
- 问题报告：[GitHub Issues]

---

**享受您的星际挖掘之旅！** 🚀✨
