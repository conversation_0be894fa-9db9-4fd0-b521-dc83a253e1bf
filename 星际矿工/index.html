<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>星际矿工 - Space Miner</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 游戏加载界面 -->
    <div id="loading-screen" class="screen">
        <div class="loading-content">
            <h1 class="game-title">星际矿工</h1>
            <div class="loading-bar">
                <div class="loading-progress"></div>
            </div>
            <p class="loading-text">正在初始化星际引擎...</p>
        </div>
    </div>

    <!-- 主菜单界面 -->
    <div id="main-menu" class="screen hidden">
        <div class="menu-content">
            <h1 class="game-title">星际矿工</h1>
            <div class="menu-buttons">
                <button id="new-game-btn" class="menu-btn primary">开始新游戏</button>
                <button id="continue-btn" class="menu-btn secondary">继续游戏</button>
                <button id="settings-btn" class="menu-btn secondary">设置</button>
            </div>
        </div>
        <div class="stars-bg"></div>
    </div>

    <!-- 游戏主界面 -->
    <div id="game-screen" class="screen hidden">
        <!-- 游戏画布 -->
        <canvas id="game-canvas"></canvas>
        
        <!-- 游戏UI界面 -->
        <div id="game-ui">
            <!-- 顶部状态栏 -->
            <div class="top-bar">
                <div class="resource-display">
                    <div class="resource-item">
                        <span class="resource-icon">💎</span>
                        <span id="crystals-count">0</span>
                    </div>
                    <div class="resource-item">
                        <span class="resource-icon">⚡</span>
                        <span id="energy-count">100</span>
                    </div>
                    <div class="resource-item">
                        <span class="resource-icon">🔧</span>
                        <span id="upgrade-points">0</span>
                    </div>
                </div>
                <div class="game-controls">
                    <button id="pause-btn" class="control-btn">⏸️</button>
                    <button id="menu-btn" class="control-btn">📋</button>
                </div>
            </div>

            <!-- 底部控制面板 -->
            <div class="bottom-panel">
                <div class="mining-controls">
                    <button id="auto-mine-btn" class="action-btn">
                        <span class="btn-icon">🤖</span>
                        <span class="btn-text">自动挖掘</span>
                    </button>
                    <button id="manual-mine-btn" class="action-btn active">
                        <span class="btn-icon">⛏️</span>
                        <span class="btn-text">手动挖掘</span>
                    </button>
                </div>
                <div class="ship-status">
                    <div class="status-item">
                        <span class="status-label">挖掘效率</span>
                        <div class="status-bar">
                            <div id="mining-efficiency" class="status-fill" style="width: 50%"></div>
                        </div>
                    </div>
                    <div class="status-item">
                        <span class="status-label">能量</span>
                        <div class="status-bar">
                            <div id="energy-bar" class="status-fill energy" style="width: 100%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 升级面板 -->
            <div id="upgrade-panel" class="panel hidden">
                <div class="panel-header">
                    <h3>飞船升级</h3>
                    <button class="close-btn">×</button>
                </div>
                <div class="panel-content">
                    <div class="upgrade-category">
                        <h4>挖掘系统</h4>
                        <div class="upgrade-item" data-upgrade="miningEfficiency">
                            <span class="upgrade-name">挖掘效率</span>
                            <span class="upgrade-level">等级 1</span>
                            <button class="upgrade-btn" data-upgrade="miningEfficiency">升级 (10💎)</button>
                        </div>
                        <div class="upgrade-item" data-upgrade="energyCapacity">
                            <span class="upgrade-name">能量容量</span>
                            <span class="upgrade-level">等级 1</span>
                            <button class="upgrade-btn" data-upgrade="energyCapacity">升级 (15💎)</button>
                        </div>
                    </div>
                    <div class="upgrade-category">
                        <h4>推进系统</h4>
                        <div class="upgrade-item" data-upgrade="movementSpeed">
                            <span class="upgrade-name">移动速度</span>
                            <span class="upgrade-level">等级 1</span>
                            <button class="upgrade-btn" data-upgrade="movementSpeed">升级 (20💎)</button>
                        </div>
                    </div>
                    <div class="upgrade-category">
                        <h4>自动化系统</h4>
                        <div class="upgrade-item" data-upgrade="autoMining">
                            <span class="upgrade-name">自动挖掘</span>
                            <span class="upgrade-level">等级 0</span>
                            <button class="upgrade-btn" data-upgrade="autoMining">升级 (50💎)</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 移动端虚拟摇杆 -->
        <div id="mobile-controls" class="mobile-only">
            <div id="joystick-container">
                <div id="joystick-base"></div>
                <div id="joystick-stick"></div>
            </div>
            <div class="mobile-action-buttons">
                <button id="mobile-mine-btn" class="mobile-btn">⛏️</button>
                <button id="mobile-boost-btn" class="mobile-btn">🚀</button>
            </div>
        </div>
    </div>

    <!-- 暂停菜单 -->
    <div id="pause-menu" class="overlay hidden">
        <div class="overlay-content">
            <h2>游戏暂停</h2>
            <div class="menu-buttons">
                <button id="resume-btn" class="menu-btn primary">继续游戏</button>
                <button id="save-btn" class="menu-btn secondary">保存游戏</button>
                <button id="main-menu-btn" class="menu-btn secondary">返回主菜单</button>
            </div>
        </div>
    </div>

    <!-- 设置面板 -->
    <div id="settings-panel" class="overlay hidden">
        <div class="overlay-content">
            <h2>游戏设置</h2>
            <div class="settings-group">
                <label>音效音量</label>
                <input type="range" id="sfx-volume" min="0" max="100" value="70">
            </div>
            <div class="settings-group">
                <label>背景音乐</label>
                <input type="range" id="music-volume" min="0" max="100" value="50">
            </div>
            <div class="settings-group">
                <label>画质设置</label>
                <select id="quality-setting">
                    <option value="low">低</option>
                    <option value="medium" selected>中</option>
                    <option value="high">高</option>
                </select>
            </div>
            <div class="menu-buttons">
                <button id="settings-close-btn" class="menu-btn primary">确定</button>
            </div>
        </div>
    </div>

    <!-- 游戏脚本 -->
    <script src="js/utils.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/particles.js"></script>
    <script src="js/gameObjects.js"></script>
    <script src="js/renderer.js"></script>
    <script src="js/input.js"></script>
    <script src="js/gameLogic.js"></script>
    <script src="js/ui.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
