/* 全局样式重置和基础设置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Orbitron', monospace;
    background: #000;
    color: #fff;
    overflow: hidden;
    user-select: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none;
}

/* 屏幕容器基础样式 */
.screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
}

.hidden {
    display: none !important;
}

/* 加载界面样式 */
#loading-screen {
    background: radial-gradient(circle at center, #1a1a2e 0%, #0c0c0c 100%);
}

.loading-content {
    text-align: center;
    animation: fadeIn 1s ease-in;
}

.game-title {
    font-size: 3rem;
    font-weight: 900;
    background: linear-gradient(45deg, #00d4ff, #ff6b6b, #4ecdc4);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
    margin-bottom: 2rem;
    text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
}

.loading-bar {
    width: 300px;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
    margin: 2rem auto;
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, #00d4ff, #4ecdc4);
    border-radius: 2px;
    animation: loadingProgress 3s ease-in-out;
}

.loading-text {
    font-size: 1rem;
    opacity: 0.8;
    animation: pulse 2s ease-in-out infinite;
}

/* 主菜单样式 */
#main-menu {
    position: relative;
}

.stars-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(2px 2px at 20px 30px, #eee, transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
        radial-gradient(1px 1px at 90px 40px, #fff, transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: starMove 20s linear infinite;
    z-index: -1;
}

.menu-content {
    text-align: center;
    z-index: 1;
}

.menu-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 3rem;
}

.menu-btn {
    padding: 1rem 2rem;
    font-family: 'Orbitron', monospace;
    font-size: 1.1rem;
    font-weight: 700;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    min-width: 250px;
}

.menu-btn.primary {
    background: linear-gradient(45deg, #00d4ff, #4ecdc4);
    color: #000;
    box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
}

.menu-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 212, 255, 0.5);
}

.menu-btn.secondary {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.menu-btn.secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

/* 游戏界面样式 */
#game-screen {
    padding: 0;
    background: #000;
}

#game-canvas {
    display: block;
    width: 100vw;
    height: 100vh;
    background: radial-gradient(circle at 30% 20%, #1a1a2e 0%, #0c0c0c 100%);
}

/* 游戏UI样式 */
#game-ui {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10;
}

#game-ui > * {
    pointer-events: auto;
}

/* 顶部状态栏 */
.top-bar {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 60px;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.8) 0%, transparent 100%);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
}

.resource-display {
    display: flex;
    gap: 20px;
}

.resource-item {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 700;
    font-size: 1rem;
}

.resource-icon {
    font-size: 1.2rem;
}

.game-controls {
    display: flex;
    gap: 10px;
}

.control-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

/* 底部控制面板 */
.bottom-panel {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.9) 0%, transparent 100%);
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.mining-controls {
    display: flex;
    gap: 15px;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    padding: 12px 20px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: #fff;
    font-family: 'Orbitron', monospace;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
}

.action-btn.active {
    background: linear-gradient(45deg, #00d4ff, #4ecdc4);
    color: #000;
    border-color: transparent;
}

.btn-icon {
    font-size: 1.5rem;
}

.btn-text {
    font-size: 0.8rem;
    font-weight: 600;
}

/* 飞船状态显示 */
.ship-status {
    display: flex;
    flex-direction: column;
    gap: 10px;
    min-width: 200px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.status-label {
    font-size: 0.9rem;
    min-width: 80px;
}

.status-bar {
    flex: 1;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
}

.status-fill {
    height: 100%;
    background: linear-gradient(90deg, #00d4ff, #4ecdc4);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.status-fill.energy {
    background: linear-gradient(90deg, #ff6b6b, #ffa500);
}

/* 移动端适配 */
.mobile-only {
    display: none;
}

@media (max-width: 768px) {
    .mobile-only {
        display: block;
    }
    
    .game-title {
        font-size: 2rem;
    }
    
    .menu-btn {
        min-width: 200px;
        font-size: 1rem;
    }
    
    .resource-display {
        gap: 10px;
    }
    
    .resource-item {
        padding: 6px 12px;
        font-size: 0.9rem;
    }
    
    .bottom-panel {
        flex-direction: column;
        gap: 15px;
        padding: 15px;
    }
    
    .ship-status {
        width: 100%;
    }
}

/* 动画定义 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes loadingProgress {
    0% { width: 0%; }
    100% { width: 100%; }
}

@keyframes pulse {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
}

@keyframes starMove {
    0% { transform: translateY(0); }
    100% { transform: translateY(-100px); }
}

/* 覆盖层和面板样式 */
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.overlay-content {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    padding: 2rem;
    border-radius: 15px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    text-align: center;
    max-width: 400px;
    width: 90%;
}

.panel {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 20px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    z-index: 100;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.close-btn {
    background: none;
    border: none;
    color: #fff;
    font-size: 1.5rem;
    cursor: pointer;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* 升级面板样式 */
.upgrade-category {
    margin-bottom: 20px;
}

.upgrade-category h4 {
    color: #00d4ff;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.upgrade-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    margin-bottom: 8px;
}

.upgrade-name {
    font-weight: 600;
}

.upgrade-level {
    color: #4ecdc4;
    font-size: 0.9rem;
}

.upgrade-btn {
    background: linear-gradient(45deg, #00d4ff, #4ecdc4);
    color: #000;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upgrade-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 212, 255, 0.3);
}

.upgrade-btn:disabled {
    background: rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.5);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 设置面板样式 */
.settings-group {
    margin-bottom: 20px;
    text-align: left;
}

.settings-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #4ecdc4;
}

.settings-group input[type="range"] {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: rgba(255, 255, 255, 0.2);
    outline: none;
    -webkit-appearance: none;
}

.settings-group input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: linear-gradient(45deg, #00d4ff, #4ecdc4);
    cursor: pointer;
}

.settings-group input[type="range"]::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: linear-gradient(45deg, #00d4ff, #4ecdc4);
    cursor: pointer;
    border: none;
}

.settings-group select {
    width: 100%;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    color: #fff;
    font-family: 'Orbitron', monospace;
}

.settings-group select option {
    background: #1a1a2e;
    color: #fff;
}

/* 移动端虚拟摇杆 */
#mobile-controls {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    pointer-events: none;
}

#mobile-controls > * {
    pointer-events: auto;
}

#joystick-container {
    position: relative;
    width: 120px;
    height: 120px;
}

#joystick-base {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

#joystick-stick {
    position: absolute;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(45deg, #00d4ff, #4ecdc4);
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.1s ease;
}

.mobile-action-buttons {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.mobile-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: #fff;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.mobile-btn:active {
    background: linear-gradient(45deg, #00d4ff, #4ecdc4);
    transform: scale(0.95);
}
