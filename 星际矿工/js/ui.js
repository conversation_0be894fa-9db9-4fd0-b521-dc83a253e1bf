/**
 * UI管理系统
 * 处理游戏界面的显示和交互
 */

class UIManager {
    constructor() {
        this.elements = {};
        this.currentScreen = 'loading';
        this.updateInterval = null;
        
        this.init();
    }

    /**
     * 初始化UI管理器
     */
    init() {
        this.cacheElements();
        this.setupEventListeners();
        this.startUIUpdate();
    }

    /**
     * 缓存DOM元素
     */
    cacheElements() {
        // 屏幕元素
        this.elements.loadingScreen = DOMUtils.$('#loading-screen');
        this.elements.mainMenu = DOMUtils.$('#main-menu');
        this.elements.gameScreen = DOMUtils.$('#game-screen');
        this.elements.pauseMenu = DOMUtils.$('#pause-menu');
        this.elements.settingsPanel = DOMUtils.$('#settings-panel');
        this.elements.upgradePanel = DOMUtils.$('#upgrade-panel');

        // 按钮元素
        this.elements.newGameBtn = DOMUtils.$('#new-game-btn');
        this.elements.continueBtn = DOMUtils.$('#continue-btn');
        this.elements.settingsBtn = DOMUtils.$('#settings-btn');
        this.elements.pauseBtn = DOMUtils.$('#pause-btn');
        this.elements.menuBtn = DOMUtils.$('#menu-btn');
        this.elements.resumeBtn = DOMUtils.$('#resume-btn');
        this.elements.saveBtn = DOMUtils.$('#save-btn');
        this.elements.mainMenuBtn = DOMUtils.$('#main-menu-btn');
        this.elements.settingsCloseBtn = DOMUtils.$('#settings-close-btn');

        // 资源显示元素
        this.elements.crystalsCount = DOMUtils.$('#crystals-count');
        this.elements.energyCount = DOMUtils.$('#energy-count');
        this.elements.upgradePoints = DOMUtils.$('#upgrade-points');

        // 状态栏元素
        this.elements.miningEfficiency = DOMUtils.$('#mining-efficiency');
        this.elements.energyBar = DOMUtils.$('#energy-bar');

        // 控制按钮
        this.elements.autoMineBtn = DOMUtils.$('#auto-mine-btn');
        this.elements.manualMineBtn = DOMUtils.$('#manual-mine-btn');

        // 设置元素
        this.elements.sfxVolume = DOMUtils.$('#sfx-volume');
        this.elements.musicVolume = DOMUtils.$('#music-volume');
        this.elements.qualitySetting = DOMUtils.$('#quality-setting');
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 主菜单按钮
        if (this.elements.newGameBtn) {
            DOMUtils.on(this.elements.newGameBtn, 'click', () => this.startNewGame());
        }
        
        if (this.elements.continueBtn) {
            DOMUtils.on(this.elements.continueBtn, 'click', () => this.continueGame());
        }
        
        if (this.elements.settingsBtn) {
            DOMUtils.on(this.elements.settingsBtn, 'click', () => this.showSettings());
        }

        // 游戏控制按钮
        if (this.elements.pauseBtn) {
            DOMUtils.on(this.elements.pauseBtn, 'click', () => this.pauseGame());
        }
        
        if (this.elements.menuBtn) {
            DOMUtils.on(this.elements.menuBtn, 'click', () => this.showUpgradePanel());
        }

        // 暂停菜单按钮
        if (this.elements.resumeBtn) {
            DOMUtils.on(this.elements.resumeBtn, 'click', () => this.resumeGame());
        }
        
        if (this.elements.saveBtn) {
            DOMUtils.on(this.elements.saveBtn, 'click', () => this.saveGame());
        }
        
        if (this.elements.mainMenuBtn) {
            DOMUtils.on(this.elements.mainMenuBtn, 'click', () => this.returnToMainMenu());
        }

        // 设置面板
        if (this.elements.settingsCloseBtn) {
            DOMUtils.on(this.elements.settingsCloseBtn, 'click', () => this.hideSettings());
        }

        // 挖掘模式切换
        if (this.elements.autoMineBtn) {
            DOMUtils.on(this.elements.autoMineBtn, 'click', () => this.toggleMiningMode('auto'));
        }
        
        if (this.elements.manualMineBtn) {
            DOMUtils.on(this.elements.manualMineBtn, 'click', () => this.toggleMiningMode('manual'));
        }

        // 设置变化监听
        if (this.elements.sfxVolume) {
            DOMUtils.on(this.elements.sfxVolume, 'input', (e) => this.updateSetting('sfxVolume', e.target.value));
        }
        
        if (this.elements.musicVolume) {
            DOMUtils.on(this.elements.musicVolume, 'input', (e) => this.updateSetting('musicVolume', e.target.value));
        }
        
        if (this.elements.qualitySetting) {
            DOMUtils.on(this.elements.qualitySetting, 'change', (e) => this.updateSetting('quality', e.target.value));
        }

        // 升级面板事件
        this.setupUpgradePanel();
    }

    /**
     * 设置升级面板事件
     */
    setupUpgradePanel() {
        const upgradePanel = this.elements.upgradePanel;
        if (!upgradePanel) return;

        // 关闭按钮
        const closeBtn = upgradePanel.querySelector('.close-btn');
        if (closeBtn) {
            DOMUtils.on(closeBtn, 'click', () => this.hideUpgradePanel());
        }

        // 升级按钮
        const upgradeButtons = upgradePanel.querySelectorAll('.upgrade-btn');
        upgradeButtons.forEach(btn => {
            DOMUtils.on(btn, 'click', (e) => {
                const upgradeType = e.target.dataset.upgrade;
                if (upgradeType) {
                    this.purchaseUpgrade(upgradeType);
                }
            });
        });
    }

    /**
     * 显示指定屏幕
     * @param {string} screenName - 屏幕名称
     */
    showScreen(screenName) {
        // 隐藏所有屏幕
        Object.values(this.elements).forEach(element => {
            if (element && element.classList.contains('screen')) {
                DOMUtils.toggle(element, false);
            }
        });

        // 显示指定屏幕
        const screen = this.elements[screenName + 'Screen'] || this.elements[screenName];
        if (screen) {
            DOMUtils.toggle(screen, true);
            this.currentScreen = screenName;
        }
    }

    /**
     * 显示加载界面
     */
    showLoading() {
        this.showScreen('loading');
        
        // 模拟加载过程
        setTimeout(() => {
            this.showMainMenu();
        }, 3000);
    }

    /**
     * 显示主菜单
     */
    showMainMenu() {
        this.showScreen('mainMenu');
        
        // 检查是否有保存的游戏
        const hasSavedGame = gameStorage.hasSavedGame();
        if (this.elements.continueBtn) {
            this.elements.continueBtn.style.display = hasSavedGame ? 'block' : 'none';
        }
    }

    /**
     * 开始新游戏
     */
    startNewGame() {
        gameLogic.startNewGame();
        this.showScreen('game');
        this.updateMiningModeButtons();
    }

    /**
     * 继续游戏
     */
    continueGame() {
        gameLogic.continueGame();
        this.showScreen('game');
        this.updateMiningModeButtons();
    }

    /**
     * 暂停游戏
     */
    pauseGame() {
        gameLogic.pauseGame();
        DOMUtils.toggle(this.elements.pauseMenu, true);
    }

    /**
     * 恢复游戏
     */
    resumeGame() {
        gameLogic.resumeGame();
        DOMUtils.toggle(this.elements.pauseMenu, false);
    }

    /**
     * 保存游戏
     */
    saveGame() {
        const success = gameLogic.saveGame();
        
        // 显示保存结果提示
        if (success) {
            this.showNotification('游戏保存成功！', 'success');
        } else {
            this.showNotification('游戏保存失败！', 'error');
        }
    }

    /**
     * 返回主菜单
     */
    returnToMainMenu() {
        // 保存游戏
        gameLogic.saveGame();
        
        // 停止自动保存
        gameStorage.stopAutoSave();
        
        // 返回主菜单
        gameLogic.setGameState('menu');
        DOMUtils.toggle(this.elements.pauseMenu, false);
        this.showMainMenu();
    }

    /**
     * 显示设置面板
     */
    showSettings() {
        DOMUtils.toggle(this.elements.settingsPanel, true);
        this.loadSettings();
    }

    /**
     * 隐藏设置面板
     */
    hideSettings() {
        DOMUtils.toggle(this.elements.settingsPanel, false);
        this.saveSettings();
    }

    /**
     * 显示升级面板
     */
    showUpgradePanel() {
        DOMUtils.toggle(this.elements.upgradePanel, true);
        this.updateUpgradePanel();
    }

    /**
     * 隐藏升级面板
     */
    hideUpgradePanel() {
        DOMUtils.toggle(this.elements.upgradePanel, false);
    }

    /**
     * 切换挖掘模式
     * @param {string} mode - 挖掘模式 ('auto' 或 'manual')
     */
    toggleMiningMode(mode) {
        // 更新按钮状态
        if (this.elements.autoMineBtn && this.elements.manualMineBtn) {
            this.elements.autoMineBtn.classList.toggle('active', mode === 'auto');
            this.elements.manualMineBtn.classList.toggle('active', mode === 'manual');
        }
        
        // 更新飞船状态
        if (gameLogic.spaceship) {
            gameLogic.spaceship.autoMining = (mode === 'auto') && 
                (gameLogic.gameData.upgrades.autoMining.level > 0);
        }
    }

    /**
     * 更新挖掘模式按钮
     */
    updateMiningModeButtons() {
        const hasAutoMining = gameLogic.gameData.upgrades.autoMining.level > 0;
        
        if (this.elements.autoMineBtn) {
            this.elements.autoMineBtn.style.display = hasAutoMining ? 'flex' : 'none';
        }
    }

    /**
     * 购买升级
     * @param {string} upgradeType - 升级类型
     */
    purchaseUpgrade(upgradeType) {
        const success = gameLogic.purchaseUpgrade(upgradeType);
        
        if (success) {
            this.updateUpgradePanel();
            this.updateMiningModeButtons();
            this.showNotification(`${upgradeType} 升级成功！`, 'success');
            
            // 播放升级音效
            AudioUtils.playBeep(600, 300, 0.1);
        } else {
            this.showNotification('资源不足或已达最大等级！', 'error');
            
            // 播放错误音效
            AudioUtils.playBeep(200, 200, 0.1);
        }
    }

    /**
     * 更新升级面板
     */
    updateUpgradePanel() {
        const gameData = gameLogic.getGameData();
        const upgrades = gameData.upgrades;
        
        Object.keys(upgrades).forEach(upgradeType => {
            const upgrade = upgrades[upgradeType];
            const cost = gameLogic.getUpgradeCost(upgradeType);
            
            // 更新等级显示
            const levelElement = DOMUtils.$(`.upgrade-item[data-upgrade="${upgradeType}"] .upgrade-level`);
            if (levelElement) {
                levelElement.textContent = `等级 ${upgrade.level}`;
            }
            
            // 更新升级按钮
            const buttonElement = DOMUtils.$(`.upgrade-item[data-upgrade="${upgradeType}"] .upgrade-btn`);
            if (buttonElement) {
                if (cost === -1) {
                    buttonElement.textContent = '已满级';
                    buttonElement.disabled = true;
                } else {
                    buttonElement.textContent = `升级 (${cost}💎)`;
                    buttonElement.disabled = gameData.resources.crystals < cost;
                }
            }
        });
    }

    /**
     * 加载设置
     */
    loadSettings() {
        const settings = gameLogic.getGameData().settings;
        
        if (this.elements.sfxVolume) {
            this.elements.sfxVolume.value = settings.sfxVolume;
        }
        
        if (this.elements.musicVolume) {
            this.elements.musicVolume.value = settings.musicVolume;
        }
        
        if (this.elements.qualitySetting) {
            this.elements.qualitySetting.value = settings.quality;
        }
    }

    /**
     * 保存设置
     */
    saveSettings() {
        const gameData = gameLogic.getGameData();
        
        if (this.elements.sfxVolume) {
            gameData.settings.sfxVolume = parseInt(this.elements.sfxVolume.value);
        }
        
        if (this.elements.musicVolume) {
            gameData.settings.musicVolume = parseInt(this.elements.musicVolume.value);
        }
        
        if (this.elements.qualitySetting) {
            gameData.settings.quality = this.elements.qualitySetting.value;
            
            // 应用画质设置
            if (gameRenderer) {
                gameRenderer.setQuality(gameData.settings.quality);
            }
        }
    }

    /**
     * 更新设置
     * @param {string} setting - 设置名称
     * @param {*} value - 设置值
     */
    updateSetting(setting, value) {
        const gameData = gameLogic.getGameData();
        
        if (setting === 'quality') {
            gameData.settings.quality = value;
            if (gameRenderer) {
                gameRenderer.setQuality(value);
            }
        } else {
            gameData.settings[setting] = parseInt(value);
        }
    }

    /**
     * 开始UI更新循环
     */
    startUIUpdate() {
        this.updateInterval = setInterval(() => {
            this.updateGameUI();
        }, 100); // 每100ms更新一次UI
    }

    /**
     * 更新游戏UI
     */
    updateGameUI() {
        if (gameLogic.getGameState() !== 'playing') return;
        
        const gameData = gameLogic.getGameData();
        const spaceship = gameLogic.spaceship;
        
        // 更新资源显示
        if (this.elements.crystalsCount) {
            this.elements.crystalsCount.textContent = gameData.resources.crystals;
        }
        
        if (this.elements.energyCount) {
            this.elements.energyCount.textContent = Math.floor(spaceship.energy);
        }
        
        if (this.elements.upgradePoints) {
            this.elements.upgradePoints.textContent = gameData.resources.upgradePoints;
        }
        
        // 更新状态栏
        if (this.elements.miningEfficiency) {
            const efficiency = (spaceship.miningPower / 5) * 100; // 假设最大效率为5
            this.elements.miningEfficiency.style.width = Math.min(100, efficiency) + '%';
        }
        
        if (this.elements.energyBar) {
            const energyPercent = (spaceship.energy / spaceship.maxEnergy) * 100;
            this.elements.energyBar.style.width = energyPercent + '%';
        }
    }

    /**
     * 显示通知
     * @param {string} message - 通知消息
     * @param {string} type - 通知类型 ('success', 'error', 'info')
     */
    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 8px;
            color: white;
            font-family: 'Orbitron', monospace;
            font-weight: 600;
            z-index: 10000;
            animation: slideIn 0.3s ease-out;
            background: ${type === 'success' ? '#4ecdc4' : type === 'error' ? '#ff6b6b' : '#00d4ff'};
        `;
        
        document.body.appendChild(notification);
        
        // 3秒后自动移除
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    /**
     * 停止UI更新
     */
    stopUIUpdate() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }
}

// 创建全局UI管理器实例
const uiManager = new UIManager();
