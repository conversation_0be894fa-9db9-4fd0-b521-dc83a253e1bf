/**
 * 游戏渲染系统
 * 负责管理Canvas渲染、背景效果、UI绘制等
 */

class GameRenderer {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.width = 0;
        this.height = 0;
        
        // 渲染设置
        this.quality = 'medium';
        this.pixelRatio = window.devicePixelRatio || 1;
        
        // 背景效果
        this.stars = [];
        this.nebula = [];
        
        // 相机系统
        this.camera = {
            x: 0,
            y: 0,
            zoom: 1,
            targetX: 0,
            targetY: 0,
            smoothing: 0.1
        };
        
        // 后处理效果
        this.effects = {
            bloom: false,
            scanlines: false,
            chromatic: false
        };
        
        this.init();
    }

    /**
     * 初始化渲染器
     */
    init() {
        this.resize();
        this.generateStars();
        this.generateNebula();
        
        // 监听窗口大小变化
        window.addEventListener('resize', () => this.resize());
    }

    /**
     * 调整画布大小
     */
    resize() {
        const rect = this.canvas.getBoundingClientRect();
        this.width = rect.width;
        this.height = rect.height;
        
        // 设置画布实际大小
        this.canvas.width = this.width * this.pixelRatio;
        this.canvas.height = this.height * this.pixelRatio;
        
        // 设置画布显示大小
        this.canvas.style.width = this.width + 'px';
        this.canvas.style.height = this.height + 'px';
        
        // 缩放上下文以匹配设备像素比
        this.ctx.scale(this.pixelRatio, this.pixelRatio);
        
        // 重新生成背景元素
        this.generateStars();
        this.generateNebula();
    }

    /**
     * 生成星空背景
     */
    generateStars() {
        this.stars = [];
        const starCount = this.quality === 'high' ? 200 : this.quality === 'medium' ? 100 : 50;
        
        for (let i = 0; i < starCount; i++) {
            this.stars.push({
                x: MathUtils.random(0, this.width),
                y: MathUtils.random(0, this.height),
                size: MathUtils.random(0.5, 2),
                brightness: MathUtils.random(0.3, 1),
                twinkleSpeed: MathUtils.random(0.5, 2),
                twinklePhase: MathUtils.random(0, Math.PI * 2),
                color: this.getStarColor()
            });
        }
    }

    /**
     * 获取星星颜色
     * @returns {string} 星星颜色
     */
    getStarColor() {
        const colors = ['#ffffff', '#ffffcc', '#ccccff', '#ffcccc', '#ccffcc'];
        return colors[Math.floor(Math.random() * colors.length)];
    }

    /**
     * 生成星云背景
     */
    generateNebula() {
        this.nebula = [];
        const nebulaCount = this.quality === 'high' ? 5 : this.quality === 'medium' ? 3 : 1;
        
        for (let i = 0; i < nebulaCount; i++) {
            this.nebula.push({
                x: MathUtils.random(-200, this.width + 200),
                y: MathUtils.random(-200, this.height + 200),
                radius: MathUtils.random(100, 300),
                color: this.getNebulaColor(),
                alpha: MathUtils.random(0.1, 0.3),
                drift: {
                    x: MathUtils.random(-10, 10),
                    y: MathUtils.random(-10, 10)
                }
            });
        }
    }

    /**
     * 获取星云颜色
     * @returns {string} 星云颜色
     */
    getNebulaColor() {
        const colors = ['#ff6b6b', '#4ecdc4', '#9b59b6', '#f39c12', '#3498db'];
        return colors[Math.floor(Math.random() * colors.length)];
    }

    /**
     * 更新相机位置
     * @param {number} targetX - 目标X坐标
     * @param {number} targetY - 目标Y坐标
     * @param {number} deltaTime - 时间增量
     */
    updateCamera(targetX, targetY, deltaTime) {
        // 平滑跟随目标
        this.camera.targetX = targetX - this.width / 2;
        this.camera.targetY = targetY - this.height / 2;
        
        this.camera.x += (this.camera.targetX - this.camera.x) * this.camera.smoothing;
        this.camera.y += (this.camera.targetY - this.camera.y) * this.camera.smoothing;
    }

    /**
     * 开始渲染帧
     */
    beginFrame() {
        // 清除画布
        this.ctx.clearRect(0, 0, this.width, this.height);
        
        // 保存上下文状态
        this.ctx.save();
        
        // 应用相机变换
        this.ctx.translate(-this.camera.x, -this.camera.y);
        this.ctx.scale(this.camera.zoom, this.camera.zoom);
    }

    /**
     * 结束渲染帧
     */
    endFrame() {
        // 恢复上下文状态
        this.ctx.restore();
        
        // 应用后处理效果
        this.applyPostEffects();
    }

    /**
     * 渲染背景
     * @param {number} deltaTime - 时间增量
     */
    renderBackground(deltaTime) {
        // 渲染星云
        this.renderNebula(deltaTime);
        
        // 渲染星星
        this.renderStars(deltaTime);
    }

    /**
     * 渲染星云
     * @param {number} deltaTime - 时间增量
     */
    renderNebula(deltaTime) {
        this.nebula.forEach(nebula => {
            // 更新星云位置
            nebula.x += nebula.drift.x * deltaTime;
            nebula.y += nebula.drift.y * deltaTime;
            
            // 边界检查
            if (nebula.x < -nebula.radius) nebula.x = this.width + nebula.radius;
            if (nebula.x > this.width + nebula.radius) nebula.x = -nebula.radius;
            if (nebula.y < -nebula.radius) nebula.y = this.height + nebula.radius;
            if (nebula.y > this.height + nebula.radius) nebula.y = -nebula.radius;
            
            // 渲染星云
            const gradient = this.ctx.createRadialGradient(
                nebula.x, nebula.y, 0,
                nebula.x, nebula.y, nebula.radius
            );
            gradient.addColorStop(0, nebula.color + Math.floor(nebula.alpha * 255).toString(16).padStart(2, '0'));
            gradient.addColorStop(1, nebula.color + '00');
            
            this.ctx.fillStyle = gradient;
            this.ctx.beginPath();
            this.ctx.arc(nebula.x, nebula.y, nebula.radius, 0, Math.PI * 2);
            this.ctx.fill();
        });
    }

    /**
     * 渲染星星
     * @param {number} deltaTime - 时间增量
     */
    renderStars(deltaTime) {
        this.stars.forEach(star => {
            // 更新闪烁效果
            star.twinklePhase += star.twinkleSpeed * deltaTime;
            const twinkle = Math.sin(star.twinklePhase) * 0.3 + 0.7;
            
            // 渲染星星
            this.ctx.save();
            this.ctx.globalAlpha = star.brightness * twinkle;
            this.ctx.fillStyle = star.color;
            this.ctx.beginPath();
            this.ctx.arc(star.x, star.y, star.size, 0, Math.PI * 2);
            this.ctx.fill();
            
            // 添加星星光晕效果
            if (star.size > 1) {
                this.ctx.globalAlpha = star.brightness * twinkle * 0.3;
                this.ctx.beginPath();
                this.ctx.arc(star.x, star.y, star.size * 2, 0, Math.PI * 2);
                this.ctx.fill();
            }
            
            this.ctx.restore();
        });
    }

    /**
     * 渲染网格（调试用）
     */
    renderGrid() {
        if (this.quality === 'low') return;
        
        this.ctx.save();
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
        this.ctx.lineWidth = 1;
        
        const gridSize = 100;
        const startX = Math.floor(this.camera.x / gridSize) * gridSize;
        const startY = Math.floor(this.camera.y / gridSize) * gridSize;
        const endX = startX + this.width + gridSize;
        const endY = startY + this.height + gridSize;
        
        // 绘制垂直线
        for (let x = startX; x <= endX; x += gridSize) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, startY);
            this.ctx.lineTo(x, endY);
            this.ctx.stroke();
        }
        
        // 绘制水平线
        for (let y = startY; y <= endY; y += gridSize) {
            this.ctx.beginPath();
            this.ctx.moveTo(startX, y);
            this.ctx.lineTo(endX, y);
            this.ctx.stroke();
        }
        
        this.ctx.restore();
    }

    /**
     * 应用后处理效果
     */
    applyPostEffects() {
        if (this.quality === 'low') return;
        
        // 扫描线效果
        if (this.effects.scanlines) {
            this.applyScanlines();
        }
        
        // 色差效果
        if (this.effects.chromatic) {
            this.applyChromaticAberration();
        }
    }

    /**
     * 应用扫描线效果
     */
    applyScanlines() {
        this.ctx.save();
        this.ctx.globalAlpha = 0.1;
        this.ctx.fillStyle = '#000000';
        
        for (let y = 0; y < this.height; y += 4) {
            this.ctx.fillRect(0, y, this.width, 2);
        }
        
        this.ctx.restore();
    }

    /**
     * 应用色差效果
     */
    applyChromaticAberration() {
        // 简化版色差效果
        const imageData = this.ctx.getImageData(0, 0, this.width, this.height);
        const data = imageData.data;
        
        // 这里可以实现更复杂的色差效果
        // 由于性能考虑，暂时简化处理
        
        this.ctx.putImageData(imageData, 0, 0);
    }

    /**
     * 设置渲染质量
     * @param {string} quality - 质量等级 (low/medium/high)
     */
    setQuality(quality) {
        this.quality = quality;
        this.generateStars();
        this.generateNebula();
    }

    /**
     * 获取世界坐标到屏幕坐标的转换
     * @param {number} worldX - 世界X坐标
     * @param {number} worldY - 世界Y坐标
     * @returns {Object} 屏幕坐标
     */
    worldToScreen(worldX, worldY) {
        return {
            x: (worldX - this.camera.x) * this.camera.zoom,
            y: (worldY - this.camera.y) * this.camera.zoom
        };
    }

    /**
     * 获取屏幕坐标到世界坐标的转换
     * @param {number} screenX - 屏幕X坐标
     * @param {number} screenY - 屏幕Y坐标
     * @returns {Object} 世界坐标
     */
    screenToWorld(screenX, screenY) {
        return {
            x: screenX / this.camera.zoom + this.camera.x,
            y: screenY / this.camera.zoom + this.camera.y
        };
    }

    /**
     * 检查对象是否在视野内
     * @param {GameObject} object - 游戏对象
     * @returns {boolean} 是否在视野内
     */
    isInView(object) {
        const margin = 100; // 视野边界扩展
        return object.x + object.width >= this.camera.x - margin &&
               object.x <= this.camera.x + this.width + margin &&
               object.y + object.height >= this.camera.y - margin &&
               object.y <= this.camera.y + this.height + margin;
    }
}

// 创建全局渲染器实例（将在main.js中初始化）
let gameRenderer = null;
