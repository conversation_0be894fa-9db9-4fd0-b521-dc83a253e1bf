/**
 * 游戏核心逻辑
 * 管理游戏状态、资源系统、升级系统等
 */

class GameLogic {
    constructor() {
        // 游戏状态
        this.gameState = 'loading'; // loading, menu, playing, paused, gameover
        this.gameData = null;
        
        // 游戏对象
        this.spaceship = null;
        this.planets = [];
        this.currentPlanet = null;
        this.minerals = [];
        
        // 游戏世界
        this.worldSize = {
            width: 2000,
            height: 2000
        };
        
        // 时间管理
        this.gameTime = 0;
        this.lastUpdateTime = 0;
        
        // 性能统计
        this.fps = 0;
        this.frameCount = 0;
        this.lastFpsUpdate = 0;
        
        this.init();
    }

    /**
     * 初始化游戏逻辑
     */
    init() {
        // 加载游戏数据
        this.loadGameData();
        
        // 初始化游戏对象
        this.initializeGameObjects();
        
        // 设置事件监听
        this.setupEventListeners();
    }

    /**
     * 加载游戏数据
     */
    loadGameData() {
        this.gameData = gameStorage.loadGame();
        console.log('游戏数据已加载:', this.gameData);
    }

    /**
     * 初始化游戏对象
     */
    initializeGameObjects() {
        // 创建玩家飞船
        this.spaceship = new Spaceship(
            this.worldSize.width / 2,
            this.worldSize.height / 2
        );
        
        // 应用升级数据到飞船
        this.applyUpgradesToSpaceship();
        
        // 生成星球
        this.generatePlanets();
        
        // 设置当前星球
        this.setCurrentPlanet(this.gameData.progress.currentPlanet);
    }

    /**
     * 应用升级数据到飞船
     */
    applyUpgradesToSpaceship() {
        const upgrades = this.gameData.upgrades;
        
        // 挖掘效率升级
        this.spaceship.miningPower = 1 + (upgrades.miningEfficiency.level - 1) * 0.5;
        
        // 能量容量升级
        this.spaceship.maxEnergy = 100 + (upgrades.energyCapacity.level - 1) * 20;
        this.spaceship.energy = Math.min(this.spaceship.energy, this.spaceship.maxEnergy);
        
        // 移动速度升级
        this.spaceship.speed = 200 + (upgrades.movementSpeed.level - 1) * 30;
        
        // 自动挖掘升级
        this.spaceship.autoMining = upgrades.autoMining.level > 0;
        this.spaceship.autoMiningEfficiency = upgrades.autoMining.level * 0.3;
    }

    /**
     * 生成星球
     */
    generatePlanets() {
        this.planets = [];
        const planetCount = 5;
        const planetTypes = ['rocky', 'ice', 'gas', 'volcanic', 'crystal'];
        
        for (let i = 0; i < planetCount; i++) {
            const angle = (Math.PI * 2 * i) / planetCount;
            const distance = 600 + i * 200;
            
            const x = this.worldSize.width / 2 + Math.cos(angle) * distance;
            const y = this.worldSize.height / 2 + Math.sin(angle) * distance;
            
            const planet = new Planet(x, y, planetTypes[i]);
            this.planets.push(planet);
        }
    }

    /**
     * 设置当前星球
     * @param {number} planetIndex - 星球索引
     */
    setCurrentPlanet(planetIndex) {
        if (planetIndex >= 0 && planetIndex < this.planets.length) {
            this.currentPlanet = this.planets[planetIndex];
            this.minerals = [...this.currentPlanet.minerals];
            
            // 将飞船移动到星球附近
            const planet = this.currentPlanet;
            this.spaceship.x = planet.x + planet.radius + 100;
            this.spaceship.y = planet.y + planet.radius / 2;
        }
    }

    /**
     * 设置事件监听
     */
    setupEventListeners() {
        // 监听窗口失焦事件，自动暂停游戏
        window.addEventListener('blur', () => {
            if (this.gameState === 'playing') {
                this.pauseGame();
            }
        });
    }

    /**
     * 开始新游戏
     */
    startNewGame() {
        this.gameData = gameStorage.getDefaultGameData();
        this.gameState = 'playing';
        this.initializeGameObjects();
        
        // 开始自动保存
        gameStorage.startAutoSave(this.gameData);
        
        console.log('新游戏已开始');
    }

    /**
     * 继续游戏
     */
    continueGame() {
        this.gameState = 'playing';
        
        // 开始自动保存
        gameStorage.startAutoSave(this.gameData);
        
        console.log('游戏已继续');
    }

    /**
     * 暂停游戏
     */
    pauseGame() {
        if (this.gameState === 'playing') {
            this.gameState = 'paused';
            console.log('游戏已暂停');
        }
    }

    /**
     * 恢复游戏
     */
    resumeGame() {
        if (this.gameState === 'paused') {
            this.gameState = 'playing';
            console.log('游戏已恢复');
        }
    }

    /**
     * 保存游戏
     */
    saveGame() {
        // 更新统计数据
        this.updateStatistics();
        
        // 保存游戏数据
        const success = gameStorage.saveGame(this.gameData);
        
        if (success) {
            console.log('游戏保存成功');
            // 可以显示保存成功的提示
        } else {
            console.error('游戏保存失败');
            // 可以显示保存失败的提示
        }
        
        return success;
    }

    /**
     * 更新统计数据
     */
    updateStatistics() {
        this.gameData.statistics.totalPlayTime += this.gameTime;
        this.gameData.progress.lastSaveTime = Date.now();
    }

    /**
     * 更新游戏逻辑
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        if (this.gameState !== 'playing') return;
        
        // 更新游戏时间
        this.gameTime += deltaTime;
        
        // 更新输入
        inputManager.updateGameInput();
        const input = inputManager.gameInput;
        
        // 处理暂停输入
        if (input.pause) {
            this.pauseGame();
            return;
        }
        
        // 更新飞船
        this.spaceship.update(deltaTime, input);
        
        // 更新星球
        this.planets.forEach(planet => planet.update(deltaTime));
        
        // 更新挖掘逻辑
        this.updateMining(deltaTime);
        
        // 更新自动挖掘
        this.updateAutoMining(deltaTime);
        
        // 更新粒子系统
        particleSystem.update(deltaTime);
        
        // 检查碰撞
        this.checkCollisions();
        
        // 更新相机
        this.updateCamera(deltaTime);
        
        // 更新FPS统计
        this.updateFPS(deltaTime);
        
        // 清除单帧输入
        inputManager.clearFrameInput();
    }

    /**
     * 更新挖掘逻辑
     * @param {number} deltaTime - 时间增量
     */
    updateMining(deltaTime) {
        if (!this.spaceship.isMining) {
            this.spaceship.miningTarget = null;
            return;
        }
        
        // 寻找挖掘目标
        const target = this.spaceship.findMiningTarget(this.minerals);
        this.spaceship.miningTarget = target;
        
        if (target) {
            // 执行挖掘
            const damage = this.spaceship.miningPower * 20 * deltaTime;
            const destroyed = target.takeDamage(damage);
            
            // 创建挖掘粒子效果
            if (Math.random() < 0.3) {
                const targetCenter = target.getCenter();
                particleSystem.createMiningEffect(
                    targetCenter.x,
                    targetCenter.y,
                    target.color
                );
            }
            
            // 播放挖掘音效
            if (Math.random() < 0.1) {
                AudioUtils.playBeep(200 + Math.random() * 100, 100, 0.05);
            }
            
            if (destroyed) {
                this.collectMineral(target);
            }
        }
    }

    /**
     * 更新自动挖掘
     * @param {number} deltaTime - 时间增量
     */
    updateAutoMining(deltaTime) {
        if (!this.spaceship.autoMining || this.spaceship.isMining) return;
        
        // 自动寻找并挖掘最近的矿物
        const target = this.spaceship.findMiningTarget(this.minerals);
        
        if (target) {
            const damage = this.spaceship.autoMiningEfficiency * 10 * deltaTime;
            const destroyed = target.takeDamage(damage);
            
            if (destroyed) {
                this.collectMineral(target);
            }
        }
    }

    /**
     * 收集矿物
     * @param {Mineral} mineral - 被收集的矿物
     */
    collectMineral(mineral) {
        // 增加资源
        this.gameData.resources.crystals += mineral.value;
        this.gameData.statistics.totalCrystalsCollected += mineral.value;
        this.gameData.statistics.totalMiningOperations++;
        
        // 创建收集效果
        const center = mineral.getCenter();
        particleSystem.createCollectionEffect(center.x, center.y, mineral.color);
        
        // 播放收集音效
        AudioUtils.playBeep(400 + mineral.value * 50, 200, 0.1);
        
        // 从矿物列表中移除
        const index = this.minerals.indexOf(mineral);
        if (index > -1) {
            this.minerals.splice(index, 1);
        }
        
        // 从星球矿物列表中移除
        if (this.currentPlanet) {
            const planetIndex = this.currentPlanet.minerals.indexOf(mineral);
            if (planetIndex > -1) {
                this.currentPlanet.minerals.splice(planetIndex, 1);
            }
        }
        
        console.log(`收集了 ${mineral.type} 矿物，价值 ${mineral.value}`);
    }

    /**
     * 检查碰撞
     */
    checkCollisions() {
        // 检查飞船与星球的碰撞（简单的距离检查）
        this.planets.forEach(planet => {
            const shipCenter = this.spaceship.getCenter();
            const planetCenter = {
                x: planet.x + planet.radius,
                y: planet.y + planet.radius
            };
            
            const distance = MathUtils.distance(
                shipCenter.x, shipCenter.y,
                planetCenter.x, planetCenter.y
            );
            
            // 如果飞船太靠近星球表面，推开它
            const minDistance = planet.radius + 30;
            if (distance < minDistance) {
                const angle = MathUtils.angle(
                    planetCenter.x, planetCenter.y,
                    shipCenter.x, shipCenter.y
                );
                
                this.spaceship.x = planetCenter.x + Math.cos(angle) * minDistance - this.spaceship.width / 2;
                this.spaceship.y = planetCenter.y + Math.sin(angle) * minDistance - this.spaceship.height / 2;
            }
        });
    }

    /**
     * 更新相机
     * @param {number} deltaTime - 时间增量
     */
    updateCamera(deltaTime) {
        if (gameRenderer) {
            const shipCenter = this.spaceship.getCenter();
            gameRenderer.updateCamera(shipCenter.x, shipCenter.y, deltaTime);
        }
    }

    /**
     * 更新FPS统计
     * @param {number} deltaTime - 时间增量
     */
    updateFPS(deltaTime) {
        this.frameCount++;
        this.lastFpsUpdate += deltaTime;
        
        if (this.lastFpsUpdate >= 1) {
            this.fps = this.frameCount;
            this.frameCount = 0;
            this.lastFpsUpdate = 0;
        }
    }

    /**
     * 购买升级
     * @param {string} upgradeType - 升级类型
     * @returns {boolean} 是否购买成功
     */
    purchaseUpgrade(upgradeType) {
        const upgrade = this.gameData.upgrades[upgradeType];
        if (!upgrade) return false;
        
        // 检查是否已达到最大等级
        if (upgrade.level >= upgrade.maxLevel) return false;
        
        // 计算升级费用
        const cost = Math.floor(upgrade.baseCost * Math.pow(upgrade.costMultiplier, upgrade.level - 1));
        
        // 检查是否有足够的资源
        if (this.gameData.resources.crystals < cost) return false;
        
        // 扣除资源并升级
        this.gameData.resources.crystals -= cost;
        upgrade.level++;
        this.gameData.statistics.upgradesPurchased++;
        
        // 应用升级效果
        this.applyUpgradesToSpaceship();
        
        console.log(`升级 ${upgradeType} 到等级 ${upgrade.level}，花费 ${cost} 水晶`);
        return true;
    }

    /**
     * 获取升级费用
     * @param {string} upgradeType - 升级类型
     * @returns {number} 升级费用
     */
    getUpgradeCost(upgradeType) {
        const upgrade = this.gameData.upgrades[upgradeType];
        if (!upgrade || upgrade.level >= upgrade.maxLevel) return -1;
        
        return Math.floor(upgrade.baseCost * Math.pow(upgrade.costMultiplier, upgrade.level - 1));
    }

    /**
     * 切换到下一个星球
     */
    nextPlanet() {
        const nextIndex = (this.gameData.progress.currentPlanet + 1) % this.planets.length;
        this.gameData.progress.currentPlanet = nextIndex;
        this.setCurrentPlanet(nextIndex);
        
        // 如果是新星球，添加到已解锁列表
        if (!this.gameData.progress.unlockedPlanets.includes(nextIndex)) {
            this.gameData.progress.unlockedPlanets.push(nextIndex);
            this.gameData.statistics.planetsVisited++;
        }
    }

    /**
     * 切换到上一个星球
     */
    previousPlanet() {
        const prevIndex = this.gameData.progress.currentPlanet - 1;
        const planetIndex = prevIndex < 0 ? this.planets.length - 1 : prevIndex;
        
        // 只能访问已解锁的星球
        if (this.gameData.progress.unlockedPlanets.includes(planetIndex)) {
            this.gameData.progress.currentPlanet = planetIndex;
            this.setCurrentPlanet(planetIndex);
        }
    }

    /**
     * 获取游戏状态
     * @returns {string} 当前游戏状态
     */
    getGameState() {
        return this.gameState;
    }

    /**
     * 设置游戏状态
     * @param {string} state - 新的游戏状态
     */
    setGameState(state) {
        this.gameState = state;
    }

    /**
     * 获取游戏数据
     * @returns {Object} 游戏数据
     */
    getGameData() {
        return this.gameData;
    }

    /**
     * 获取性能信息
     * @returns {Object} 性能信息
     */
    getPerformanceInfo() {
        return {
            fps: this.fps,
            particleCount: particleSystem.getParticleCount(),
            mineralCount: this.minerals.length
        };
    }
}

// 创建全局游戏逻辑实例
const gameLogic = new GameLogic();
