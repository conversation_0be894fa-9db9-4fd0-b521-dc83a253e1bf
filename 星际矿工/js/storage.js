/**
 * 游戏存储系统
 * 使用localStorage实现游戏状态的保存和加载
 */

class GameStorage {
    constructor() {
        this.storageKey = 'spaceMinerGameData';
        this.defaultGameData = this.getDefaultGameData();
    }

    /**
     * 获取默认游戏数据结构
     * @returns {Object} 默认游戏数据
     */
    getDefaultGameData() {
        return {
            // 游戏版本，用于数据兼容性检查
            version: '1.0.0',
            
            // 玩家资源
            resources: {
                crystals: 0,        // 水晶数量
                energy: 100,        // 当前能量
                maxEnergy: 100,     // 最大能量
                upgradePoints: 0    // 升级点数
            },

            // 玩家统计数据
            statistics: {
                totalPlayTime: 0,           // 总游戏时间（秒）
                totalCrystalsCollected: 0,  // 总收集水晶数
                totalMiningOperations: 0,   // 总挖掘次数
                planetsVisited: 0,          // 访问过的星球数量
                upgradesPurchased: 0        // 购买的升级数量
            },

            // 飞船升级状态
            upgrades: {
                miningEfficiency: {
                    level: 1,
                    maxLevel: 10,
                    baseCost: 10,
                    costMultiplier: 1.5
                },
                energyCapacity: {
                    level: 1,
                    maxLevel: 10,
                    baseCost: 15,
                    costMultiplier: 1.6
                },
                movementSpeed: {
                    level: 1,
                    maxLevel: 10,
                    baseCost: 20,
                    costMultiplier: 1.4
                },
                autoMining: {
                    level: 0,
                    maxLevel: 5,
                    baseCost: 50,
                    costMultiplier: 2.0
                }
            },

            // 游戏设置
            settings: {
                sfxVolume: 70,      // 音效音量 (0-100)
                musicVolume: 50,    // 背景音乐音量 (0-100)
                quality: 'medium',  // 画质设置 (low/medium/high)
                autoSave: true      // 自动保存开关
            },

            // 游戏进度
            progress: {
                currentPlanet: 0,           // 当前星球索引
                unlockedPlanets: [0],       // 已解锁的星球
                achievements: [],           // 已获得的成就
                lastSaveTime: Date.now()    // 最后保存时间
            }
        };
    }

    /**
     * 保存游戏数据到localStorage
     * @param {Object} gameData - 要保存的游戏数据
     * @returns {boolean} 保存是否成功
     */
    saveGame(gameData) {
        try {
            // 更新保存时间
            gameData.progress.lastSaveTime = Date.now();
            
            // 将数据转换为JSON字符串并保存
            const jsonData = JSON.stringify(gameData);
            localStorage.setItem(this.storageKey, jsonData);
            
            console.log('游戏数据保存成功');
            return true;
        } catch (error) {
            console.error('保存游戏数据失败:', error);
            return false;
        }
    }

    /**
     * 从localStorage加载游戏数据
     * @returns {Object} 加载的游戏数据，如果失败则返回默认数据
     */
    loadGame() {
        try {
            const savedData = localStorage.getItem(this.storageKey);
            
            if (!savedData) {
                console.log('未找到保存的游戏数据，使用默认数据');
                return this.defaultGameData;
            }

            const gameData = JSON.parse(savedData);
            
            // 验证数据完整性并合并缺失的字段
            const mergedData = this.mergeGameData(gameData, this.defaultGameData);
            
            console.log('游戏数据加载成功');
            return mergedData;
        } catch (error) {
            console.error('加载游戏数据失败:', error);
            return this.defaultGameData;
        }
    }

    /**
     * 合并游戏数据，确保所有必要字段都存在
     * @param {Object} savedData - 保存的数据
     * @param {Object} defaultData - 默认数据
     * @returns {Object} 合并后的数据
     */
    mergeGameData(savedData, defaultData) {
        const merged = JSON.parse(JSON.stringify(defaultData));
        
        // 递归合并对象
        const mergeObjects = (target, source) => {
            for (const key in source) {
                if (source.hasOwnProperty(key)) {
                    if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
                        if (!target[key] || typeof target[key] !== 'object') {
                            target[key] = {};
                        }
                        mergeObjects(target[key], source[key]);
                    } else {
                        target[key] = source[key];
                    }
                }
            }
        };

        mergeObjects(merged, savedData);
        return merged;
    }

    /**
     * 检查是否存在保存的游戏数据
     * @returns {boolean} 是否存在保存数据
     */
    hasSavedGame() {
        return localStorage.getItem(this.storageKey) !== null;
    }

    /**
     * 删除保存的游戏数据
     * @returns {boolean} 删除是否成功
     */
    deleteSavedGame() {
        try {
            localStorage.removeItem(this.storageKey);
            console.log('游戏数据已删除');
            return true;
        } catch (error) {
            console.error('删除游戏数据失败:', error);
            return false;
        }
    }

    /**
     * 导出游戏数据为JSON字符串
     * @param {Object} gameData - 游戏数据
     * @returns {string} JSON字符串
     */
    exportGameData(gameData) {
        try {
            return JSON.stringify(gameData, null, 2);
        } catch (error) {
            console.error('导出游戏数据失败:', error);
            return null;
        }
    }

    /**
     * 从JSON字符串导入游戏数据
     * @param {string} jsonData - JSON字符串
     * @returns {Object|null} 游戏数据对象，失败返回null
     */
    importGameData(jsonData) {
        try {
            const gameData = JSON.parse(jsonData);
            return this.mergeGameData(gameData, this.defaultGameData);
        } catch (error) {
            console.error('导入游戏数据失败:', error);
            return null;
        }
    }

    /**
     * 获取存储使用情况
     * @returns {Object} 存储使用信息
     */
    getStorageInfo() {
        try {
            const data = localStorage.getItem(this.storageKey);
            const dataSize = data ? new Blob([data]).size : 0;
            
            // 估算localStorage总容量（通常为5-10MB）
            let totalCapacity = 5 * 1024 * 1024; // 5MB
            try {
                // 尝试检测实际容量
                const testKey = 'storage_test';
                let testData = '';
                while (true) {
                    try {
                        localStorage.setItem(testKey, testData);
                        testData += 'a'.repeat(1024); // 每次增加1KB
                    } catch (e) {
                        localStorage.removeItem(testKey);
                        totalCapacity = testData.length;
                        break;
                    }
                }
            } catch (e) {
                // 使用默认值
            }

            return {
                used: dataSize,
                total: totalCapacity,
                percentage: Math.round((dataSize / totalCapacity) * 100)
            };
        } catch (error) {
            console.error('获取存储信息失败:', error);
            return { used: 0, total: 0, percentage: 0 };
        }
    }

    /**
     * 自动保存功能
     * @param {Object} gameData - 游戏数据
     * @param {number} interval - 自动保存间隔（毫秒）
     */
    startAutoSave(gameData, interval = 30000) { // 默认30秒
        if (this.autoSaveInterval) {
            clearInterval(this.autoSaveInterval);
        }

        this.autoSaveInterval = setInterval(() => {
            if (gameData.settings.autoSave) {
                this.saveGame(gameData);
            }
        }, interval);
    }

    /**
     * 停止自动保存
     */
    stopAutoSave() {
        if (this.autoSaveInterval) {
            clearInterval(this.autoSaveInterval);
            this.autoSaveInterval = null;
        }
    }
}

// 创建全局存储实例
const gameStorage = new GameStorage();
