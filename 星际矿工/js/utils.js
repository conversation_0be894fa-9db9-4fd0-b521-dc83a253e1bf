/**
 * 工具函数库
 * 包含游戏中常用的数学计算、随机数生成、颜色处理等工具函数
 */

// 数学工具函数
const MathUtils = {
    /**
     * 生成指定范围内的随机数
     * @param {number} min - 最小值
     * @param {number} max - 最大值
     * @returns {number} 随机数
     */
    random(min, max) {
        return Math.random() * (max - min) + min;
    },

    /**
     * 生成指定范围内的随机整数
     * @param {number} min - 最小值
     * @param {number} max - 最大值
     * @returns {number} 随机整数
     */
    randomInt(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    },

    /**
     * 将数值限制在指定范围内
     * @param {number} value - 要限制的值
     * @param {number} min - 最小值
     * @param {number} max - 最大值
     * @returns {number} 限制后的值
     */
    clamp(value, min, max) {
        return Math.min(Math.max(value, min), max);
    },

    /**
     * 线性插值
     * @param {number} a - 起始值
     * @param {number} b - 结束值
     * @param {number} t - 插值参数 (0-1)
     * @returns {number} 插值结果
     */
    lerp(a, b, t) {
        return a + (b - a) * t;
    },

    /**
     * 计算两点之间的距离
     * @param {number} x1 - 第一个点的x坐标
     * @param {number} y1 - 第一个点的y坐标
     * @param {number} x2 - 第二个点的x坐标
     * @param {number} y2 - 第二个点的y坐标
     * @returns {number} 距离
     */
    distance(x1, y1, x2, y2) {
        const dx = x2 - x1;
        const dy = y2 - y1;
        return Math.sqrt(dx * dx + dy * dy);
    },

    /**
     * 计算角度（弧度）
     * @param {number} x1 - 起点x坐标
     * @param {number} y1 - 起点y坐标
     * @param {number} x2 - 终点x坐标
     * @param {number} y2 - 终点y坐标
     * @returns {number} 角度（弧度）
     */
    angle(x1, y1, x2, y2) {
        return Math.atan2(y2 - y1, x2 - x1);
    },

    /**
     * 弧度转角度
     * @param {number} radians - 弧度值
     * @returns {number} 角度值
     */
    radiansToDegrees(radians) {
        return radians * (180 / Math.PI);
    },

    /**
     * 角度转弧度
     * @param {number} degrees - 角度值
     * @returns {number} 弧度值
     */
    degreesToRadians(degrees) {
        return degrees * (Math.PI / 180);
    }
};

// 颜色工具函数
const ColorUtils = {
    /**
     * 生成随机颜色
     * @returns {string} 十六进制颜色值
     */
    randomColor() {
        const colors = [
            '#00d4ff', '#4ecdc4', '#ff6b6b', '#ffa500',
            '#9b59b6', '#e74c3c', '#f39c12', '#2ecc71'
        ];
        return colors[Math.floor(Math.random() * colors.length)];
    },

    /**
     * 根据稀有度获取颜色
     * @param {string} rarity - 稀有度等级
     * @returns {string} 对应的颜色
     */
    getRarityColor(rarity) {
        const rarityColors = {
            'common': '#ffffff',      // 普通 - 白色
            'uncommon': '#00d4ff',    // 不常见 - 蓝色
            'rare': '#9b59b6',        // 稀有 - 紫色
            'epic': '#ffa500',        // 史诗 - 橙色
            'legendary': '#ff6b6b'    // 传说 - 红色
        };
        return rarityColors[rarity] || rarityColors.common;
    },

    /**
     * 颜色插值
     * @param {string} color1 - 起始颜色
     * @param {string} color2 - 结束颜色
     * @param {number} t - 插值参数 (0-1)
     * @returns {string} 插值后的颜色
     */
    lerpColor(color1, color2, t) {
        // 简化版本，仅支持十六进制颜色
        const r1 = parseInt(color1.slice(1, 3), 16);
        const g1 = parseInt(color1.slice(3, 5), 16);
        const b1 = parseInt(color1.slice(5, 7), 16);
        
        const r2 = parseInt(color2.slice(1, 3), 16);
        const g2 = parseInt(color2.slice(3, 5), 16);
        const b2 = parseInt(color2.slice(5, 7), 16);
        
        const r = Math.round(MathUtils.lerp(r1, r2, t));
        const g = Math.round(MathUtils.lerp(g1, g2, t));
        const b = Math.round(MathUtils.lerp(b1, b2, t));
        
        return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
    }
};

// 时间工具函数
const TimeUtils = {
    /**
     * 格式化时间显示
     * @param {number} seconds - 秒数
     * @returns {string} 格式化的时间字符串
     */
    formatTime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        
        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }
    },

    /**
     * 获取当前时间戳
     * @returns {number} 时间戳
     */
    now() {
        return Date.now();
    }
};

// DOM工具函数
const DOMUtils = {
    /**
     * 获取元素
     * @param {string} selector - CSS选择器
     * @returns {Element|null} DOM元素
     */
    $(selector) {
        return document.querySelector(selector);
    },

    /**
     * 获取所有匹配的元素
     * @param {string} selector - CSS选择器
     * @returns {NodeList} DOM元素列表
     */
    $$(selector) {
        return document.querySelectorAll(selector);
    },

    /**
     * 添加事件监听器
     * @param {Element} element - DOM元素
     * @param {string} event - 事件类型
     * @param {Function} handler - 事件处理函数
     */
    on(element, event, handler) {
        element.addEventListener(event, handler);
    },

    /**
     * 移除事件监听器
     * @param {Element} element - DOM元素
     * @param {string} event - 事件类型
     * @param {Function} handler - 事件处理函数
     */
    off(element, event, handler) {
        element.removeEventListener(event, handler);
    },

    /**
     * 切换元素的显示/隐藏状态
     * @param {Element} element - DOM元素
     * @param {boolean} show - 是否显示
     */
    toggle(element, show) {
        if (show) {
            element.classList.remove('hidden');
        } else {
            element.classList.add('hidden');
        }
    },

    /**
     * 检查元素是否隐藏
     * @param {Element} element - DOM元素
     * @returns {boolean} 是否隐藏
     */
    isHidden(element) {
        return element.classList.contains('hidden');
    }
};

// 设备检测工具
const DeviceUtils = {
    /**
     * 检查是否为移动设备
     * @returns {boolean} 是否为移动设备
     */
    isMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               window.innerWidth <= 768;
    },

    /**
     * 检查是否支持触摸
     * @returns {boolean} 是否支持触摸
     */
    isTouchDevice() {
        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    },

    /**
     * 获取屏幕尺寸
     * @returns {Object} 包含宽度和高度的对象
     */
    getScreenSize() {
        return {
            width: window.innerWidth,
            height: window.innerHeight
        };
    }
};

// 音频工具函数
const AudioUtils = {
    /**
     * 创建音频上下文（用于后续音效实现）
     */
    createAudioContext() {
        if (typeof AudioContext !== 'undefined') {
            return new AudioContext();
        } else if (typeof webkitAudioContext !== 'undefined') {
            return new webkitAudioContext();
        }
        return null;
    },

    /**
     * 播放简单的蜂鸣音效
     * @param {number} frequency - 频率
     * @param {number} duration - 持续时间（毫秒）
     * @param {number} volume - 音量 (0-1)
     */
    playBeep(frequency = 440, duration = 200, volume = 0.1) {
        const audioContext = this.createAudioContext();
        if (!audioContext) return;

        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.value = frequency;
        oscillator.type = 'sine';

        gainNode.gain.setValueAtTime(volume, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration / 1000);

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + duration / 1000);
    }
};

// 导出工具函数（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        MathUtils,
        ColorUtils,
        TimeUtils,
        DOMUtils,
        DeviceUtils,
        AudioUtils
    };
}
