/**
 * 游戏对象类定义
 * 包含飞船、矿物、星球等游戏实体
 */

// 基础游戏对象类
class GameObject {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.width = 32;
        this.height = 32;
        this.active = true;
        this.id = Math.random().toString(36).substr(2, 9);
    }

    /**
     * 更新对象状态
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        // 基础更新逻辑，子类可重写
    }

    /**
     * 渲染对象
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    render(ctx) {
        // 基础渲染逻辑，子类可重写
    }

    /**
     * 检查与另一个对象的碰撞
     * @param {GameObject} other - 另一个游戏对象
     * @returns {boolean} 是否发生碰撞
     */
    collidesWith(other) {
        return this.x < other.x + other.width &&
               this.x + this.width > other.x &&
               this.y < other.y + other.height &&
               this.y + this.height > other.y;
    }

    /**
     * 获取对象中心点
     * @returns {Object} 包含x和y坐标的对象
     */
    getCenter() {
        return {
            x: this.x + this.width / 2,
            y: this.y + this.height / 2
        };
    }
}

// 玩家飞船类
class Spaceship extends GameObject {
    constructor(x, y) {
        super(x, y);
        this.width = 48;
        this.height = 48;
        this.speed = 200; // 像素/秒
        this.rotation = 0;
        this.thrust = 0;
        this.maxThrust = 300;
        this.friction = 0.95;
        this.velocityX = 0;
        this.velocityY = 0;
        
        // 飞船状态
        this.energy = 100;
        this.maxEnergy = 100;
        this.miningPower = 1;
        this.miningRange = 80;
        this.isMining = false;
        this.miningTarget = null;
        
        // 视觉效果
        this.thrusterParticles = [];
        this.miningBeam = null;
    }

    /**
     * 更新飞船状态
     * @param {number} deltaTime - 时间增量
     * @param {Object} input - 输入状态
     */
    update(deltaTime, input) {
        // 处理输入
        this.handleInput(input, deltaTime);
        
        // 更新物理状态
        this.updatePhysics(deltaTime);
        
        // 更新能量
        this.updateEnergy(deltaTime);
        
        // 更新粒子效果
        this.updateParticles(deltaTime);
        
        // 更新挖掘状态
        this.updateMining(deltaTime);
    }

    /**
     * 处理输入
     * @param {Object} input - 输入状态
     * @param {number} deltaTime - 时间增量
     */
    handleInput(input, deltaTime) {
        // 旋转控制
        if (input.left) {
            this.rotation -= 3 * deltaTime;
        }
        if (input.right) {
            this.rotation += 3 * deltaTime;
        }
        
        // 推进控制
        if (input.up && this.energy > 0) {
            this.thrust = this.maxThrust;
            this.energy = Math.max(0, this.energy - 20 * deltaTime);
            this.createThrusterParticles();
        } else {
            this.thrust = 0;
        }
        
        // 挖掘控制
        this.isMining = input.mine && this.energy > 10;
    }

    /**
     * 更新物理状态
     * @param {number} deltaTime - 时间增量
     */
    updatePhysics(deltaTime) {
        // 计算推进力
        const thrustX = Math.cos(this.rotation) * this.thrust * deltaTime;
        const thrustY = Math.sin(this.rotation) * this.thrust * deltaTime;
        
        // 更新速度
        this.velocityX += thrustX;
        this.velocityY += thrustY;
        
        // 应用摩擦力
        this.velocityX *= this.friction;
        this.velocityY *= this.friction;
        
        // 更新位置
        this.x += this.velocityX * deltaTime;
        this.y += this.velocityY * deltaTime;
        
        // 边界检查（可选：环绕屏幕或限制在边界内）
        this.x = MathUtils.clamp(this.x, 0, window.innerWidth - this.width);
        this.y = MathUtils.clamp(this.y, 0, window.innerHeight - this.height);
    }

    /**
     * 更新能量
     * @param {number} deltaTime - 时间增量
     */
    updateEnergy(deltaTime) {
        // 能量自然恢复
        if (this.energy < this.maxEnergy && this.thrust === 0) {
            this.energy = Math.min(this.maxEnergy, this.energy + 15 * deltaTime);
        }
    }

    /**
     * 创建推进器粒子效果
     */
    createThrusterParticles() {
        const particleCount = 3;
        for (let i = 0; i < particleCount; i++) {
            const angle = this.rotation + Math.PI + MathUtils.random(-0.3, 0.3);
            const speed = MathUtils.random(100, 200);
            const particle = {
                x: this.x + this.width / 2 - Math.cos(this.rotation) * 20,
                y: this.y + this.height / 2 - Math.sin(this.rotation) * 20,
                vx: Math.cos(angle) * speed,
                vy: Math.sin(angle) * speed,
                life: 0.5,
                maxLife: 0.5,
                size: MathUtils.random(2, 4),
                color: '#ff6b6b'
            };
            this.thrusterParticles.push(particle);
        }
    }

    /**
     * 更新粒子效果
     * @param {number} deltaTime - 时间增量
     */
    updateParticles(deltaTime) {
        this.thrusterParticles = this.thrusterParticles.filter(particle => {
            particle.x += particle.vx * deltaTime;
            particle.y += particle.vy * deltaTime;
            particle.life -= deltaTime;
            return particle.life > 0;
        });
    }

    /**
     * 更新挖掘状态
     * @param {number} deltaTime - 时间增量
     */
    updateMining(deltaTime) {
        if (this.isMining && this.energy > 10) {
            this.energy -= 25 * deltaTime;
            // 挖掘逻辑将在游戏主循环中处理
        }
    }

    /**
     * 渲染飞船
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    render(ctx) {
        ctx.save();
        
        // 移动到飞船中心
        ctx.translate(this.x + this.width / 2, this.y + this.height / 2);
        ctx.rotate(this.rotation);
        
        // 绘制飞船主体
        ctx.fillStyle = '#4ecdc4';
        ctx.beginPath();
        ctx.moveTo(20, 0);
        ctx.lineTo(-15, -12);
        ctx.lineTo(-10, 0);
        ctx.lineTo(-15, 12);
        ctx.closePath();
        ctx.fill();
        
        // 绘制飞船细节
        ctx.fillStyle = '#00d4ff';
        ctx.beginPath();
        ctx.moveTo(15, 0);
        ctx.lineTo(-8, -8);
        ctx.lineTo(-5, 0);
        ctx.lineTo(-8, 8);
        ctx.closePath();
        ctx.fill();
        
        // 绘制驾驶舱
        ctx.fillStyle = '#ffffff';
        ctx.beginPath();
        ctx.arc(5, 0, 4, 0, Math.PI * 2);
        ctx.fill();
        
        ctx.restore();
        
        // 绘制粒子效果
        this.renderParticles(ctx);
        
        // 绘制挖掘光束
        if (this.isMining && this.miningTarget) {
            this.renderMiningBeam(ctx);
        }
    }

    /**
     * 渲染粒子效果
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    renderParticles(ctx) {
        this.thrusterParticles.forEach(particle => {
            const alpha = particle.life / particle.maxLife;
            ctx.save();
            ctx.globalAlpha = alpha;
            ctx.fillStyle = particle.color;
            ctx.beginPath();
            ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            ctx.fill();
            ctx.restore();
        });
    }

    /**
     * 渲染挖掘光束
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    renderMiningBeam(ctx) {
        if (!this.miningTarget) return;
        
        const center = this.getCenter();
        const targetCenter = this.miningTarget.getCenter();
        
        ctx.save();
        ctx.strokeStyle = '#00d4ff';
        ctx.lineWidth = 3;
        ctx.globalAlpha = 0.8;
        
        // 绘制主光束
        ctx.beginPath();
        ctx.moveTo(center.x, center.y);
        ctx.lineTo(targetCenter.x, targetCenter.y);
        ctx.stroke();
        
        // 绘制光束效果
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 1;
        ctx.globalAlpha = 0.6;
        ctx.stroke();
        
        ctx.restore();
    }

    /**
     * 寻找挖掘目标
     * @param {Array} minerals - 矿物数组
     * @returns {Mineral|null} 找到的矿物目标
     */
    findMiningTarget(minerals) {
        const center = this.getCenter();
        let closestMineral = null;
        let closestDistance = this.miningRange;
        
        minerals.forEach(mineral => {
            if (!mineral.active) return;
            
            const mineralCenter = mineral.getCenter();
            const distance = MathUtils.distance(
                center.x, center.y,
                mineralCenter.x, mineralCenter.y
            );
            
            if (distance < closestDistance) {
                closestDistance = distance;
                closestMineral = mineral;
            }
        });
        
        return closestMineral;
    }
}

// 矿物类
class Mineral extends GameObject {
    constructor(x, y, type = 'common') {
        super(x, y);
        this.type = type;
        this.value = this.getValueByType(type);
        this.health = this.getHealthByType(type);
        this.maxHealth = this.health;
        this.color = ColorUtils.getRarityColor(type);
        this.size = MathUtils.random(16, 32);
        this.width = this.size;
        this.height = this.size;
        this.rotation = MathUtils.random(0, Math.PI * 2);
        this.rotationSpeed = MathUtils.random(-1, 1);
        this.pulsePhase = MathUtils.random(0, Math.PI * 2);
    }

    /**
     * 根据类型获取价值
     * @param {string} type - 矿物类型
     * @returns {number} 矿物价值
     */
    getValueByType(type) {
        const values = {
            'common': 1,
            'uncommon': 3,
            'rare': 8,
            'epic': 20,
            'legendary': 50
        };
        return values[type] || 1;
    }

    /**
     * 根据类型获取生命值
     * @param {string} type - 矿物类型
     * @returns {number} 矿物生命值
     */
    getHealthByType(type) {
        const healths = {
            'common': 10,
            'uncommon': 25,
            'rare': 50,
            'epic': 100,
            'legendary': 200
        };
        return healths[type] || 10;
    }

    /**
     * 更新矿物状态
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        // 旋转动画
        this.rotation += this.rotationSpeed * deltaTime;
        
        // 脉冲动画
        this.pulsePhase += 2 * deltaTime;
        
        // 检查是否被摧毁
        if (this.health <= 0) {
            this.active = false;
        }
    }

    /**
     * 受到挖掘伤害
     * @param {number} damage - 伤害值
     * @returns {boolean} 是否被摧毁
     */
    takeDamage(damage) {
        this.health -= damage;
        return this.health <= 0;
    }

    /**
     * 渲染矿物
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    render(ctx) {
        ctx.save();
        
        // 移动到矿物中心
        ctx.translate(this.x + this.width / 2, this.y + this.height / 2);
        ctx.rotate(this.rotation);
        
        // 计算脉冲效果
        const pulse = Math.sin(this.pulsePhase) * 0.1 + 1;
        const currentSize = this.size * pulse;
        
        // 绘制矿物外层光晕
        ctx.fillStyle = this.color + '40';
        ctx.beginPath();
        ctx.arc(0, 0, currentSize * 0.8, 0, Math.PI * 2);
        ctx.fill();
        
        // 绘制矿物主体
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(0, 0, currentSize * 0.6, 0, Math.PI * 2);
        ctx.fill();
        
        // 绘制矿物核心
        ctx.fillStyle = '#ffffff';
        ctx.beginPath();
        ctx.arc(0, 0, currentSize * 0.3, 0, Math.PI * 2);
        ctx.fill();
        
        // 绘制生命值条（如果受损）
        if (this.health < this.maxHealth) {
            ctx.restore();
            this.renderHealthBar(ctx);
            return;
        }
        
        ctx.restore();
    }

    /**
     * 渲染生命值条
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    renderHealthBar(ctx) {
        const barWidth = this.width;
        const barHeight = 4;
        const barY = this.y - 10;
        
        // 背景
        ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
        ctx.fillRect(this.x, barY, barWidth, barHeight);
        
        // 生命值
        const healthPercent = this.health / this.maxHealth;
        ctx.fillStyle = healthPercent > 0.5 ? '#4ecdc4' : '#ff6b6b';
        ctx.fillRect(this.x, barY, barWidth * healthPercent, barHeight);
    }
}

// 星球类
class Planet extends GameObject {
    constructor(x, y, type = 'rocky') {
        super(x, y);
        this.type = type;
        this.radius = MathUtils.random(100, 200);
        this.width = this.radius * 2;
        this.height = this.radius * 2;
        this.color = this.getColorByType(type);
        this.rotation = 0;
        this.rotationSpeed = MathUtils.random(0.1, 0.3);
        this.minerals = [];
        this.generateMinerals();
    }

    /**
     * 根据类型获取颜色
     * @param {string} type - 星球类型
     * @returns {string} 星球颜色
     */
    getColorByType(type) {
        const colors = {
            'rocky': '#8B4513',
            'ice': '#87CEEB',
            'gas': '#9370DB',
            'volcanic': '#FF4500',
            'crystal': '#00CED1'
        };
        return colors[type] || colors.rocky;
    }

    /**
     * 生成星球上的矿物
     */
    generateMinerals() {
        const mineralCount = MathUtils.randomInt(5, 15);
        
        for (let i = 0; i < mineralCount; i++) {
            // 在星球表面生成矿物
            const angle = MathUtils.random(0, Math.PI * 2);
            const distance = this.radius * MathUtils.random(0.7, 0.9);
            
            const mineralX = this.x + this.radius + Math.cos(angle) * distance;
            const mineralY = this.y + this.radius + Math.sin(angle) * distance;
            
            // 根据星球类型决定矿物稀有度
            const rarity = this.getMineralRarity();
            const mineral = new Mineral(mineralX, mineralY, rarity);
            
            this.minerals.push(mineral);
        }
    }

    /**
     * 获取矿物稀有度
     * @returns {string} 矿物稀有度
     */
    getMineralRarity() {
        const rand = Math.random();
        if (rand < 0.5) return 'common';
        if (rand < 0.75) return 'uncommon';
        if (rand < 0.9) return 'rare';
        if (rand < 0.98) return 'epic';
        return 'legendary';
    }

    /**
     * 更新星球状态
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        this.rotation += this.rotationSpeed * deltaTime;
        
        // 更新矿物
        this.minerals.forEach(mineral => mineral.update(deltaTime));
        
        // 移除被摧毁的矿物
        this.minerals = this.minerals.filter(mineral => mineral.active);
    }

    /**
     * 渲染星球
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    render(ctx) {
        ctx.save();
        
        // 绘制星球主体
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(this.x + this.radius, this.y + this.radius, this.radius, 0, Math.PI * 2);
        ctx.fill();
        
        // 绘制星球表面细节
        ctx.fillStyle = ColorUtils.lerpColor(this.color, '#000000', 0.3);
        ctx.beginPath();
        ctx.arc(this.x + this.radius * 0.7, this.y + this.radius * 0.7, this.radius * 0.3, 0, Math.PI * 2);
        ctx.fill();
        
        ctx.restore();
        
        // 渲染矿物
        this.minerals.forEach(mineral => mineral.render(ctx));
    }
}
