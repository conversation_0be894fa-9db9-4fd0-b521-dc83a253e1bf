/**
 * 游戏主程序
 * 负责初始化游戏、管理游戏循环和协调各个系统
 */

class SpaceMinerGame {
    constructor() {
        // 游戏状态
        this.isRunning = false;
        this.isPaused = false;
        this.lastFrameTime = 0;
        this.targetFPS = 60;
        this.frameInterval = 1000 / this.targetFPS;
        
        // 性能监控
        this.performanceStats = {
            fps: 0,
            frameTime: 0,
            updateTime: 0,
            renderTime: 0
        };
        
        // 调试模式
        this.debugMode = false;
        
        this.init();
    }

    /**
     * 初始化游戏
     */
    async init() {
        try {
            console.log('🚀 星际矿工游戏初始化开始...');
            
            // 检查浏览器兼容性
            this.checkBrowserCompatibility();
            
            // 初始化渲染器
            await this.initRenderer();
            
            // 初始化音频系统
            this.initAudio();
            
            // 设置全局事件监听
            this.setupGlobalEvents();
            
            // 显示加载界面
            uiManager.showLoading();
            
            // 启动游戏循环
            this.startGameLoop();
            
            console.log('✅ 游戏初始化完成');
            
        } catch (error) {
            console.error('❌ 游戏初始化失败:', error);
            this.showErrorMessage('游戏初始化失败，请刷新页面重试。');
        }
    }

    /**
     * 检查浏览器兼容性
     */
    checkBrowserCompatibility() {
        // 检查Canvas支持
        const canvas = document.createElement('canvas');
        if (!canvas.getContext) {
            throw new Error('浏览器不支持Canvas');
        }

        // 检查localStorage支持
        if (!window.localStorage) {
            console.warn('浏览器不支持localStorage，游戏进度无法保存');
        }

        // 检查AudioContext支持
        if (!window.AudioContext && !window.webkitAudioContext) {
            console.warn('浏览器不支持Web Audio API，音效可能无法正常工作');
        }

        console.log('✅ 浏览器兼容性检查通过');
    }

    /**
     * 初始化渲染器
     */
    async initRenderer() {
        const canvas = document.getElementById('game-canvas');
        if (!canvas) {
            throw new Error('找不到游戏画布元素');
        }

        // 创建全局渲染器实例
        window.gameRenderer = new GameRenderer(canvas);
        
        console.log('✅ 渲染器初始化完成');
    }

    /**
     * 初始化音频系统
     */
    initAudio() {
        try {
            // 这里可以预加载音效文件
            // 目前使用简单的蜂鸣音效
            console.log('✅ 音频系统初始化完成');
        } catch (error) {
            console.warn('⚠️ 音频系统初始化失败:', error);
        }
    }

    /**
     * 设置全局事件监听
     */
    setupGlobalEvents() {
        // 窗口失焦时暂停游戏
        window.addEventListener('blur', () => {
            if (gameLogic.getGameState() === 'playing') {
                this.pauseGame();
            }
        });

        // 窗口获得焦点时恢复游戏
        window.addEventListener('focus', () => {
            if (gameLogic.getGameState() === 'paused' && this.isPaused) {
                this.resumeGame();
            }
        });

        // 监听页面卸载事件，保存游戏
        window.addEventListener('beforeunload', () => {
            if (gameLogic.getGameState() === 'playing') {
                gameLogic.saveGame();
            }
        });

        // 监听键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleGlobalKeydown(e);
        });

        // 阻止右键菜单
        document.addEventListener('contextmenu', (e) => {
            e.preventDefault();
        });

        // 阻止选择文本
        document.addEventListener('selectstart', (e) => {
            e.preventDefault();
        });

        console.log('✅ 全局事件监听设置完成');
    }

    /**
     * 处理全局键盘事件
     * @param {KeyboardEvent} e - 键盘事件
     */
    handleGlobalKeydown(e) {
        // 调试模式切换
        if (e.code === 'F12' || (e.ctrlKey && e.shiftKey && e.code === 'KeyD')) {
            e.preventDefault();
            this.toggleDebugMode();
        }

        // 全屏切换
        if (e.code === 'F11') {
            e.preventDefault();
            this.toggleFullscreen();
        }

        // 快速保存
        if (e.ctrlKey && e.code === 'KeyS') {
            e.preventDefault();
            if (gameLogic.getGameState() === 'playing') {
                gameLogic.saveGame();
                uiManager.showNotification('游戏已保存', 'success');
            }
        }
    }

    /**
     * 启动游戏循环
     */
    startGameLoop() {
        this.isRunning = true;
        this.lastFrameTime = performance.now();
        this.gameLoop();
        
        console.log('✅ 游戏循环已启动');
    }

    /**
     * 游戏主循环
     */
    gameLoop() {
        if (!this.isRunning) return;

        const currentTime = performance.now();
        const deltaTime = (currentTime - this.lastFrameTime) / 1000; // 转换为秒
        
        // 限制最大帧时间，避免大的时间跳跃
        const clampedDeltaTime = Math.min(deltaTime, 1/30); // 最低30FPS
        
        // 更新性能统计
        this.updatePerformanceStats(currentTime, deltaTime);
        
        try {
            // 更新游戏逻辑
            const updateStartTime = performance.now();
            this.update(clampedDeltaTime);
            this.performanceStats.updateTime = performance.now() - updateStartTime;
            
            // 渲染游戏
            const renderStartTime = performance.now();
            this.render(clampedDeltaTime);
            this.performanceStats.renderTime = performance.now() - renderStartTime;
            
        } catch (error) {
            console.error('游戏循环错误:', error);
            this.handleGameError(error);
        }
        
        this.lastFrameTime = currentTime;
        
        // 请求下一帧
        requestAnimationFrame(() => this.gameLoop());
    }

    /**
     * 更新游戏逻辑
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        // 更新游戏逻辑
        gameLogic.update(deltaTime);
    }

    /**
     * 渲染游戏
     * @param {number} deltaTime - 时间增量
     */
    render(deltaTime) {
        if (!gameRenderer) return;

        // 开始渲染帧
        gameRenderer.beginFrame();

        // 渲染背景
        gameRenderer.renderBackground(deltaTime);

        // 只在游戏进行时渲染游戏对象
        if (gameLogic.getGameState() === 'playing') {
            // 渲染星球
            gameLogic.planets.forEach(planet => {
                if (gameRenderer.isInView(planet)) {
                    planet.render(gameRenderer.ctx);
                }
            });

            // 渲染矿物
            gameLogic.minerals.forEach(mineral => {
                if (gameRenderer.isInView(mineral)) {
                    mineral.render(gameRenderer.ctx);
                }
            });

            // 渲染飞船
            if (gameLogic.spaceship) {
                gameLogic.spaceship.render(gameRenderer.ctx);
            }

            // 渲染粒子系统
            particleSystem.render(gameRenderer.ctx);
        }

        // 渲染调试信息
        if (this.debugMode) {
            this.renderDebugInfo();
        }

        // 结束渲染帧
        gameRenderer.endFrame();
    }

    /**
     * 渲染调试信息
     */
    renderDebugInfo() {
        const ctx = gameRenderer.ctx;
        const perfInfo = gameLogic.getPerformanceInfo();
        
        ctx.save();
        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        ctx.fillRect(10, 10, 200, 120);
        
        ctx.fillStyle = '#00ff00';
        ctx.font = '12px monospace';
        ctx.fillText(`FPS: ${perfInfo.fps}`, 20, 30);
        ctx.fillText(`Frame Time: ${this.performanceStats.frameTime.toFixed(2)}ms`, 20, 45);
        ctx.fillText(`Update Time: ${this.performanceStats.updateTime.toFixed(2)}ms`, 20, 60);
        ctx.fillText(`Render Time: ${this.performanceStats.renderTime.toFixed(2)}ms`, 20, 75);
        ctx.fillText(`Particles: ${perfInfo.particleCount}`, 20, 90);
        ctx.fillText(`Minerals: ${perfInfo.mineralCount}`, 20, 105);
        
        if (gameLogic.spaceship) {
            ctx.fillText(`Ship Energy: ${Math.floor(gameLogic.spaceship.energy)}`, 20, 120);
        }
        
        ctx.restore();
    }

    /**
     * 更新性能统计
     * @param {number} currentTime - 当前时间
     * @param {number} deltaTime - 时间增量
     */
    updatePerformanceStats(currentTime, deltaTime) {
        this.performanceStats.frameTime = deltaTime * 1000; // 转换为毫秒
        this.performanceStats.fps = Math.round(1 / deltaTime);
    }

    /**
     * 暂停游戏
     */
    pauseGame() {
        this.isPaused = true;
        gameLogic.pauseGame();
        console.log('游戏已暂停');
    }

    /**
     * 恢复游戏
     */
    resumeGame() {
        this.isPaused = false;
        gameLogic.resumeGame();
        console.log('游戏已恢复');
    }

    /**
     * 停止游戏
     */
    stopGame() {
        this.isRunning = false;
        
        // 保存游戏
        if (gameLogic.getGameState() === 'playing') {
            gameLogic.saveGame();
        }
        
        // 停止自动保存
        gameStorage.stopAutoSave();
        
        // 停止UI更新
        uiManager.stopUIUpdate();
        
        console.log('游戏已停止');
    }

    /**
     * 切换调试模式
     */
    toggleDebugMode() {
        this.debugMode = !this.debugMode;
        console.log(`调试模式: ${this.debugMode ? '开启' : '关闭'}`);
        
        if (this.debugMode) {
            uiManager.showNotification('调试模式已开启', 'info');
        } else {
            uiManager.showNotification('调试模式已关闭', 'info');
        }
    }

    /**
     * 切换全屏模式
     */
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(err => {
                console.warn('无法进入全屏模式:', err);
            });
        } else {
            document.exitFullscreen().catch(err => {
                console.warn('无法退出全屏模式:', err);
            });
        }
    }

    /**
     * 处理游戏错误
     * @param {Error} error - 错误对象
     */
    handleGameError(error) {
        console.error('游戏运行时错误:', error);
        
        // 尝试保存游戏状态
        try {
            if (gameLogic.getGameState() === 'playing') {
                gameLogic.saveGame();
            }
        } catch (saveError) {
            console.error('保存游戏失败:', saveError);
        }
        
        // 显示错误信息
        this.showErrorMessage('游戏运行出现错误，已尝试保存进度。请刷新页面重试。');
    }

    /**
     * 显示错误信息
     * @param {string} message - 错误信息
     */
    showErrorMessage(message) {
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 0, 0, 0.9);
            color: white;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Orbitron', monospace;
            text-align: center;
            z-index: 10000;
            max-width: 400px;
        `;
        errorDiv.innerHTML = `
            <h3>游戏错误</h3>
            <p>${message}</p>
            <button onclick="location.reload()" style="
                margin-top: 10px;
                padding: 8px 16px;
                background: white;
                color: red;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                font-family: 'Orbitron', monospace;
            ">刷新页面</button>
        `;
        
        document.body.appendChild(errorDiv);
    }

    /**
     * 获取游戏实例
     * @returns {SpaceMinerGame} 游戏实例
     */
    static getInstance() {
        if (!SpaceMinerGame.instance) {
            SpaceMinerGame.instance = new SpaceMinerGame();
        }
        return SpaceMinerGame.instance;
    }
}

// 页面加载完成后启动游戏
document.addEventListener('DOMContentLoaded', () => {
    console.log('🌟 欢迎来到星际矿工！');
    
    // 创建游戏实例
    window.spaceMinerGame = SpaceMinerGame.getInstance();
    
    // 添加一些CSS动画
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
        
        .notification {
            animation: slideIn 0.3s ease-out;
        }
    `;
    document.head.appendChild(style);
});
