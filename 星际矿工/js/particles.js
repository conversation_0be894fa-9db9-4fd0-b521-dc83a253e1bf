/**
 * 粒子系统
 * 用于创建各种视觉效果，如爆炸、火花、星尘等
 */

// 单个粒子类
class Particle {
    constructor(x, y, options = {}) {
        this.x = x;
        this.y = y;
        this.vx = options.vx || 0;
        this.vy = options.vy || 0;
        this.ax = options.ax || 0;
        this.ay = options.ay || 0;
        this.life = options.life || 1;
        this.maxLife = this.life;
        this.size = options.size || 2;
        this.color = options.color || '#ffffff';
        this.alpha = options.alpha || 1;
        this.rotation = options.rotation || 0;
        this.rotationSpeed = options.rotationSpeed || 0;
        this.gravity = options.gravity || 0;
        this.friction = options.friction || 1;
        this.fadeOut = options.fadeOut !== false;
        this.shrink = options.shrink || false;
        this.active = true;
    }

    /**
     * 更新粒子状态
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        if (!this.active) return;

        // 更新速度
        this.vx += this.ax * deltaTime;
        this.vy += this.ay * deltaTime;
        this.vy += this.gravity * deltaTime;

        // 应用摩擦力
        this.vx *= this.friction;
        this.vy *= this.friction;

        // 更新位置
        this.x += this.vx * deltaTime;
        this.y += this.vy * deltaTime;

        // 更新旋转
        this.rotation += this.rotationSpeed * deltaTime;

        // 更新生命值
        this.life -= deltaTime;

        // 更新视觉效果
        if (this.fadeOut) {
            this.alpha = this.life / this.maxLife;
        }

        if (this.shrink) {
            this.size = (this.life / this.maxLife) * this.size;
        }

        // 检查是否死亡
        if (this.life <= 0) {
            this.active = false;
        }
    }

    /**
     * 渲染粒子
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    render(ctx) {
        if (!this.active) return;

        ctx.save();
        ctx.globalAlpha = this.alpha;
        ctx.translate(this.x, this.y);
        ctx.rotate(this.rotation);

        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(0, 0, this.size, 0, Math.PI * 2);
        ctx.fill();

        ctx.restore();
    }
}

// 粒子系统管理器
class ParticleSystem {
    constructor() {
        this.particles = [];
        this.emitters = [];
    }

    /**
     * 创建爆炸效果
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {Object} options - 配置选项
     */
    createExplosion(x, y, options = {}) {
        const particleCount = options.count || 20;
        const colors = options.colors || ['#ff6b6b', '#ffa500', '#ffff00', '#ffffff'];
        const speed = options.speed || 150;
        const life = options.life || 1;

        for (let i = 0; i < particleCount; i++) {
            const angle = (Math.PI * 2 * i) / particleCount + MathUtils.random(-0.3, 0.3);
            const velocity = MathUtils.random(speed * 0.5, speed);
            const color = colors[Math.floor(Math.random() * colors.length)];

            const particle = new Particle(x, y, {
                vx: Math.cos(angle) * velocity,
                vy: Math.sin(angle) * velocity,
                life: life + MathUtils.random(-0.3, 0.3),
                size: MathUtils.random(2, 6),
                color: color,
                gravity: 50,
                friction: 0.98,
                shrink: true
            });

            this.particles.push(particle);
        }
    }

    /**
     * 创建火花效果
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {number} angle - 发射角度
     * @param {Object} options - 配置选项
     */
    createSparks(x, y, angle, options = {}) {
        const particleCount = options.count || 10;
        const spread = options.spread || 0.5;
        const speed = options.speed || 200;
        const color = options.color || '#00d4ff';

        for (let i = 0; i < particleCount; i++) {
            const sparkAngle = angle + MathUtils.random(-spread, spread);
            const velocity = MathUtils.random(speed * 0.7, speed);

            const particle = new Particle(x, y, {
                vx: Math.cos(sparkAngle) * velocity,
                vy: Math.sin(sparkAngle) * velocity,
                life: MathUtils.random(0.3, 0.8),
                size: MathUtils.random(1, 3),
                color: color,
                gravity: 100,
                friction: 0.95
            });

            this.particles.push(particle);
        }
    }

    /**
     * 创建收集效果
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {string} color - 颜色
     */
    createCollectionEffect(x, y, color = '#4ecdc4') {
        const particleCount = 8;

        for (let i = 0; i < particleCount; i++) {
            const angle = (Math.PI * 2 * i) / particleCount;
            const distance = 30;
            const startX = x + Math.cos(angle) * distance;
            const startY = y + Math.sin(angle) * distance;

            const particle = new Particle(startX, startY, {
                vx: -Math.cos(angle) * 100,
                vy: -Math.sin(angle) * 100,
                life: 0.5,
                size: 4,
                color: color,
                shrink: true
            });

            this.particles.push(particle);
        }
    }

    /**
     * 创建星尘效果
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {Object} options - 配置选项
     */
    createStardust(x, y, options = {}) {
        const particleCount = options.count || 5;
        const colors = ['#ffffff', '#00d4ff', '#4ecdc4', '#ffa500'];

        for (let i = 0; i < particleCount; i++) {
            const angle = MathUtils.random(0, Math.PI * 2);
            const velocity = MathUtils.random(20, 60);
            const color = colors[Math.floor(Math.random() * colors.length)];

            const particle = new Particle(x, y, {
                vx: Math.cos(angle) * velocity,
                vy: Math.sin(angle) * velocity,
                life: MathUtils.random(2, 4),
                size: MathUtils.random(1, 2),
                color: color,
                friction: 0.99,
                fadeOut: true
            });

            this.particles.push(particle);
        }
    }

    /**
     * 创建推进器尾焰效果
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {number} angle - 角度
     * @param {Object} options - 配置选项
     */
    createThrusterFlame(x, y, angle, options = {}) {
        const particleCount = options.count || 3;
        const colors = options.colors || ['#ff6b6b', '#ffa500', '#ffff00'];

        for (let i = 0; i < particleCount; i++) {
            const flameAngle = angle + MathUtils.random(-0.3, 0.3);
            const velocity = MathUtils.random(100, 200);
            const color = colors[Math.floor(Math.random() * colors.length)];

            const particle = new Particle(x, y, {
                vx: Math.cos(flameAngle) * velocity,
                vy: Math.sin(flameAngle) * velocity,
                life: MathUtils.random(0.3, 0.6),
                size: MathUtils.random(2, 5),
                color: color,
                friction: 0.95,
                shrink: true
            });

            this.particles.push(particle);
        }
    }

    /**
     * 创建挖掘效果
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {string} mineralColor - 矿物颜色
     */
    createMiningEffect(x, y, mineralColor) {
        const particleCount = 5;

        for (let i = 0; i < particleCount; i++) {
            const angle = MathUtils.random(0, Math.PI * 2);
            const velocity = MathUtils.random(50, 120);

            const particle = new Particle(x, y, {
                vx: Math.cos(angle) * velocity,
                vy: Math.sin(angle) * velocity,
                life: MathUtils.random(0.5, 1),
                size: MathUtils.random(1, 3),
                color: mineralColor,
                gravity: 80,
                friction: 0.96
            });

            this.particles.push(particle);
        }
    }

    /**
     * 更新所有粒子
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        // 更新粒子
        this.particles.forEach(particle => particle.update(deltaTime));

        // 移除死亡的粒子
        this.particles = this.particles.filter(particle => particle.active);

        // 更新发射器
        this.emitters.forEach(emitter => emitter.update(deltaTime));
    }

    /**
     * 渲染所有粒子
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    render(ctx) {
        this.particles.forEach(particle => particle.render(ctx));
    }

    /**
     * 清除所有粒子
     */
    clear() {
        this.particles = [];
        this.emitters = [];
    }

    /**
     * 获取粒子数量
     * @returns {number} 当前粒子数量
     */
    getParticleCount() {
        return this.particles.length;
    }
}

// 粒子发射器类
class ParticleEmitter {
    constructor(x, y, options = {}) {
        this.x = x;
        this.y = y;
        this.active = true;
        this.emissionRate = options.emissionRate || 10; // 每秒发射粒子数
        this.particleLife = options.particleLife || 1;
        this.particleSpeed = options.particleSpeed || 100;
        this.particleSize = options.particleSize || 2;
        this.particleColor = options.particleColor || '#ffffff';
        this.emissionAngle = options.emissionAngle || 0;
        this.emissionSpread = options.emissionSpread || Math.PI * 2;
        this.gravity = options.gravity || 0;
        this.friction = options.friction || 1;
        
        this.timeSinceLastEmission = 0;
        this.particles = [];
    }

    /**
     * 更新发射器
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        if (!this.active) return;

        this.timeSinceLastEmission += deltaTime;
        const emissionInterval = 1 / this.emissionRate;

        // 发射新粒子
        while (this.timeSinceLastEmission >= emissionInterval) {
            this.emitParticle();
            this.timeSinceLastEmission -= emissionInterval;
        }

        // 更新粒子
        this.particles.forEach(particle => particle.update(deltaTime));
        this.particles = this.particles.filter(particle => particle.active);
    }

    /**
     * 发射单个粒子
     */
    emitParticle() {
        const angle = this.emissionAngle + MathUtils.random(-this.emissionSpread / 2, this.emissionSpread / 2);
        const speed = this.particleSpeed + MathUtils.random(-this.particleSpeed * 0.2, this.particleSpeed * 0.2);

        const particle = new Particle(this.x, this.y, {
            vx: Math.cos(angle) * speed,
            vy: Math.sin(angle) * speed,
            life: this.particleLife + MathUtils.random(-this.particleLife * 0.2, this.particleLife * 0.2),
            size: this.particleSize + MathUtils.random(-1, 1),
            color: this.particleColor,
            gravity: this.gravity,
            friction: this.friction
        });

        this.particles.push(particle);
    }

    /**
     * 渲染发射器的粒子
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    render(ctx) {
        this.particles.forEach(particle => particle.render(ctx));
    }

    /**
     * 停止发射
     */
    stop() {
        this.active = false;
    }

    /**
     * 开始发射
     */
    start() {
        this.active = true;
    }

    /**
     * 设置位置
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     */
    setPosition(x, y) {
        this.x = x;
        this.y = y;
    }
}

// 创建全局粒子系统实例
const particleSystem = new ParticleSystem();
