/**
 * 输入管理系统
 * 处理键盘、鼠标和触摸输入
 */

class InputManager {
    constructor() {
        // 键盘状态
        this.keys = {};
        this.keysPressed = {};
        this.keysReleased = {};
        
        // 鼠标状态
        this.mouse = {
            x: 0,
            y: 0,
            buttons: {},
            buttonsPressed: {},
            buttonsReleased: {},
            wheel: 0
        };
        
        // 触摸状态
        this.touches = [];
        this.touchStarted = [];
        this.touchEnded = [];
        
        // 虚拟摇杆状态（移动端）
        this.joystick = {
            active: false,
            x: 0,
            y: 0,
            centerX: 0,
            centerY: 0,
            radius: 60,
            deadZone: 0.2
        };
        
        // 游戏输入状态
        this.gameInput = {
            left: false,
            right: false,
            up: false,
            down: false,
            mine: false,
            boost: false,
            menu: false,
            pause: false
        };
        
        this.init();
    }

    /**
     * 初始化输入系统
     */
    init() {
        this.setupKeyboardEvents();
        this.setupMouseEvents();
        this.setupTouchEvents();
        this.setupVirtualJoystick();
    }

    /**
     * 设置键盘事件监听
     */
    setupKeyboardEvents() {
        document.addEventListener('keydown', (e) => {
            if (!this.keys[e.code]) {
                this.keysPressed[e.code] = true;
            }
            this.keys[e.code] = true;
            
            // 阻止某些默认行为
            if (['Space', 'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(e.code)) {
                e.preventDefault();
            }
        });

        document.addEventListener('keyup', (e) => {
            this.keys[e.code] = false;
            this.keysReleased[e.code] = true;
        });
    }

    /**
     * 设置鼠标事件监听
     */
    setupMouseEvents() {
        document.addEventListener('mousemove', (e) => {
            const rect = document.getElementById('game-canvas').getBoundingClientRect();
            this.mouse.x = e.clientX - rect.left;
            this.mouse.y = e.clientY - rect.top;
        });

        document.addEventListener('mousedown', (e) => {
            if (!this.mouse.buttons[e.button]) {
                this.mouse.buttonsPressed[e.button] = true;
            }
            this.mouse.buttons[e.button] = true;
        });

        document.addEventListener('mouseup', (e) => {
            this.mouse.buttons[e.button] = false;
            this.mouse.buttonsReleased[e.button] = true;
        });

        document.addEventListener('wheel', (e) => {
            this.mouse.wheel = e.deltaY;
            e.preventDefault();
        });
    }

    /**
     * 设置触摸事件监听
     */
    setupTouchEvents() {
        const canvas = document.getElementById('game-canvas');
        
        canvas.addEventListener('touchstart', (e) => {
            e.preventDefault();
            this.updateTouches(e.touches);
            
            // 记录新的触摸点
            for (let i = 0; i < e.changedTouches.length; i++) {
                const touch = e.changedTouches[i];
                this.touchStarted.push({
                    id: touch.identifier,
                    x: touch.clientX,
                    y: touch.clientY
                });
            }
        });

        canvas.addEventListener('touchmove', (e) => {
            e.preventDefault();
            this.updateTouches(e.touches);
        });

        canvas.addEventListener('touchend', (e) => {
            e.preventDefault();
            this.updateTouches(e.touches);
            
            // 记录结束的触摸点
            for (let i = 0; i < e.changedTouches.length; i++) {
                const touch = e.changedTouches[i];
                this.touchEnded.push({
                    id: touch.identifier,
                    x: touch.clientX,
                    y: touch.clientY
                });
            }
        });
    }

    /**
     * 更新触摸状态
     * @param {TouchList} touches - 触摸列表
     */
    updateTouches(touches) {
        const rect = document.getElementById('game-canvas').getBoundingClientRect();
        this.touches = [];
        
        for (let i = 0; i < touches.length; i++) {
            const touch = touches[i];
            this.touches.push({
                id: touch.identifier,
                x: touch.clientX - rect.left,
                y: touch.clientY - rect.top
            });
        }
    }

    /**
     * 设置虚拟摇杆
     */
    setupVirtualJoystick() {
        const joystickContainer = document.getElementById('joystick-container');
        const joystickBase = document.getElementById('joystick-base');
        const joystickStick = document.getElementById('joystick-stick');
        
        if (!joystickContainer) return;
        
        const rect = joystickContainer.getBoundingClientRect();
        this.joystick.centerX = rect.left + rect.width / 2;
        this.joystick.centerY = rect.top + rect.height / 2;
        
        // 触摸开始
        joystickContainer.addEventListener('touchstart', (e) => {
            e.preventDefault();
            this.joystick.active = true;
            this.updateJoystick(e.touches[0]);
        });
        
        // 触摸移动
        joystickContainer.addEventListener('touchmove', (e) => {
            e.preventDefault();
            if (this.joystick.active) {
                this.updateJoystick(e.touches[0]);
            }
        });
        
        // 触摸结束
        joystickContainer.addEventListener('touchend', (e) => {
            e.preventDefault();
            this.joystick.active = false;
            this.joystick.x = 0;
            this.joystick.y = 0;
            
            // 重置摇杆位置
            joystickStick.style.transform = 'translate(-50%, -50%)';
        });
    }

    /**
     * 更新虚拟摇杆状态
     * @param {Touch} touch - 触摸点
     */
    updateJoystick(touch) {
        const dx = touch.clientX - this.joystick.centerX;
        const dy = touch.clientY - this.joystick.centerY;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance <= this.joystick.radius) {
            this.joystick.x = dx / this.joystick.radius;
            this.joystick.y = dy / this.joystick.radius;
        } else {
            this.joystick.x = dx / distance;
            this.joystick.y = dy / distance;
        }
        
        // 应用死区
        if (Math.abs(this.joystick.x) < this.joystick.deadZone) this.joystick.x = 0;
        if (Math.abs(this.joystick.y) < this.joystick.deadZone) this.joystick.y = 0;
        
        // 更新摇杆视觉位置
        const joystickStick = document.getElementById('joystick-stick');
        if (joystickStick) {
            const visualX = this.joystick.x * (this.joystick.radius * 0.8);
            const visualY = this.joystick.y * (this.joystick.radius * 0.8);
            joystickStick.style.transform = `translate(calc(-50% + ${visualX}px), calc(-50% + ${visualY}px))`;
        }
    }

    /**
     * 更新游戏输入状态
     */
    updateGameInput() {
        // 键盘输入
        this.gameInput.left = this.keys['ArrowLeft'] || this.keys['KeyA'];
        this.gameInput.right = this.keys['ArrowRight'] || this.keys['KeyD'];
        this.gameInput.up = this.keys['ArrowUp'] || this.keys['KeyW'];
        this.gameInput.down = this.keys['ArrowDown'] || this.keys['KeyS'];
        this.gameInput.mine = this.keys['Space'] || this.mouse.buttons[0];
        this.gameInput.boost = this.keys['ShiftLeft'] || this.keys['ShiftRight'];
        this.gameInput.menu = this.keysPressed['KeyM'] || this.keysPressed['Escape'];
        this.gameInput.pause = this.keysPressed['KeyP'];
        
        // 虚拟摇杆输入（移动端）
        if (this.joystick.active) {
            this.gameInput.left = this.joystick.x < -0.3;
            this.gameInput.right = this.joystick.x > 0.3;
            this.gameInput.up = this.joystick.y < -0.3;
            this.gameInput.down = this.joystick.y > 0.3;
        }
        
        // 移动端按钮输入
        const mobileMineBtnPressed = this.isMobileButtonPressed('mobile-mine-btn');
        const mobileBoostBtnPressed = this.isMobileButtonPressed('mobile-boost-btn');
        
        if (mobileMineBtnPressed) this.gameInput.mine = true;
        if (mobileBoostBtnPressed) this.gameInput.boost = true;
    }

    /**
     * 检查移动端按钮是否被按下
     * @param {string} buttonId - 按钮ID
     * @returns {boolean} 是否被按下
     */
    isMobileButtonPressed(buttonId) {
        const button = document.getElementById(buttonId);
        if (!button) return false;
        
        const rect = button.getBoundingClientRect();
        
        return this.touches.some(touch => 
            touch.x >= rect.left && touch.x <= rect.right &&
            touch.y >= rect.top && touch.y <= rect.bottom
        );
    }

    /**
     * 检查键是否刚被按下
     * @param {string} keyCode - 键码
     * @returns {boolean} 是否刚被按下
     */
    isKeyPressed(keyCode) {
        return this.keysPressed[keyCode] || false;
    }

    /**
     * 检查键是否刚被释放
     * @param {string} keyCode - 键码
     * @returns {boolean} 是否刚被释放
     */
    isKeyReleased(keyCode) {
        return this.keysReleased[keyCode] || false;
    }

    /**
     * 检查鼠标按钮是否刚被按下
     * @param {number} button - 鼠标按钮 (0=左键, 1=中键, 2=右键)
     * @returns {boolean} 是否刚被按下
     */
    isMousePressed(button) {
        return this.mouse.buttonsPressed[button] || false;
    }

    /**
     * 检查鼠标按钮是否刚被释放
     * @param {number} button - 鼠标按钮
     * @returns {boolean} 是否刚被释放
     */
    isMouseReleased(button) {
        return this.mouse.buttonsReleased[button] || false;
    }

    /**
     * 获取鼠标位置
     * @returns {Object} 鼠标坐标
     */
    getMousePosition() {
        return { x: this.mouse.x, y: this.mouse.y };
    }

    /**
     * 获取虚拟摇杆输入
     * @returns {Object} 摇杆输入值
     */
    getJoystickInput() {
        return {
            x: this.joystick.x,
            y: this.joystick.y,
            active: this.joystick.active
        };
    }

    /**
     * 清除单帧输入状态
     */
    clearFrameInput() {
        this.keysPressed = {};
        this.keysReleased = {};
        this.mouse.buttonsPressed = {};
        this.mouse.buttonsReleased = {};
        this.mouse.wheel = 0;
        this.touchStarted = [];
        this.touchEnded = [];
    }

    /**
     * 重置所有输入状态
     */
    reset() {
        this.keys = {};
        this.keysPressed = {};
        this.keysReleased = {};
        this.mouse.buttons = {};
        this.mouse.buttonsPressed = {};
        this.mouse.buttonsReleased = {};
        this.touches = [];
        this.touchStarted = [];
        this.touchEnded = [];
        this.joystick.active = false;
        this.joystick.x = 0;
        this.joystick.y = 0;
    }
}

// 创建全局输入管理器实例
const inputManager = new InputManager();
