<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#1a1a2e">
    <title>时空织梦者 - Time Weaver</title>
    
    <!-- PWA 支持 -->
    <link rel="manifest" href="manifest.json">
    <link rel="icon" type="image/png" href="assets/images/icon-192.png">
    
    <!-- 预加载关键资源 -->
    <link rel="preload" href="css/main.css" as="style">
    <link rel="preload" href="js/game.js" as="script">
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/responsive.css">
</head>
<body>
    <!-- 加载屏幕 -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="time-weaver-logo">
                <div class="logo-circle"></div>
                <div class="logo-text">时空织梦者</div>
            </div>
            <div class="loading-bar">
                <div class="loading-progress"></div>
            </div>
            <div class="loading-text">正在编织时空...</div>
        </div>
        <div class="loading-particles"></div>
    </div>

    <!-- 主游戏容器 -->
    <div id="game-container" class="game-container">
        <!-- 游戏标题栏 -->
        <header class="game-header">
            <div class="header-left">
                <h1 class="game-title">时空织梦者</h1>
                <div class="level-info">
                    <span class="level-text">关卡 <span id="current-level">1</span></span>
                </div>
            </div>
            <div class="header-right">
                <div class="time-energy">
                    <div class="energy-bar">
                        <div class="energy-fill" id="energy-fill"></div>
                    </div>
                    <span class="energy-text" id="energy-text">100</span>
                </div>
                <button class="menu-btn" id="menu-btn">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </header>

        <!-- 时间线面板 -->
        <div class="timeline-panel" id="timeline-panel">
            <div class="timeline-header">
                <h3>时间线控制</h3>
                <div class="timeline-controls">
                    <button class="timeline-btn" id="past-btn" data-time="past">过去</button>
                    <button class="timeline-btn active" id="present-btn" data-time="present">现在</button>
                    <button class="timeline-btn" id="future-btn" data-time="future">未来</button>
                </div>
            </div>
            <div class="timeline-slider">
                <input type="range" id="time-slider" min="0" max="100" value="50" class="time-range">
                <div class="time-markers">
                    <span class="marker past">过去</span>
                    <span class="marker present">现在</span>
                    <span class="marker future">未来</span>
                </div>
            </div>
        </div>

        <!-- 主游戏画布 -->
        <main class="game-main">
            <canvas id="game-canvas" class="game-canvas"></canvas>
            
            <!-- 时间锚点工具栏 -->
            <div class="anchor-toolbar" id="anchor-toolbar">
                <div class="toolbar-title">时间锚点</div>
                <div class="anchor-tools">
                    <button class="anchor-btn" id="create-anchor" data-type="create">
                        <span class="anchor-icon">⚓</span>
                        <span class="anchor-text">创建锚点</span>
                    </button>
                    <button class="anchor-btn" id="link-anchor" data-type="link">
                        <span class="anchor-icon">🔗</span>
                        <span class="anchor-text">连接锚点</span>
                    </button>
                    <button class="anchor-btn" id="remove-anchor" data-type="remove">
                        <span class="anchor-icon">❌</span>
                        <span class="anchor-text">移除锚点</span>
                    </button>
                </div>
            </div>

            <!-- 游戏信息面板 -->
            <div class="info-panel" id="info-panel">
                <div class="panel-section">
                    <h4>目标</h4>
                    <p id="level-objective">通过操控时间线，让所有能量球到达目标点</p>
                </div>
                <div class="panel-section">
                    <h4>统计</h4>
                    <div class="stats">
                        <div class="stat-item">
                            <span class="stat-label">锚点数量:</span>
                            <span class="stat-value" id="anchor-count">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">时间能量:</span>
                            <span class="stat-value" id="time-energy">100</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">完成度:</span>
                            <span class="stat-value" id="completion">0%</span>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- 移动端控制面板 -->
        <div class="mobile-controls" id="mobile-controls">
            <div class="control-row">
                <button class="mobile-btn" id="mobile-past">过去</button>
                <button class="mobile-btn" id="mobile-present">现在</button>
                <button class="mobile-btn" id="mobile-future">未来</button>
            </div>
            <div class="control-row">
                <button class="mobile-btn" id="mobile-anchor">锚点</button>
                <button class="mobile-btn" id="mobile-link">连接</button>
                <button class="mobile-btn" id="mobile-reset">重置</button>
            </div>
        </div>
    </div>

    <!-- 游戏菜单 -->
    <div id="game-menu" class="game-menu">
        <div class="menu-content">
            <h2>游戏菜单</h2>
            <div class="menu-buttons">
                <button class="menu-button" id="resume-btn">继续游戏</button>
                <button class="menu-button" id="restart-btn">重新开始</button>
                <button class="menu-button" id="settings-btn">设置</button>
                <button class="menu-button" id="help-btn">帮助</button>
                <button class="menu-button" id="save-btn">保存游戏</button>
                <button class="menu-button" id="load-btn">载入游戏</button>
            </div>
        </div>
    </div>

    <!-- 设置面板 -->
    <div id="settings-panel" class="settings-panel">
        <div class="settings-content">
            <h3>游戏设置</h3>
            <div class="setting-group">
                <label>音效音量</label>
                <input type="range" id="sfx-volume" min="0" max="100" value="70">
            </div>
            <div class="setting-group">
                <label>背景音乐</label>
                <input type="range" id="bgm-volume" min="0" max="100" value="50">
            </div>
            <div class="setting-group">
                <label>粒子效果</label>
                <select id="particle-quality">
                    <option value="high">高质量</option>
                    <option value="medium" selected>中等</option>
                    <option value="low">低质量</option>
                </select>
            </div>
            <div class="setting-group">
                <label>自动保存</label>
                <input type="checkbox" id="auto-save" checked>
            </div>
            <button class="close-btn" id="close-settings">关闭</button>
        </div>
    </div>

    <!-- 帮助面板 -->
    <div id="help-panel" class="help-panel">
        <div class="help-content">
            <h3>游戏帮助</h3>
            <div class="help-section">
                <h4>游戏目标</h4>
                <p>作为时空织梦者，你需要通过操控时间线来解决各种时空谜题。</p>
            </div>
            <div class="help-section">
                <h4>基本操作</h4>
                <ul>
                    <li>点击时间线按钮切换不同的时间状态</li>
                    <li>拖拽时间滑块精确控制时间流</li>
                    <li>放置时间锚点来固定重要的时间节点</li>
                    <li>连接锚点创建因果关系链</li>
                </ul>
            </div>
            <div class="help-section">
                <h4>高级技巧</h4>
                <ul>
                    <li>合理使用时间能量，避免过度消耗</li>
                    <li>观察物体的运动轨迹，预测未来状态</li>
                    <li>利用因果关系创造连锁反应</li>
                </ul>
            </div>
            <button class="close-btn" id="close-help">关闭</button>
        </div>
    </div>

    <!-- 胜利面板 -->
    <div id="victory-panel" class="victory-panel">
        <div class="victory-content">
            <h2>关卡完成！</h2>
            <div class="victory-stats">
                <div class="stat">
                    <span class="stat-label">用时:</span>
                    <span class="stat-value" id="completion-time">--</span>
                </div>
                <div class="stat">
                    <span class="stat-label">锚点使用:</span>
                    <span class="stat-value" id="anchors-used">--</span>
                </div>
                <div class="stat">
                    <span class="stat-label">评分:</span>
                    <span class="stat-value" id="level-score">--</span>
                </div>
            </div>
            <div class="victory-buttons">
                <button class="victory-btn" id="next-level">下一关</button>
                <button class="victory-btn" id="replay-level">重玩</button>
                <button class="victory-btn" id="back-menu">返回菜单</button>
            </div>
        </div>
    </div>

    <!-- 脚本文件 -->
    <script src="js/utils.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/particles.js"></script>
    <script src="js/timeline.js"></script>
    <script src="js/anchors.js"></script>
    <script src="js/physics.js"></script>
    <script src="js/levels.js"></script>
    <script src="js/audio.js"></script>
    <script src="js/game.js"></script>

    <!-- Service Worker 注册 -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('./sw.js')
                    .then(registration => {
                        console.log('Service Worker 注册成功:', registration.scope);
                    })
                    .catch(error => {
                        console.log('Service Worker 注册失败:', error);
                    });
            });
        }
    </script>
</body>
</html>
