/* 时空织梦者 - 动画效果样式 */

/* 时空粒子效果 */
.time-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--accent-color);
    border-radius: 50%;
    pointer-events: none;
    animation: timeParticle 3s linear infinite;
}

.time-particle.past {
    background: var(--warning-color);
    animation-duration: 4s;
}

.time-particle.future {
    background: var(--primary-color);
    animation-duration: 2s;
}

@keyframes timeParticle {
    0% {
        opacity: 0;
        transform: scale(0) rotate(0deg);
    }
    10% {
        opacity: 1;
        transform: scale(1) rotate(36deg);
    }
    90% {
        opacity: 1;
        transform: scale(1) rotate(324deg);
    }
    100% {
        opacity: 0;
        transform: scale(0) rotate(360deg);
    }
}

/* 时间锚点动画 */
.time-anchor {
    position: absolute;
    width: 20px;
    height: 20px;
    border: 2px solid var(--accent-color);
    border-radius: 50%;
    background: rgba(6, 182, 212, 0.2);
    cursor: pointer;
    transition: all var(--transition-normal);
    animation: anchorPulse 2s ease-in-out infinite;
}

.time-anchor:hover {
    transform: scale(1.2);
    box-shadow: 0 0 20px var(--accent-color);
}

.time-anchor.active {
    border-color: var(--success-color);
    background: rgba(16, 185, 129, 0.3);
    animation: anchorActive 1s ease-in-out infinite;
}

.time-anchor.past {
    border-color: var(--warning-color);
    background: rgba(245, 158, 11, 0.2);
}

.time-anchor.future {
    border-color: var(--primary-color);
    background: rgba(99, 102, 241, 0.2);
}

@keyframes anchorPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
}

@keyframes anchorActive {
    0%, 100% {
        box-shadow: 0 0 10px var(--success-color);
    }
    50% {
        box-shadow: 0 0 25px var(--success-color);
    }
}

/* 时间连接线动画 */
.time-connection {
    position: absolute;
    height: 2px;
    background: linear-gradient(90deg, 
        var(--accent-color) 0%, 
        transparent 50%, 
        var(--accent-color) 100%);
    transform-origin: left center;
    animation: connectionFlow 2s linear infinite;
}

.time-connection.past {
    background: linear-gradient(90deg, 
        var(--warning-color) 0%, 
        transparent 50%, 
        var(--warning-color) 100%);
}

.time-connection.future {
    background: linear-gradient(90deg, 
        var(--primary-color) 0%, 
        transparent 50%, 
        var(--primary-color) 100%);
}

@keyframes connectionFlow {
    0% {
        background-position: -100% 0;
    }
    100% {
        background-position: 100% 0;
    }
}

/* 能量球动画 */
.energy-orb {
    position: absolute;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: radial-gradient(circle, var(--accent-color) 0%, transparent 70%);
    box-shadow: 0 0 15px var(--accent-color);
    animation: orbFloat 3s ease-in-out infinite;
}

.energy-orb.moving {
    animation: orbMove 1s linear;
}

.energy-orb.collected {
    animation: orbCollect 0.5s ease-out forwards;
}

@keyframes orbFloat {
    0%, 100% {
        transform: translateY(0px) scale(1);
    }
    50% {
        transform: translateY(-5px) scale(1.1);
    }
}

@keyframes orbMove {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.3);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes orbCollect {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(2);
        opacity: 0.5;
    }
    100% {
        transform: scale(0);
        opacity: 0;
    }
}

/* 时间波纹效果 */
.time-ripple {
    position: absolute;
    border: 2px solid var(--accent-color);
    border-radius: 50%;
    pointer-events: none;
    animation: rippleExpand 1s ease-out forwards;
}

@keyframes rippleExpand {
    0% {
        width: 0;
        height: 0;
        opacity: 1;
    }
    100% {
        width: 100px;
        height: 100px;
        opacity: 0;
    }
}

/* 菜单动画 */
.game-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-overlay);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.game-menu.active {
    opacity: 1;
    visibility: visible;
}

.menu-content {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-lg);
    transform: scale(0.9) translateY(20px);
    transition: transform var(--transition-normal);
}

.game-menu.active .menu-content {
    transform: scale(1) translateY(0);
}

.menu-content h2 {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    font-size: var(--text-2xl);
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.menu-buttons {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    min-width: 200px;
}

.menu-button {
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: var(--text-base);
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.menu-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left var(--transition-normal);
}

.menu-button:hover::before {
    left: 100%;
}

.menu-button:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* 设置和帮助面板动画 */
.settings-panel,
.help-panel {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-overlay);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1001;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.settings-panel.active,
.help-panel.active {
    opacity: 1;
    visibility: visible;
}

.settings-content,
.help-content {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-lg);
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
    transform: scale(0.9) translateY(20px);
    transition: transform var(--transition-normal);
}

.settings-panel.active .settings-content,
.help-panel.active .help-content {
    transform: scale(1) translateY(0);
}

/* 胜利面板动画 */
.victory-panel {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-overlay);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1002;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.victory-panel.active {
    opacity: 1;
    visibility: visible;
}

.victory-content {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-lg);
    text-align: center;
    transform: scale(0.8) rotateY(90deg);
    transition: transform var(--transition-slow);
}

.victory-panel.active .victory-content {
    transform: scale(1) rotateY(0deg);
}

.victory-content h2 {
    font-size: var(--text-3xl);
    margin-bottom: var(--spacing-xl);
    background: linear-gradient(45deg, var(--success-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: victoryGlow 2s ease-in-out infinite;
}

@keyframes victoryGlow {
    0%, 100% {
        filter: brightness(1);
    }
    50% {
        filter: brightness(1.2);
    }
}

.victory-stats {
    display: flex;
    justify-content: space-around;
    margin-bottom: var(--spacing-xl);
    gap: var(--spacing-lg);
}

.victory-stats .stat {
    text-align: center;
}

.victory-stats .stat-label {
    display: block;
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.victory-stats .stat-value {
    display: block;
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--text-primary);
}

.victory-buttons {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
}

.victory-btn {
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: var(--text-base);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.victory-btn:hover {
    background: var(--success-color);
    border-color: var(--success-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* 响应式动画调整 */
@media (max-width: 768px) {
    .menu-content,
    .settings-content,
    .help-content,
    .victory-content {
        margin: var(--spacing-md);
        padding: var(--spacing-lg);
        max-width: calc(100% - 2rem);
    }
    
    .victory-stats {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .victory-buttons {
        flex-direction: column;
    }
}

/* 减少动画效果（用户偏好） */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
