/* 时空织梦者 - 响应式设计样式 */

/* 大屏幕设备 (1200px+) */
@media (min-width: 1200px) {
    .game-container {
        max-width: 1400px;
        margin: 0 auto;
    }
    
    .anchor-toolbar {
        right: var(--spacing-xl);
        top: var(--spacing-xl);
        min-width: 250px;
    }
    
    .info-panel {
        left: var(--spacing-xl);
        bottom: var(--spacing-xl);
        min-width: 300px;
    }
    
    .timeline-panel {
        padding: var(--spacing-lg) var(--spacing-xl);
    }
    
    .game-header {
        padding: var(--spacing-lg) var(--spacing-xl);
    }
}

/* 中等屏幕设备 (768px - 1199px) */
@media (min-width: 768px) and (max-width: 1199px) {
    .game-title {
        font-size: var(--text-lg);
    }
    
    .anchor-toolbar {
        min-width: 180px;
    }
    
    .info-panel {
        min-width: 220px;
        max-width: 280px;
    }
    
    .timeline-controls {
        gap: var(--spacing-xs);
    }
    
    .timeline-btn {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--text-xs);
    }
}

/* 小屏幕设备 (481px - 767px) */
@media (min-width: 481px) and (max-width: 767px) {
    .game-header {
        padding: var(--spacing-sm) var(--spacing-md);
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }
    
    .header-left,
    .header-right {
        flex: 1;
        min-width: 200px;
    }
    
    .game-title {
        font-size: var(--text-base);
    }
    
    .timeline-panel {
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    .timeline-header {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: stretch;
    }
    
    .timeline-controls {
        justify-content: center;
    }
    
    .anchor-toolbar {
        position: relative;
        top: auto;
        right: auto;
        margin: var(--spacing-sm);
        min-width: auto;
        order: 1;
    }
    
    .info-panel {
        position: relative;
        bottom: auto;
        left: auto;
        margin: var(--spacing-sm);
        min-width: auto;
        max-width: none;
        order: 2;
    }
    
    .game-main {
        flex-direction: column;
    }
    
    .game-canvas {
        min-height: 300px;
        order: 0;
    }
    
    .mobile-controls {
        display: flex;
        order: 3;
    }
    
    .energy-bar {
        width: 80px;
    }
    
    .energy-text {
        font-size: var(--text-xs);
    }
}

/* 超小屏幕设备 (320px - 480px) */
@media (max-width: 480px) {
    :root {
        --spacing-xs: 0.125rem;
        --spacing-sm: 0.25rem;
        --spacing-md: 0.5rem;
        --spacing-lg: 0.75rem;
        --spacing-xl: 1rem;
    }
    
    .game-header {
        padding: var(--spacing-sm);
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .header-left,
    .header-right {
        justify-content: center;
    }
    
    .game-title {
        font-size: var(--text-sm);
        text-align: center;
    }
    
    .level-info {
        justify-content: center;
    }
    
    .time-energy {
        flex-direction: column;
        gap: var(--spacing-xs);
        align-items: center;
    }
    
    .energy-bar {
        width: 60px;
        height: 6px;
    }
    
    .timeline-panel {
        padding: var(--spacing-sm);
    }
    
    .timeline-header h3 {
        font-size: var(--text-base);
        text-align: center;
    }
    
    .timeline-controls {
        justify-content: space-between;
    }
    
    .timeline-btn {
        flex: 1;
        padding: var(--spacing-xs);
        font-size: var(--text-xs);
    }
    
    .anchor-toolbar {
        position: relative;
        top: auto;
        right: auto;
        margin: var(--spacing-sm);
        padding: var(--spacing-sm);
        min-width: auto;
    }
    
    .anchor-tools {
        flex-direction: row;
        flex-wrap: wrap;
    }
    
    .anchor-btn {
        flex: 1;
        min-width: calc(50% - var(--spacing-xs));
        padding: var(--spacing-xs);
        font-size: var(--text-xs);
    }
    
    .anchor-text {
        display: none;
    }
    
    .info-panel {
        position: relative;
        bottom: auto;
        left: auto;
        margin: var(--spacing-sm);
        padding: var(--spacing-sm);
        min-width: auto;
        max-width: none;
    }
    
    .panel-section h4 {
        font-size: var(--text-xs);
    }
    
    .panel-section p {
        font-size: var(--text-xs);
    }
    
    .stat-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }
    
    .game-main {
        flex-direction: column;
    }
    
    .game-canvas {
        min-height: 250px;
    }
    
    .mobile-controls {
        display: flex;
        padding: var(--spacing-sm);
    }
    
    .mobile-btn {
        padding: var(--spacing-sm);
        font-size: var(--text-xs);
    }
    
    /* 菜单适配 */
    .menu-content {
        margin: var(--spacing-sm);
        padding: var(--spacing-md);
        max-width: calc(100% - 1rem);
    }
    
    .menu-content h2 {
        font-size: var(--text-xl);
        margin-bottom: var(--spacing-md);
    }
    
    .menu-buttons {
        min-width: auto;
    }
    
    .menu-button {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--text-sm);
    }
    
    /* 设置面板适配 */
    .settings-content,
    .help-content {
        margin: var(--spacing-sm);
        padding: var(--spacing-md);
        max-width: calc(100% - 1rem);
        max-height: calc(100vh - 2rem);
    }
    
    .settings-content h3,
    .help-content h3 {
        font-size: var(--text-lg);
        margin-bottom: var(--spacing-md);
    }
    
    .setting-group {
        margin-bottom: var(--spacing-sm);
    }
    
    .setting-group label {
        font-size: var(--text-sm);
        display: block;
        margin-bottom: var(--spacing-xs);
    }
    
    .help-section {
        margin-bottom: var(--spacing-md);
    }
    
    .help-section h4 {
        font-size: var(--text-base);
        margin-bottom: var(--spacing-sm);
    }
    
    .help-section p,
    .help-section li {
        font-size: var(--text-sm);
        line-height: 1.4;
    }
    
    /* 胜利面板适配 */
    .victory-content {
        margin: var(--spacing-sm);
        padding: var(--spacing-md);
        max-width: calc(100% - 1rem);
    }
    
    .victory-content h2 {
        font-size: var(--text-xl);
        margin-bottom: var(--spacing-md);
    }
    
    .victory-stats {
        flex-direction: column;
        gap: var(--spacing-sm);
        margin-bottom: var(--spacing-md);
    }
    
    .victory-stats .stat-label {
        font-size: var(--text-xs);
    }
    
    .victory-stats .stat-value {
        font-size: var(--text-lg);
    }
    
    .victory-buttons {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .victory-btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--text-sm);
    }
}

/* 横屏模式优化 */
@media (max-height: 500px) and (orientation: landscape) {
    .game-header {
        padding: var(--spacing-xs) var(--spacing-md);
    }
    
    .timeline-panel {
        padding: var(--spacing-xs) var(--spacing-md);
    }
    
    .timeline-header {
        margin-bottom: var(--spacing-xs);
    }
    
    .anchor-toolbar,
    .info-panel {
        max-height: 200px;
        overflow-y: auto;
    }
    
    .mobile-controls {
        padding: var(--spacing-xs);
    }
    
    .mobile-btn {
        padding: var(--spacing-xs) var(--spacing-sm);
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .timeline-btn,
    .anchor-btn,
    .mobile-btn,
    .menu-button,
    .victory-btn {
        min-height: 44px; /* iOS 推荐的最小触摸目标 */
        min-width: 44px;
    }
    
    .time-range::-webkit-slider-thumb {
        width: 24px;
        height: 24px;
    }
    
    .menu-btn {
        padding: var(--spacing-md);
    }
    
    .menu-btn span {
        width: 24px;
        height: 3px;
    }
    
    /* 移除悬停效果 */
    .timeline-btn:hover,
    .anchor-btn:hover,
    .mobile-btn:hover,
    .menu-button:hover,
    .victory-btn:hover {
        transform: none;
    }
    
    /* 添加触摸反馈 */
    .timeline-btn:active,
    .anchor-btn:active,
    .mobile-btn:active,
    .menu-button:active,
    .victory-btn:active {
        transform: scale(0.95);
        opacity: 0.8;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    :root {
        --border-color: #ffffff;
        --text-secondary: #ffffff;
        --text-muted: #cccccc;
    }
    
    .timeline-btn,
    .anchor-btn,
    .mobile-btn {
        border-width: 2px;
    }
    
    .time-anchor {
        border-width: 3px;
    }
}

/* 打印样式 */
@media print {
    .game-container {
        display: none;
    }
    
    body::after {
        content: "时空织梦者是一个交互式网页游戏，无法打印。请在浏览器中体验游戏。";
        display: block;
        text-align: center;
        padding: 2rem;
        font-size: 1.2rem;
    }
}
