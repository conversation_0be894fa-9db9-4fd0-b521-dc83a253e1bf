/* 时空织梦者 - 主样式文件 */

/* CSS 变量定义 */
:root {
    /* 主题颜色 */
    --primary-color: #6366f1;
    --secondary-color: #8b5cf6;
    --accent-color: #06b6d4;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    
    /* 背景颜色 */
    --bg-primary: #0f0f23;
    --bg-secondary: #1a1a2e;
    --bg-tertiary: #16213e;
    --bg-overlay: rgba(15, 15, 35, 0.9);
    
    /* 文字颜色 */
    --text-primary: #ffffff;
    --text-secondary: #a1a1aa;
    --text-muted: #71717a;
    
    /* 边框和阴影 */
    --border-color: #374151;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-glow: 0 0 20px rgba(99, 102, 241, 0.3);
    
    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    
    /* 字体大小 */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    
    /* 过渡效果 */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    overflow: hidden;
    user-select: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none;
}

/* 加载屏幕 */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity var(--transition-slow), visibility var(--transition-slow);
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-content {
    text-align: center;
    z-index: 2;
}

.time-weaver-logo {
    margin-bottom: var(--spacing-xl);
}

.logo-circle {
    width: 80px;
    height: 80px;
    border: 3px solid var(--primary-color);
    border-radius: 50%;
    margin: 0 auto var(--spacing-md);
    position: relative;
    animation: rotate 2s linear infinite;
}

.logo-circle::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    background: var(--accent-color);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: pulse 1.5s ease-in-out infinite;
}

.logo-text {
    font-size: var(--text-3xl);
    font-weight: 700;
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.loading-bar {
    width: 200px;
    height: 4px;
    background: var(--bg-tertiary);
    border-radius: 2px;
    margin: var(--spacing-lg) auto;
    overflow: hidden;
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 2px;
    animation: loading 2s ease-in-out infinite;
}

.loading-text {
    color: var(--text-secondary);
    font-size: var(--text-base);
    margin-top: var(--spacing-md);
}

.loading-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

/* 游戏容器 */
.game-container {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: var(--bg-primary);
    position: relative;
}

/* 游戏标题栏 */
.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-md);
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.game-title {
    font-size: var(--text-xl);
    font-weight: 700;
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.level-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.level-text {
    color: var(--text-secondary);
    font-size: var(--text-sm);
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.time-energy {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.energy-bar {
    width: 100px;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.energy-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--success-color), var(--accent-color));
    border-radius: 3px;
    transition: width var(--transition-normal);
    width: 100%;
}

.energy-text {
    color: var(--text-primary);
    font-size: var(--text-sm);
    font-weight: 600;
    min-width: 30px;
}

.menu-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-sm);
    display: flex;
    flex-direction: column;
    gap: 3px;
    transition: transform var(--transition-fast);
}

.menu-btn:hover {
    transform: scale(1.1);
}

.menu-btn span {
    width: 20px;
    height: 2px;
    background: var(--text-primary);
    border-radius: 1px;
    transition: all var(--transition-fast);
}

/* 时间线面板 */
.timeline-panel {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-md) var(--spacing-lg);
    box-shadow: var(--shadow-sm);
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.timeline-header h3 {
    font-size: var(--text-lg);
    color: var(--text-primary);
}

.timeline-controls {
    display: flex;
    gap: var(--spacing-sm);
}

.timeline-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-secondary);
    font-size: var(--text-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.timeline-btn:hover {
    background: var(--bg-primary);
    color: var(--text-primary);
}

.timeline-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    box-shadow: var(--shadow-glow);
}

.timeline-slider {
    position: relative;
}

.time-range {
    width: 100%;
    height: 6px;
    background: var(--bg-tertiary);
    border-radius: 3px;
    outline: none;
    cursor: pointer;
    -webkit-appearance: none;
}

.time-range::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    background: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-fast);
}

.time-range::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    box-shadow: var(--shadow-glow);
}

.time-markers {
    display: flex;
    justify-content: space-between;
    margin-top: var(--spacing-sm);
}

.marker {
    font-size: var(--text-xs);
    color: var(--text-muted);
}

/* 主游戏区域 */
.game-main {
    flex: 1;
    position: relative;
    display: flex;
    overflow: hidden;
}

.game-canvas {
    flex: 1;
    background: radial-gradient(ellipse at center, var(--bg-secondary) 0%, var(--bg-primary) 100%);
    cursor: crosshair;
}

/* 工具栏 */
.anchor-toolbar {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    background: var(--bg-overlay);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: var(--spacing-md);
    min-width: 200px;
    box-shadow: var(--shadow-lg);
}

.toolbar-title {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
    text-align: center;
}

.anchor-tools {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.anchor-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: var(--text-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.anchor-btn:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.anchor-btn.active {
    background: var(--accent-color);
    border-color: var(--accent-color);
    box-shadow: var(--shadow-glow);
}

.anchor-icon {
    font-size: var(--text-base);
}

/* 信息面板 */
.info-panel {
    position: absolute;
    bottom: var(--spacing-md);
    left: var(--spacing-md);
    background: var(--bg-overlay);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: var(--spacing-md);
    min-width: 250px;
    max-width: 300px;
    box-shadow: var(--shadow-lg);
}

.panel-section {
    margin-bottom: var(--spacing-md);
}

.panel-section:last-child {
    margin-bottom: 0;
}

.panel-section h4 {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.panel-section p {
    font-size: var(--text-sm);
    color: var(--text-primary);
    line-height: 1.5;
}

.stats {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stat-label {
    font-size: var(--text-xs);
    color: var(--text-secondary);
}

.stat-value {
    font-size: var(--text-sm);
    color: var(--text-primary);
    font-weight: 600;
}

/* 移动端控制 */
.mobile-controls {
    display: none;
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-md);
    gap: var(--spacing-sm);
}

.control-row {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: space-between;
}

.mobile-btn {
    flex: 1;
    padding: var(--spacing-md);
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: var(--text-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    touch-action: manipulation;
}

.mobile-btn:hover,
.mobile-btn:active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    transform: scale(0.98);
}

/* 动画关键帧 */
@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
    50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.7; }
}

@keyframes loading {
    0% { transform: translateX(-100%); }
    50% { transform: translateX(0%); }
    100% { transform: translateX(100%); }
}

/* 响应式设计预览 */
@media (max-width: 768px) {
    .mobile-controls {
        display: flex;
        flex-direction: column;
    }
    
    .anchor-toolbar {
        position: static;
        margin: var(--spacing-md);
        min-width: auto;
    }
    
    .info-panel {
        position: static;
        margin: var(--spacing-md);
        min-width: auto;
        max-width: none;
    }
    
    .game-main {
        flex-direction: column;
    }
}
