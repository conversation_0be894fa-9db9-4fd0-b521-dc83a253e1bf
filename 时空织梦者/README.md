# 时空织梦者 (Time Weaver)

一款创新的时间操控解谜游戏，玩家扮演时空织梦者，通过操控时间线、放置时间锚点来解决复杂的时空谜题。

## 🎮 游戏特色

### 核心玩法
- **时间操控机制**: 自由切换过去、现在、未来三种时间状态
- **时间锚点系统**: 在关键时间节点放置锚点，创建因果关系链
- **能量管理**: 合理使用时间能量，避免过度消耗
- **物理模拟**: 真实的物理引擎，支持重力、碰撞等效果

### 视觉效果
- **粒子系统**: 丰富的粒子效果，展现时间流动的美感
- **渐变动画**: 平滑的时间切换动画和视觉反馈
- **响应式设计**: 完美适配桌面端和移动端设备
- **主题色彩**: 深邃的时空主题配色方案

### 技术特性
- **纯前端实现**: 基于HTML5 Canvas和JavaScript
- **本地存储**: 游戏进度自动保存到浏览器本地
- **PWA支持**: 可安装为桌面应用，支持离线游戏
- **性能优化**: 空间网格优化、帧率控制、内存管理

## 🎯 游戏目标

通过操控时间线，让能量球到达指定位置，同时完成各种挑战目标：
- 创建指定数量的时间连接
- 保持能量效率在目标范围内
- 在时间限制内完成关卡
- 收集所有能量球
- 最小化锚点浪费

## 🕹️ 操作指南

### 基本操作
- **时间切换**: 点击过去/现在/未来按钮或使用时间滑块
- **创建锚点**: 选择"创建锚点"工具，在画布上点击
- **连接锚点**: 选择"连接锚点"工具，依次点击两个锚点
- **移除锚点**: 选择"移除锚点"工具，点击要删除的锚点

### 移动端操作
- **触摸控制**: 支持触摸操作，优化的移动端界面
- **手势支持**: 滑动、点击、长按等手势操作
- **自适应布局**: 根据屏幕尺寸自动调整界面

### 快捷键
- **空格键**: 暂停/继续游戏
- **Ctrl+R**: 重置当前关卡
- **Esc**: 显示游戏菜单

## 📁 项目结构

```
时空织梦者/
├── index.html              # 主页面文件
├── manifest.json           # PWA配置文件
├── README.md              # 项目说明文档
├── css/                   # 样式文件目录
│   ├── main.css          # 主要样式
│   ├── animations.css    # 动画效果
│   └── responsive.css    # 响应式样式
├── js/                    # JavaScript文件目录
│   ├── utils.js          # 工具函数库
│   ├── storage.js        # 存储管理
│   ├── particles.js      # 粒子系统
│   ├── timeline.js       # 时间线系统
│   ├── anchors.js        # 锚点管理
│   ├── physics.js        # 物理引擎
│   ├── levels.js         # 关卡系统
│   ├── audio.js          # 音频系统
│   └── game.js           # 主游戏引擎
└── assets/               # 资源文件目录
    └── images/           # 图片资源
```

## 🎵 音效系统

游戏包含完整的音效系统，使用Web Audio API实现：

### 音效类型
- **界面音效**: 点击、悬停等交互音效
- **游戏音效**: 锚点创建、连接、移除音效
- **时间音效**: 时间切换、时间倒流音效
- **成功音效**: 关卡完成、得分音效
- **背景音乐**: 环境音乐、紧张音乐、胜利音乐

### 音频特性
- **程序生成**: 所有音效都是程序生成，无需外部音频文件
- **动态调节**: 支持音量调节和音效开关
- **时间相关**: 不同时间状态有不同的音效主题

## 🏆 关卡系统

游戏包含5个精心设计的关卡：

1. **时间初探** - 学习基本操作
2. **时间之流** - 探索三种时间状态
3. **时空网络** - 构建复杂连接网络
4. **时间大师** - 重力环境下的挑战
5. **时空织梦者** - 终极挑战关卡

### 关卡特性
- **渐进难度**: 从简单到复杂的学习曲线
- **多样目标**: 不同类型的完成条件
- **评分系统**: 基于时间、效率、完成度的评分
- **解锁机制**: 完成前置关卡解锁新关卡

## 💾 存储系统

### 本地存储
- **游戏进度**: 关卡完成状态、最佳记录
- **用户设置**: 音量、画质、控制偏好
- **统计数据**: 游戏时间、完成次数等
- **数据验证**: 防止数据损坏和版本兼容

### 数据管理
- **自动保存**: 游戏进度实时保存
- **数据导出**: 支持备份和恢复游戏数据
- **版本控制**: 处理不同版本间的数据兼容

## 🔧 技术实现

### 核心技术
- **HTML5 Canvas**: 游戏渲染和交互
- **Web Audio API**: 音效和音乐系统
- **LocalStorage**: 本地数据存储
- **CSS3 动画**: 界面动画效果
- **ES6+ JavaScript**: 现代JavaScript特性

### 性能优化
- **空间分割**: 碰撞检测优化
- **对象池**: 减少垃圾回收
- **帧率控制**: 稳定的游戏循环
- **资源管理**: 内存使用监控

### 兼容性
- **现代浏览器**: Chrome, Firefox, Safari, Edge
- **移动设备**: iOS Safari, Android Chrome
- **响应式设计**: 适配各种屏幕尺寸
- **PWA支持**: 可安装为原生应用

## 🚀 部署说明

### 静态部署
游戏是纯前端应用，可以部署到任何静态文件服务器：

1. **GitHub Pages**: 直接部署到GitHub Pages
2. **Netlify**: 拖拽文件夹即可部署
3. **Vercel**: 连接Git仓库自动部署
4. **本地服务器**: 使用任何HTTP服务器

### 部署要求
- 支持HTTPS（PWA功能需要）
- 支持Service Worker（离线功能）
- 正确的MIME类型配置

## 🎨 自定义和扩展

### 添加新关卡
在 `js/levels.js` 中的 `createLevels()` 方法中添加新关卡配置。

### 修改视觉效果
- 调整 `css/main.css` 中的CSS变量
- 修改 `js/particles.js` 中的粒子效果
- 更新 `css/animations.css` 中的动画

### 扩展音效
在 `js/audio.js` 中的 `soundConfig` 对象中添加新音效配置。

## 📄 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进游戏！

---

**享受时空织梦的奇妙旅程！** ✨
