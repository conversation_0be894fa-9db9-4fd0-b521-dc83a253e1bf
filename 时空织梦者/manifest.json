{"name": "时空织梦者 - <PERSON> Weaver", "short_name": "时空织梦者", "description": "一款创新的时间操控解谜游戏，通过操控时间线解决复杂的时空谜题", "version": "1.0.0", "start_url": "./index.html", "display": "standalone", "orientation": "any", "theme_color": "#1a1a2e", "background_color": "#0f172a", "lang": "zh-CN", "scope": "./", "categories": ["games", "puzzle", "strategy"], "icons": [{"src": "assets/images/icon-72.png", "sizes": "72x72", "type": "image/png", "purpose": "any"}, {"src": "assets/images/icon-96.png", "sizes": "96x96", "type": "image/png", "purpose": "any"}, {"src": "assets/images/icon-128.png", "sizes": "128x128", "type": "image/png", "purpose": "any"}, {"src": "assets/images/icon-144.png", "sizes": "144x144", "type": "image/png", "purpose": "any"}, {"src": "assets/images/icon-152.png", "sizes": "152x152", "type": "image/png", "purpose": "any"}, {"src": "assets/images/icon-192.png", "sizes": "192x192", "type": "image/png", "purpose": "any maskable"}, {"src": "assets/images/icon-384.png", "sizes": "384x384", "type": "image/png", "purpose": "any"}, {"src": "assets/images/icon-512.png", "sizes": "512x512", "type": "image/png", "purpose": "any maskable"}], "screenshots": [{"src": "assets/images/screenshot-1.png", "sizes": "1280x720", "type": "image/png", "form_factor": "wide", "label": "游戏主界面"}, {"src": "assets/images/screenshot-2.png", "sizes": "720x1280", "type": "image/png", "form_factor": "narrow", "label": "移动端界面"}], "shortcuts": [{"name": "开始新游戏", "short_name": "新游戏", "description": "开始一个新的时空织梦之旅", "url": "./index.html?action=new", "icons": [{"src": "assets/images/shortcut-new.png", "sizes": "96x96"}]}, {"name": "继续游戏", "short_name": "继续", "description": "继续上次的游戏进度", "url": "./index.html?action=continue", "icons": [{"src": "assets/images/shortcut-continue.png", "sizes": "96x96"}]}, {"name": "关卡选择", "short_name": "关卡", "description": "选择要挑战的关卡", "url": "./index.html?action=levels", "icons": [{"src": "assets/images/shortcut-levels.png", "sizes": "96x96"}]}], "related_applications": [], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}}