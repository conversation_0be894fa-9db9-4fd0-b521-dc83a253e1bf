/**
 * 时空织梦者 - 粒子效果系统
 * 负责创建和管理各种视觉粒子效果，增强游戏的视觉体验
 */

class Particle {
    constructor(x, y, options = {}) {
        this.x = x;
        this.y = y;
        this.startX = x;
        this.startY = y;
        
        // 粒子属性
        this.vx = options.vx || MathUtils.random(-2, 2);
        this.vy = options.vy || MathUtils.random(-2, 2);
        this.size = options.size || MathUtils.random(2, 6);
        this.life = options.life || 1.0;
        this.maxLife = this.life;
        this.decay = options.decay || 0.02;
        
        // 视觉属性
        this.color = options.color || '#06b6d4';
        this.alpha = options.alpha || 1.0;
        this.rotation = options.rotation || 0;
        this.rotationSpeed = options.rotationSpeed || MathUtils.random(-0.1, 0.1);
        
        // 物理属性
        this.gravity = options.gravity || 0;
        this.friction = options.friction || 0.98;
        this.bounce = options.bounce || 0.8;
        
        // 特殊效果
        this.trail = options.trail || false;
        this.trailPoints = [];
        this.maxTrailLength = options.maxTrailLength || 10;
        
        // 动画属性
        this.scaleAnimation = options.scaleAnimation || false;
        this.colorAnimation = options.colorAnimation || false;
        this.waveAmplitude = options.waveAmplitude || 0;
        this.waveFrequency = options.waveFrequency || 0.1;
        this.time = 0;
    }

    /**
     * 更新粒子状态
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        this.time += deltaTime;
        
        // 更新位置
        this.x += this.vx * deltaTime;
        this.y += this.vy * deltaTime;
        
        // 应用重力
        this.vy += this.gravity * deltaTime;
        
        // 应用摩擦力
        this.vx *= this.friction;
        this.vy *= this.friction;
        
        // 波浪运动
        if (this.waveAmplitude > 0) {
            this.y += Math.sin(this.time * this.waveFrequency) * this.waveAmplitude;
        }
        
        // 更新旋转
        this.rotation += this.rotationSpeed * deltaTime;
        
        // 更新生命值
        this.life -= this.decay * deltaTime;
        
        // 更新透明度
        this.alpha = this.life / this.maxLife;
        
        // 尺寸动画
        if (this.scaleAnimation) {
            const scale = 0.5 + 0.5 * Math.sin(this.time * 0.1);
            this.currentSize = this.size * scale;
        } else {
            this.currentSize = this.size * this.alpha;
        }
        
        // 轨迹效果
        if (this.trail) {
            this.trailPoints.push({ x: this.x, y: this.y, alpha: this.alpha });
            if (this.trailPoints.length > this.maxTrailLength) {
                this.trailPoints.shift();
            }
        }
    }

    /**
     * 渲染粒子
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    render(ctx) {
        if (this.life <= 0) return;
        
        ctx.save();
        
        // 设置透明度
        ctx.globalAlpha = this.alpha;
        
        // 渲染轨迹
        if (this.trail && this.trailPoints.length > 1) {
            this.renderTrail(ctx);
        }
        
        // 移动到粒子位置
        ctx.translate(this.x, this.y);
        ctx.rotate(this.rotation);
        
        // 设置颜色
        ctx.fillStyle = this.color;
        ctx.strokeStyle = this.color;
        
        // 渲染粒子
        ctx.beginPath();
        ctx.arc(0, 0, this.currentSize, 0, Math.PI * 2);
        ctx.fill();
        
        // 添加发光效果
        ctx.shadowColor = this.color;
        ctx.shadowBlur = this.currentSize * 2;
        ctx.fill();
        
        ctx.restore();
    }

    /**
     * 渲染轨迹效果
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    renderTrail(ctx) {
        ctx.beginPath();
        ctx.strokeStyle = this.color;
        
        for (let i = 0; i < this.trailPoints.length - 1; i++) {
            const point = this.trailPoints[i];
            const nextPoint = this.trailPoints[i + 1];
            const alpha = (i / this.trailPoints.length) * this.alpha;
            
            ctx.globalAlpha = alpha;
            ctx.lineWidth = this.size * alpha;
            
            ctx.beginPath();
            ctx.moveTo(point.x, point.y);
            ctx.lineTo(nextPoint.x, nextPoint.y);
            ctx.stroke();
        }
    }

    /**
     * 检查粒子是否存活
     * @returns {boolean} 是否存活
     */
    isAlive() {
        return this.life > 0;
    }

    /**
     * 检查粒子是否在边界内
     * @param {number} width - 边界宽度
     * @param {number} height - 边界高度
     * @returns {boolean} 是否在边界内
     */
    isInBounds(width, height) {
        return this.x >= 0 && this.x <= width && this.y >= 0 && this.y <= height;
    }
}

class ParticleSystem {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.particles = [];
        this.emitters = [];
        this.maxParticles = 1000;
        this.quality = 'medium'; // low, medium, high
        
        // 性能优化
        this.lastTime = 0;
        this.frameCount = 0;
        this.fps = 60;
        
        this.init();
    }

    /**
     * 初始化粒子系统
     */
    init() {
        // 根据设备性能调整粒子数量
        if (DeviceUtils.isMobile()) {
            this.maxParticles = 500;
            this.quality = 'low';
        }
        
        console.log('粒子系统初始化完成');
    }

    /**
     * 设置粒子质量
     * @param {string} quality - 质量等级 (low, medium, high)
     */
    setQuality(quality) {
        this.quality = quality;
        
        switch (quality) {
            case 'low':
                this.maxParticles = 300;
                break;
            case 'medium':
                this.maxParticles = 600;
                break;
            case 'high':
                this.maxParticles = 1000;
                break;
        }
    }

    /**
     * 创建单个粒子
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {Object} options - 粒子选项
     */
    createParticle(x, y, options = {}) {
        if (this.particles.length >= this.maxParticles) {
            return;
        }
        
        const particle = new Particle(x, y, options);
        this.particles.push(particle);
    }

    /**
     * 创建粒子爆发效果
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {Object} options - 爆发选项
     */
    createBurst(x, y, options = {}) {
        const count = options.count || 20;
        const colors = options.colors || ['#06b6d4', '#8b5cf6', '#10b981'];
        const speed = options.speed || 5;
        
        for (let i = 0; i < count; i++) {
            const angle = (Math.PI * 2 * i) / count;
            const velocity = MathUtils.random(speed * 0.5, speed);
            
            this.createParticle(x, y, {
                vx: Math.cos(angle) * velocity,
                vy: Math.sin(angle) * velocity,
                color: colors[Math.floor(Math.random() * colors.length)],
                size: MathUtils.random(3, 8),
                life: MathUtils.random(0.8, 1.5),
                decay: 0.015,
                gravity: options.gravity || 0.1
            });
        }
    }

    /**
     * 创建时间粒子效果
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {string} timeType - 时间类型 (past, present, future)
     */
    createTimeParticles(x, y, timeType = 'present') {
        const colors = {
            past: ['#f59e0b', '#fbbf24', '#fcd34d'],
            present: ['#06b6d4', '#0891b2', '#0e7490'],
            future: ['#6366f1', '#8b5cf6', '#a855f7']
        };
        
        const particleColors = colors[timeType];
        
        for (let i = 0; i < 15; i++) {
            this.createParticle(x + MathUtils.random(-20, 20), y + MathUtils.random(-20, 20), {
                vx: MathUtils.random(-1, 1),
                vy: MathUtils.random(-3, -1),
                color: particleColors[Math.floor(Math.random() * particleColors.length)],
                size: MathUtils.random(2, 5),
                life: MathUtils.random(1, 2),
                decay: 0.01,
                scaleAnimation: true,
                waveAmplitude: 2,
                waveFrequency: 0.05
            });
        }
    }

    /**
     * 创建连接线粒子效果
     * @param {number} x1 - 起点X坐标
     * @param {number} y1 - 起点Y坐标
     * @param {number} x2 - 终点X坐标
     * @param {number} y2 - 终点Y坐标
     */
    createConnectionParticles(x1, y1, x2, y2) {
        const distance = MathUtils.distance(x1, y1, x2, y2);
        const steps = Math.floor(distance / 10);
        
        for (let i = 0; i <= steps; i++) {
            const t = i / steps;
            const x = MathUtils.lerp(x1, x2, t);
            const y = MathUtils.lerp(y1, y2, t);
            
            this.createParticle(x, y, {
                vx: MathUtils.random(-0.5, 0.5),
                vy: MathUtils.random(-0.5, 0.5),
                color: '#06b6d4',
                size: MathUtils.random(1, 3),
                life: 0.5,
                decay: 0.02,
                trail: true,
                maxTrailLength: 5
            });
        }
    }

    /**
     * 创建能量收集效果
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     */
    createCollectionEffect(x, y) {
        this.createBurst(x, y, {
            count: 30,
            colors: ['#10b981', '#34d399', '#6ee7b7'],
            speed: 8,
            gravity: -0.2
        });
        
        // 添加向上飘散的粒子
        for (let i = 0; i < 10; i++) {
            this.createParticle(x, y, {
                vx: MathUtils.random(-2, 2),
                vy: MathUtils.random(-5, -2),
                color: '#10b981',
                size: MathUtils.random(4, 8),
                life: 2,
                decay: 0.008,
                scaleAnimation: true,
                alpha: 0.8
            });
        }
    }

    /**
     * 更新所有粒子
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        // 更新粒子
        for (let i = this.particles.length - 1; i >= 0; i--) {
            const particle = this.particles[i];
            particle.update(deltaTime);
            
            // 移除死亡的粒子
            if (!particle.isAlive() || !particle.isInBounds(this.canvas.width, this.canvas.height + 100)) {
                this.particles.splice(i, 1);
            }
        }
        
        // 更新发射器
        this.emitters.forEach(emitter => {
            emitter.update(deltaTime);
        });
    }

    /**
     * 渲染所有粒子
     */
    render() {
        // 根据质量设置渲染选项
        if (this.quality === 'low') {
            this.ctx.imageSmoothingEnabled = false;
        }
        
        // 渲染粒子
        this.particles.forEach(particle => {
            particle.render(this.ctx);
        });
        
        // 渲染发射器
        this.emitters.forEach(emitter => {
            emitter.render(this.ctx);
        });
    }

    /**
     * 清除所有粒子
     */
    clear() {
        this.particles = [];
        this.emitters = [];
    }

    /**
     * 获取粒子数量
     * @returns {number} 粒子数量
     */
    getParticleCount() {
        return this.particles.length;
    }

    /**
     * 创建背景粒子效果
     */
    createBackgroundParticles() {
        const count = this.quality === 'high' ? 50 : this.quality === 'medium' ? 30 : 15;
        
        for (let i = 0; i < count; i++) {
            this.createParticle(
                MathUtils.random(0, this.canvas.width),
                MathUtils.random(0, this.canvas.height),
                {
                    vx: MathUtils.random(-0.5, 0.5),
                    vy: MathUtils.random(-0.5, 0.5),
                    color: '#374151',
                    size: MathUtils.random(1, 3),
                    life: Infinity,
                    decay: 0,
                    alpha: MathUtils.random(0.1, 0.3),
                    waveAmplitude: 1,
                    waveFrequency: 0.02
                }
            );
        }
    }
}

// 粒子发射器类
class ParticleEmitter {
    constructor(x, y, options = {}) {
        this.x = x;
        this.y = y;
        this.rate = options.rate || 10; // 每秒发射粒子数
        this.particleOptions = options.particleOptions || {};
        this.active = true;
        this.timer = 0;
        this.duration = options.duration || Infinity;
        this.elapsed = 0;
    }

    update(deltaTime) {
        if (!this.active) return;
        
        this.elapsed += deltaTime;
        this.timer += deltaTime;
        
        if (this.timer >= 1 / this.rate) {
            // 发射粒子的逻辑会在ParticleSystem中实现
            this.timer = 0;
        }
        
        if (this.elapsed >= this.duration) {
            this.active = false;
        }
    }

    render(ctx) {
        // 发射器可视化（可选）
    }
}
