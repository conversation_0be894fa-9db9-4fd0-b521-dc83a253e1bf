/**
 * 时空织梦者 - 锚点系统
 * 负责管理时间锚点的创建、连接、渲染和交互
 */

class TimeAnchor {
    constructor(x, y, timeState, options = {}) {
        this.id = Date.now() + Math.random();
        this.x = x;
        this.y = y;
        this.timeState = timeState;
        this.timeValue = options.timeValue || 0.5;
        
        // 视觉属性
        this.radius = options.radius || 10;
        this.maxRadius = this.radius * 2;
        this.currentRadius = this.radius;
        this.color = this.getColorByTimeState(timeState);
        this.alpha = 1.0;
        
        // 状态属性
        this.active = true;
        this.selected = false;
        this.hovered = false;
        this.connections = [];
        
        // 动画属性
        this.pulsePhase = Math.random() * Math.PI * 2;
        this.pulseSpeed = 2.0;
        this.glowIntensity = 0.5;
        
        // 物理属性
        this.energy = 100;
        this.maxEnergy = 100;
        this.energyDecay = 0.1;
        
        // 创建时间
        this.createdAt = Date.now();
        this.lastUsed = Date.now();
        
        // 特殊效果
        this.ripples = [];
        this.particles = [];
        
        this.init();
    }

    /**
     * 初始化锚点
     */
    init() {
        // 创建初始粒子效果
        this.createSpawnEffect();
        
        console.log(`时间锚点创建: ${this.timeState} at (${this.x}, ${this.y})`);
    }

    /**
     * 根据时间状态获取颜色
     * @param {string} timeState - 时间状态
     * @returns {string} 颜色值
     */
    getColorByTimeState(timeState) {
        const colors = {
            past: '#f59e0b',
            present: '#06b6d4',
            future: '#6366f1'
        };
        return colors[timeState] || colors.present;
    }

    /**
     * 更新锚点状态
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        // 更新脉冲动画
        this.pulsePhase += this.pulseSpeed * deltaTime;
        const pulseScale = 1 + Math.sin(this.pulsePhase) * 0.2;
        this.currentRadius = this.radius * pulseScale;
        
        // 更新发光强度
        this.glowIntensity = 0.5 + Math.sin(this.pulsePhase * 0.5) * 0.3;
        
        // 更新能量
        if (this.energy > 0) {
            this.energy = Math.max(0, this.energy - this.energyDecay * deltaTime);
        }
        
        // 更新透明度基于能量
        this.alpha = 0.3 + (this.energy / this.maxEnergy) * 0.7;
        
        // 更新涟漪效果
        this.updateRipples(deltaTime);
        
        // 更新粒子效果
        this.updateParticles(deltaTime);
        
        // 检查是否需要移除
        if (this.energy <= 0 && !this.selected) {
            this.active = false;
        }
    }

    /**
     * 更新涟漪效果
     * @param {number} deltaTime - 时间增量
     */
    updateRipples(deltaTime) {
        for (let i = this.ripples.length - 1; i >= 0; i--) {
            const ripple = this.ripples[i];
            ripple.radius += ripple.speed * deltaTime;
            ripple.alpha -= ripple.decay * deltaTime;
            
            if (ripple.alpha <= 0) {
                this.ripples.splice(i, 1);
            }
        }
    }

    /**
     * 更新粒子效果
     * @param {number} deltaTime - 时间增量
     */
    updateParticles(deltaTime) {
        for (let i = this.particles.length - 1; i >= 0; i--) {
            const particle = this.particles[i];
            particle.x += particle.vx * deltaTime;
            particle.y += particle.vy * deltaTime;
            particle.life -= deltaTime;
            particle.alpha = particle.life / particle.maxLife;
            
            if (particle.life <= 0) {
                this.particles.splice(i, 1);
            }
        }
    }

    /**
     * 渲染锚点
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    render(ctx) {
        if (!this.active) return;
        
        ctx.save();
        
        // 渲染涟漪效果
        this.renderRipples(ctx);
        
        // 渲染粒子效果
        this.renderParticles(ctx);
        
        // 设置透明度
        ctx.globalAlpha = this.alpha;
        
        // 渲染发光效果
        if (this.glowIntensity > 0) {
            ctx.shadowColor = this.color;
            ctx.shadowBlur = this.currentRadius * this.glowIntensity;
        }
        
        // 渲染主体
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.currentRadius, 0, Math.PI * 2);
        
        // 填充颜色
        if (this.selected) {
            ctx.fillStyle = ColorUtils.withAlpha(this.color, 0.8);
        } else if (this.hovered) {
            ctx.fillStyle = ColorUtils.withAlpha(this.color, 0.6);
        } else {
            ctx.fillStyle = ColorUtils.withAlpha(this.color, 0.4);
        }
        ctx.fill();
        
        // 边框
        ctx.strokeStyle = this.color;
        ctx.lineWidth = this.selected ? 3 : 2;
        ctx.stroke();
        
        // 渲染内部图标
        this.renderIcon(ctx);
        
        // 渲染能量指示器
        if (this.energy < this.maxEnergy) {
            this.renderEnergyBar(ctx);
        }
        
        ctx.restore();
    }

    /**
     * 渲染涟漪效果
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    renderRipples(ctx) {
        this.ripples.forEach(ripple => {
            ctx.save();
            ctx.globalAlpha = ripple.alpha;
            ctx.strokeStyle = this.color;
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.arc(this.x, this.y, ripple.radius, 0, Math.PI * 2);
            ctx.stroke();
            ctx.restore();
        });
    }

    /**
     * 渲染粒子效果
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    renderParticles(ctx) {
        this.particles.forEach(particle => {
            ctx.save();
            ctx.globalAlpha = particle.alpha;
            ctx.fillStyle = particle.color;
            ctx.beginPath();
            ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            ctx.fill();
            ctx.restore();
        });
    }

    /**
     * 渲染内部图标
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    renderIcon(ctx) {
        ctx.save();
        ctx.fillStyle = '#ffffff';
        ctx.font = `${this.currentRadius}px Arial`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        
        const icons = {
            past: '◀',
            present: '●',
            future: '▶'
        };
        
        ctx.fillText(icons[this.timeState] || '●', this.x, this.y);
        ctx.restore();
    }

    /**
     * 渲染能量条
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    renderEnergyBar(ctx) {
        const barWidth = this.currentRadius * 2;
        const barHeight = 4;
        const barY = this.y + this.currentRadius + 8;
        
        ctx.save();
        
        // 背景
        ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
        ctx.fillRect(this.x - barWidth / 2, barY, barWidth, barHeight);
        
        // 能量条
        const energyWidth = (this.energy / this.maxEnergy) * barWidth;
        ctx.fillStyle = this.color;
        ctx.fillRect(this.x - barWidth / 2, barY, energyWidth, barHeight);
        
        ctx.restore();
    }

    /**
     * 创建生成效果
     */
    createSpawnEffect() {
        // 创建涟漪
        this.createRipple();
        
        // 创建粒子爆发
        for (let i = 0; i < 12; i++) {
            const angle = (Math.PI * 2 * i) / 12;
            const speed = MathUtils.random(50, 100);
            
            this.particles.push({
                x: this.x,
                y: this.y,
                vx: Math.cos(angle) * speed,
                vy: Math.sin(angle) * speed,
                size: MathUtils.random(2, 4),
                color: this.color,
                life: MathUtils.random(0.5, 1.0),
                maxLife: 1.0,
                alpha: 1.0
            });
        }
    }

    /**
     * 创建涟漪效果
     */
    createRipple() {
        this.ripples.push({
            radius: this.radius,
            speed: 60,
            alpha: 0.8,
            decay: 1.0
        });
    }

    /**
     * 检查点是否在锚点内
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @returns {boolean} 是否在锚点内
     */
    containsPoint(x, y) {
        const distance = MathUtils.distance(this.x, this.y, x, y);
        return distance <= this.currentRadius;
    }

    /**
     * 设置悬停状态
     * @param {boolean} hovered - 是否悬停
     */
    setHovered(hovered) {
        if (this.hovered !== hovered) {
            this.hovered = hovered;
            if (hovered) {
                this.createRipple();
            }
        }
    }

    /**
     * 设置选中状态
     * @param {boolean} selected - 是否选中
     */
    setSelected(selected) {
        if (this.selected !== selected) {
            this.selected = selected;
            if (selected) {
                this.createRipple();
                this.lastUsed = Date.now();
            }
        }
    }

    /**
     * 添加连接
     * @param {string} anchorId - 连接的锚点ID
     */
    addConnection(anchorId) {
        if (!this.connections.includes(anchorId)) {
            this.connections.push(anchorId);
            this.createRipple();
        }
    }

    /**
     * 移除连接
     * @param {string} anchorId - 要移除的锚点ID
     */
    removeConnection(anchorId) {
        const index = this.connections.indexOf(anchorId);
        if (index !== -1) {
            this.connections.splice(index, 1);
        }
    }

    /**
     * 激活锚点
     */
    activate() {
        this.energy = this.maxEnergy;
        this.lastUsed = Date.now();
        this.createRipple();
        
        // 创建激活粒子效果
        for (let i = 0; i < 8; i++) {
            const angle = (Math.PI * 2 * i) / 8;
            const distance = this.radius * 2;
            
            this.particles.push({
                x: this.x + Math.cos(angle) * distance,
                y: this.y + Math.sin(angle) * distance,
                vx: Math.cos(angle) * -30,
                vy: Math.sin(angle) * -30,
                size: 3,
                color: this.color,
                life: 0.8,
                maxLife: 0.8,
                alpha: 1.0
            });
        }
    }

    /**
     * 获取锚点信息
     * @returns {Object} 锚点信息
     */
    getInfo() {
        return {
            id: this.id,
            x: this.x,
            y: this.y,
            timeState: this.timeState,
            timeValue: this.timeValue,
            energy: this.energy,
            connections: this.connections.length,
            active: this.active,
            selected: this.selected
        };
    }

    /**
     * 销毁锚点
     */
    destroy() {
        this.active = false;

        // 创建销毁效果
        for (let i = 0; i < 16; i++) {
            const angle = (Math.PI * 2 * i) / 16;
            const speed = MathUtils.random(80, 120);

            this.particles.push({
                x: this.x,
                y: this.y,
                vx: Math.cos(angle) * speed,
                vy: Math.sin(angle) * speed,
                size: MathUtils.random(1, 3),
                color: this.color,
                life: MathUtils.random(0.3, 0.8),
                maxLife: 0.8,
                alpha: 1.0
            });
        }
    }
}

/**
 * 时间连接线类
 */
class TimeConnection {
    constructor(anchor1, anchor2) {
        this.id = Date.now() + Math.random();
        this.anchor1 = anchor1;
        this.anchor2 = anchor2;
        this.active = true;

        // 视觉属性
        this.color = this.getConnectionColor();
        this.width = 3;
        this.alpha = 0.8;

        // 动画属性
        this.flowOffset = 0;
        this.flowSpeed = 100;
        this.pulsePhase = 0;
        this.pulseSpeed = 2;

        // 能量传输
        this.energyFlow = 0;
        this.maxEnergyFlow = 50;

        this.init();
    }

    /**
     * 初始化连接
     */
    init() {
        console.log(`时间连接创建: ${this.anchor1.id} <-> ${this.anchor2.id}`);
    }

    /**
     * 获取连接颜色
     * @returns {string} 连接颜色
     */
    getConnectionColor() {
        // 根据两个锚点的时间状态混合颜色
        const colors = {
            past: [245, 158, 11],
            present: [6, 182, 212],
            future: [99, 102, 241]
        };

        const color1 = colors[this.anchor1.timeState];
        const color2 = colors[this.anchor2.timeState];

        if (!color1 || !color2) return '#06b6d4';

        // 混合颜色
        const r = Math.floor((color1[0] + color2[0]) / 2);
        const g = Math.floor((color1[1] + color2[1]) / 2);
        const b = Math.floor((color1[2] + color2[2]) / 2);

        return `rgb(${r}, ${g}, ${b})`;
    }

    /**
     * 更新连接状态
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        // 更新流动动画
        this.flowOffset += this.flowSpeed * deltaTime;
        if (this.flowOffset > 20) {
            this.flowOffset = 0;
        }

        // 更新脉冲动画
        this.pulsePhase += this.pulseSpeed * deltaTime;

        // 检查锚点是否仍然活跃
        if (!this.anchor1.active || !this.anchor2.active) {
            this.active = false;
        }
    }

    /**
     * 渲染连接线
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    render(ctx) {
        if (!this.active) return;

        ctx.save();

        // 设置透明度
        ctx.globalAlpha = this.alpha;

        // 计算脉冲宽度
        const pulseWidth = this.width + Math.sin(this.pulsePhase) * 2;

        // 渲染主连接线
        ctx.strokeStyle = this.color;
        ctx.lineWidth = pulseWidth;
        ctx.lineCap = 'round';

        ctx.beginPath();
        ctx.moveTo(this.anchor1.x, this.anchor1.y);
        ctx.lineTo(this.anchor2.x, this.anchor2.y);
        ctx.stroke();

        // 渲染流动效果
        this.renderFlow(ctx);

        // 渲染发光效果
        ctx.shadowColor = this.color;
        ctx.shadowBlur = 10;
        ctx.stroke();

        ctx.restore();
    }

    /**
     * 渲染流动效果
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    renderFlow(ctx) {
        const distance = MathUtils.distance(
            this.anchor1.x, this.anchor1.y,
            this.anchor2.x, this.anchor2.y
        );

        const steps = Math.floor(distance / 20);

        for (let i = 0; i < steps; i++) {
            const t = (i / steps + this.flowOffset / distance) % 1;
            const x = MathUtils.lerp(this.anchor1.x, this.anchor2.x, t);
            const y = MathUtils.lerp(this.anchor1.y, this.anchor2.y, t);

            ctx.save();
            ctx.globalAlpha = 0.8;
            ctx.fillStyle = '#ffffff';
            ctx.beginPath();
            ctx.arc(x, y, 2, 0, Math.PI * 2);
            ctx.fill();
            ctx.restore();
        }
    }

    /**
     * 获取连接信息
     * @returns {Object} 连接信息
     */
    getInfo() {
        return {
            id: this.id,
            anchor1Id: this.anchor1.id,
            anchor2Id: this.anchor2.id,
            active: this.active,
            energyFlow: this.energyFlow
        };
    }
}

/**
 * 锚点管理器类
 */
class AnchorManager {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.anchors = [];
        this.connections = [];
        this.selectedAnchor = null;
        this.hoveredAnchor = null;
        this.maxAnchors = 15;

        // 交互状态
        this.mode = 'create'; // create, link, remove
        this.linkingFrom = null;

        // 事件监听器
        this.eventListeners = {};

        this.init();
    }

    /**
     * 初始化锚点管理器
     */
    init() {
        this.bindEvents();
        console.log('锚点管理器初始化完成');
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 工具栏按钮
        const createBtn = DOMUtils.$('#create-anchor');
        const linkBtn = DOMUtils.$('#link-anchor');
        const removeBtn = DOMUtils.$('#remove-anchor');

        if (createBtn) {
            DOMUtils.addEvent(createBtn, 'click', () => this.setMode('create'));
        }
        if (linkBtn) {
            DOMUtils.addEvent(linkBtn, 'click', () => this.setMode('link'));
        }
        if (removeBtn) {
            DOMUtils.addEvent(removeBtn, 'click', () => this.setMode('remove'));
        }

        // 移动端按钮
        const mobileAnchor = DOMUtils.$('#mobile-anchor');
        const mobileLink = DOMUtils.$('#mobile-link');

        if (mobileAnchor) {
            DOMUtils.addEvent(mobileAnchor, 'click', () => this.setMode('create'));
        }
        if (mobileLink) {
            DOMUtils.addEvent(mobileLink, 'click', () => this.setMode('link'));
        }
    }

    /**
     * 设置操作模式
     * @param {string} mode - 操作模式
     */
    setMode(mode) {
        this.mode = mode;
        this.linkingFrom = null;
        this.updateModeUI();

        this.emit('modeChanged', { mode });
        console.log('锚点模式切换:', mode);
    }

    /**
     * 更新模式UI
     */
    updateModeUI() {
        const buttons = {
            create: DOMUtils.$('#create-anchor'),
            link: DOMUtils.$('#link-anchor'),
            remove: DOMUtils.$('#remove-anchor')
        };

        Object.keys(buttons).forEach(mode => {
            const btn = buttons[mode];
            if (btn) {
                if (mode === this.mode) {
                    btn.classList.add('active');
                } else {
                    btn.classList.remove('active');
                }
            }
        });
    }

    /**
     * 处理画布点击
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     */
    handleCanvasClick(x, y) {
        const clickedAnchor = this.getAnchorAt(x, y);

        switch (this.mode) {
            case 'create':
                if (!clickedAnchor) {
                    this.createAnchor(x, y);
                }
                break;

            case 'link':
                if (clickedAnchor) {
                    this.handleLinkClick(clickedAnchor);
                }
                break;

            case 'remove':
                if (clickedAnchor) {
                    this.removeAnchor(clickedAnchor.id);
                }
                break;
        }
    }

    /**
     * 处理连接点击
     * @param {TimeAnchor} anchor - 被点击的锚点
     */
    handleLinkClick(anchor) {
        if (!this.linkingFrom) {
            this.linkingFrom = anchor;
            anchor.setSelected(true);
        } else if (this.linkingFrom.id !== anchor.id) {
            this.createConnection(this.linkingFrom, anchor);
            this.linkingFrom.setSelected(false);
            this.linkingFrom = null;
        }
    }

    /**
     * 创建锚点
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @returns {TimeAnchor|null} 创建的锚点
     */
    createAnchor(x, y) {
        if (this.anchors.length >= this.maxAnchors) {
            this.emit('anchorLimitReached');
            return null;
        }

        // 获取当前时间状态
        const timeState = timeline.currentState;
        const timeValue = timeline.currentTime;

        const anchor = new TimeAnchor(x, y, timeState, { timeValue });
        this.anchors.push(anchor);

        this.emit('anchorCreated', anchor);
        return anchor;
    }

    /**
     * 创建连接
     * @param {TimeAnchor} anchor1 - 第一个锚点
     * @param {TimeAnchor} anchor2 - 第二个锚点
     * @returns {TimeConnection|null} 创建的连接
     */
    createConnection(anchor1, anchor2) {
        // 检查是否已经连接
        const existingConnection = this.connections.find(conn =>
            (conn.anchor1.id === anchor1.id && conn.anchor2.id === anchor2.id) ||
            (conn.anchor1.id === anchor2.id && conn.anchor2.id === anchor1.id)
        );

        if (existingConnection) {
            console.warn('锚点已经连接');
            return null;
        }

        const connection = new TimeConnection(anchor1, anchor2);
        this.connections.push(connection);

        // 更新锚点连接信息
        anchor1.addConnection(anchor2.id);
        anchor2.addConnection(anchor1.id);

        this.emit('connectionCreated', connection);
        return connection;
    }

    /**
     * 移除锚点
     * @param {string} anchorId - 锚点ID
     * @returns {boolean} 是否移除成功
     */
    removeAnchor(anchorId) {
        const anchorIndex = this.anchors.findIndex(a => a.id === anchorId);
        if (anchorIndex === -1) return false;

        const anchor = this.anchors[anchorIndex];

        // 移除相关连接
        this.connections = this.connections.filter(conn => {
            if (conn.anchor1.id === anchorId || conn.anchor2.id === anchorId) {
                conn.active = false;
                return false;
            }
            return true;
        });

        // 销毁锚点
        anchor.destroy();
        this.anchors.splice(anchorIndex, 1);

        this.emit('anchorRemoved', anchor);
        return true;
    }

    /**
     * 获取指定位置的锚点
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @returns {TimeAnchor|null} 找到的锚点
     */
    getAnchorAt(x, y) {
        return this.anchors.find(anchor => anchor.containsPoint(x, y)) || null;
    }

    /**
     * 处理鼠标移动
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     */
    handleMouseMove(x, y) {
        const hoveredAnchor = this.getAnchorAt(x, y);

        // 更新悬停状态
        if (this.hoveredAnchor !== hoveredAnchor) {
            if (this.hoveredAnchor) {
                this.hoveredAnchor.setHovered(false);
            }
            if (hoveredAnchor) {
                hoveredAnchor.setHovered(true);
            }
            this.hoveredAnchor = hoveredAnchor;
        }
    }

    /**
     * 更新所有锚点和连接
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        // 更新锚点
        for (let i = this.anchors.length - 1; i >= 0; i--) {
            const anchor = this.anchors[i];
            anchor.update(deltaTime);

            if (!anchor.active) {
                this.anchors.splice(i, 1);
            }
        }

        // 更新连接
        for (let i = this.connections.length - 1; i >= 0; i--) {
            const connection = this.connections[i];
            connection.update(deltaTime);

            if (!connection.active) {
                this.connections.splice(i, 1);
            }
        }
    }

    /**
     * 渲染所有锚点和连接
     */
    render() {
        // 先渲染连接线
        this.connections.forEach(connection => {
            connection.render(this.ctx);
        });

        // 再渲染锚点
        this.anchors.forEach(anchor => {
            anchor.render(this.ctx);
        });

        // 渲染连接预览
        if (this.mode === 'link' && this.linkingFrom && this.hoveredAnchor) {
            this.renderLinkPreview();
        }
    }

    /**
     * 渲染连接预览
     */
    renderLinkPreview() {
        this.ctx.save();
        this.ctx.strokeStyle = '#ffffff';
        this.ctx.lineWidth = 2;
        this.ctx.setLineDash([5, 5]);
        this.ctx.globalAlpha = 0.6;

        this.ctx.beginPath();
        this.ctx.moveTo(this.linkingFrom.x, this.linkingFrom.y);
        this.ctx.lineTo(this.hoveredAnchor.x, this.hoveredAnchor.y);
        this.ctx.stroke();

        this.ctx.restore();
    }

    /**
     * 清除所有锚点和连接
     */
    clear() {
        this.anchors.forEach(anchor => anchor.destroy());
        this.anchors = [];
        this.connections = [];
        this.selectedAnchor = null;
        this.hoveredAnchor = null;
        this.linkingFrom = null;

        this.emit('cleared');
    }

    /**
     * 获取锚点统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            totalAnchors: this.anchors.length,
            totalConnections: this.connections.length,
            activeAnchors: this.anchors.filter(a => a.active).length,
            timeStates: {
                past: this.anchors.filter(a => a.timeState === 'past').length,
                present: this.anchors.filter(a => a.timeState === 'present').length,
                future: this.anchors.filter(a => a.timeState === 'future').length
            }
        };
    }

    /**
     * 添加事件监听器
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    on(event, callback) {
        if (!this.eventListeners[event]) {
            this.eventListeners[event] = [];
        }
        this.eventListeners[event].push(callback);
    }

    /**
     * 触发事件
     * @param {string} event - 事件名称
     * @param {*} data - 事件数据
     */
    emit(event, data) {
        if (this.eventListeners[event]) {
            this.eventListeners[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error('锚点事件处理器错误:', error);
                }
            });
        }
    }
}

// 创建全局锚点管理器实例（将在游戏初始化时创建）
let anchorManager = null;
