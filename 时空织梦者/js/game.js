/**
 * 时空织梦者 - 游戏主引擎
 * 负责游戏的核心逻辑、状态管理和渲染循环
 */

class TimeWeaverGame {
    constructor() {
        // 游戏状态
        this.state = 'loading'; // loading, menu, playing, paused, victory, gameover
        this.previousState = null;
        
        // 画布和上下文
        this.canvas = null;
        this.ctx = null;
        this.width = 0;
        this.height = 0;
        
        // 游戏系统
        this.timeline = null;
        this.anchorManager = null;
        this.physicsWorld = null;
        this.particleSystem = null;
        this.storage = null;
        
        // 游戏数据
        this.currentLevel = 1;
        this.score = 0;
        this.lives = 3;
        this.gameTime = 0;
        
        // 渲染相关
        this.lastFrameTime = 0;
        this.deltaTime = 0;
        this.fps = 60;
        this.frameCount = 0;
        
        // 输入处理
        this.input = {
            mouse: { x: 0, y: 0, pressed: false, justPressed: false },
            keys: {},
            touches: []
        };
        
        // 游戏设置
        this.settings = {
            soundEnabled: true,
            musicEnabled: true,
            particleQuality: 'high',
            showFPS: false,
            debugMode: false
        };
        
        // 性能监控
        this.performance = {
            frameTime: 0,
            updateTime: 0,
            renderTime: 0,
            memoryUsage: 0
        };
        
        this.init();
    }

    /**
     * 初始化游戏
     */
    async init() {
        try {
            console.log('游戏初始化开始...');
            
            // 初始化画布
            this.initCanvas();
            
            // 初始化存储系统
            this.storage = new GameStorage();
            await this.loadSettings();
            
            // 初始化游戏系统
            this.initSystems();
            
            // 绑定事件
            this.bindEvents();
            
            // 开始游戏循环
            this.startGameLoop();
            
            // 切换到菜单状态
            this.setState('menu');
            
            console.log('游戏初始化完成');
            
        } catch (error) {
            console.error('游戏初始化失败:', error);
            this.showError('游戏初始化失败，请刷新页面重试');
        }
    }

    /**
     * 初始化画布
     */
    initCanvas() {
        this.canvas = DOMUtils.$('#game-canvas');
        if (!this.canvas) {
            throw new Error('找不到游戏画布元素');
        }
        
        this.ctx = this.canvas.getContext('2d');
        this.resizeCanvas();
        
        // 设置画布样式
        this.ctx.imageSmoothingEnabled = true;
        this.ctx.imageSmoothingQuality = 'high';
    }

    /**
     * 调整画布大小
     */
    resizeCanvas() {
        const container = this.canvas.parentElement;
        const rect = container.getBoundingClientRect();
        
        // 设置画布实际大小
        this.canvas.width = rect.width;
        this.canvas.height = rect.height;
        
        // 更新游戏尺寸
        this.width = this.canvas.width;
        this.height = this.canvas.height;
        
        // 更新物理世界尺寸
        if (this.physicsWorld) {
            this.physicsWorld.width = this.width;
            this.physicsWorld.height = this.height;
        }
        
        console.log(`画布大小调整: ${this.width}x${this.height}`);
    }

    /**
     * 初始化游戏系统
     */
    initSystems() {
        // 初始化时间线系统
        this.timeline = new Timeline();

        // 初始化锚点管理器
        this.anchorManager = new AnchorManager(this.canvas);

        // 初始化物理世界
        this.physicsWorld = new PhysicsWorld(this.width, this.height, {
            gravity: { x: 0, y: 0 },
            enableSpatialOptimization: true
        });

        // 初始化粒子系统
        this.particleSystem = new ParticleSystem(this.canvas, {
            maxParticles: this.settings.particleQuality === 'high' ? 1000 : 500
        });
        
        // 系统间事件绑定
        this.bindSystemEvents();
    }

    /**
     * 绑定系统间事件
     */
    bindSystemEvents() {
        // 时间线事件
        this.timeline.on('timeStateChanged', (data) => {
            this.physicsWorld.updateTimeInfo({
                currentState: data.newState,
                currentTime: data.timeValue
            });
            
            // 创建时间切换粒子效果
            this.particleSystem.createTimeParticles(
                this.width / 2, this.height / 2, data.newState
            );
        });
        
        // 锚点事件
        this.anchorManager.on('anchorCreated', (anchor) => {
            this.particleSystem.createBurst(anchor.x, anchor.y, anchor.color, 20);
            this.addScore(10);
        });
        
        this.anchorManager.on('connectionCreated', (connection) => {
            this.particleSystem.createConnectionEffect(
                connection.anchor1.x, connection.anchor1.y,
                connection.anchor2.x, connection.anchor2.y,
                connection.color
            );
            this.addScore(25);
        });
        
        // 能量不足事件
        this.timeline.on('energyInsufficient', () => {
            this.showMessage('时间能量不足！', 'warning');
        });
    }

    /**
     * 绑定输入事件
     */
    bindEvents() {
        // 鼠标事件
        DOMUtils.addEvent(this.canvas, 'mousedown', (e) => this.handleMouseDown(e));
        DOMUtils.addEvent(this.canvas, 'mouseup', (e) => this.handleMouseUp(e));
        DOMUtils.addEvent(this.canvas, 'mousemove', (e) => this.handleMouseMove(e));
        
        // 触摸事件
        DOMUtils.addEvent(this.canvas, 'touchstart', (e) => this.handleTouchStart(e));
        DOMUtils.addEvent(this.canvas, 'touchend', (e) => this.handleTouchEnd(e));
        DOMUtils.addEvent(this.canvas, 'touchmove', (e) => this.handleTouchMove(e));
        
        // 键盘事件
        DOMUtils.addEvent(document, 'keydown', (e) => this.handleKeyDown(e));
        DOMUtils.addEvent(document, 'keyup', (e) => this.handleKeyUp(e));
        
        // 窗口事件
        DOMUtils.addEvent(window, 'resize', () => {
            TimeUtils.debounce(() => this.resizeCanvas(), 100)();
        });
        
        // 游戏控制按钮
        this.bindGameControls();
    }

    /**
     * 绑定游戏控制按钮
     */
    bindGameControls() {
        // 暂停/继续按钮
        const pauseBtn = DOMUtils.$('#pause-btn');
        if (pauseBtn) {
            DOMUtils.addEvent(pauseBtn, 'click', () => this.togglePause());
        }
        
        // 重置按钮
        const resetBtn = DOMUtils.$('#reset-btn');
        if (resetBtn) {
            DOMUtils.addEvent(resetBtn, 'click', () => this.resetLevel());
        }
        
        // 设置按钮
        const settingsBtn = DOMUtils.$('#settings-btn');
        if (settingsBtn) {
            DOMUtils.addEvent(settingsBtn, 'click', () => this.showSettings());
        }
        
        // 帮助按钮
        const helpBtn = DOMUtils.$('#help-btn');
        if (helpBtn) {
            DOMUtils.addEvent(helpBtn, 'click', () => this.showHelp());
        }

        // 开始游戏按钮（点击画布开始）
        DOMUtils.addEvent(this.canvas, 'click', () => {
            if (this.state === 'menu') {
                this.startGame();
            }
        });
    }

    /**
     * 处理鼠标按下
     * @param {MouseEvent} e - 鼠标事件
     */
    handleMouseDown(e) {
        if (this.state !== 'playing') return;
        
        const rect = this.canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        this.input.mouse.x = x;
        this.input.mouse.y = y;
        this.input.mouse.pressed = true;
        this.input.mouse.justPressed = true;
        
        // 传递给锚点管理器
        this.anchorManager.handleCanvasClick(x, y);
    }

    /**
     * 处理鼠标抬起
     * @param {MouseEvent} e - 鼠标事件
     */
    handleMouseUp(e) {
        this.input.mouse.pressed = false;
    }

    /**
     * 处理鼠标移动
     * @param {MouseEvent} e - 鼠标事件
     */
    handleMouseMove(e) {
        const rect = this.canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        this.input.mouse.x = x;
        this.input.mouse.y = y;
        
        // 传递给锚点管理器
        if (this.state === 'playing') {
            this.anchorManager.handleMouseMove(x, y);
        }
    }

    /**
     * 处理触摸开始
     * @param {TouchEvent} e - 触摸事件
     */
    handleTouchStart(e) {
        e.preventDefault();
        
        if (this.state !== 'playing') return;
        
        const rect = this.canvas.getBoundingClientRect();
        const touch = e.touches[0];
        const x = touch.clientX - rect.left;
        const y = touch.clientY - rect.top;
        
        // 模拟鼠标事件
        this.handleMouseDown({ clientX: touch.clientX, clientY: touch.clientY });
    }

    /**
     * 处理触摸结束
     * @param {TouchEvent} e - 触摸事件
     */
    handleTouchEnd(e) {
        e.preventDefault();
        this.input.mouse.pressed = false;
    }

    /**
     * 处理触摸移动
     * @param {TouchEvent} e - 触摸事件
     */
    handleTouchMove(e) {
        e.preventDefault();
        
        const rect = this.canvas.getBoundingClientRect();
        const touch = e.touches[0];
        
        // 模拟鼠标移动
        this.handleMouseMove({ clientX: touch.clientX, clientY: touch.clientY });
    }

    /**
     * 处理键盘按下
     * @param {KeyboardEvent} e - 键盘事件
     */
    handleKeyDown(e) {
        this.input.keys[e.code] = true;
        
        // 快捷键处理
        switch (e.code) {
            case 'Space':
                e.preventDefault();
                this.togglePause();
                break;
            case 'KeyR':
                if (e.ctrlKey) {
                    e.preventDefault();
                    this.resetLevel();
                }
                break;
            case 'Escape':
                this.showMenu();
                break;
        }
    }

    /**
     * 处理键盘抬起
     * @param {KeyboardEvent} e - 键盘事件
     */
    handleKeyUp(e) {
        this.input.keys[e.code] = false;
    }

    /**
     * 开始游戏循环
     */
    startGameLoop() {
        const gameLoop = (currentTime) => {
            // 计算时间增量
            this.deltaTime = (currentTime - this.lastFrameTime) / 1000;
            this.lastFrameTime = currentTime;
            
            // 限制最大时间增量，避免大的跳跃
            this.deltaTime = Math.min(this.deltaTime, 1/30);
            
            // 更新和渲染
            this.update(this.deltaTime);
            this.render();
            
            // 更新性能统计
            this.updatePerformanceStats();
            
            // 继续循环
            requestAnimationFrame(gameLoop);
        };
        
        requestAnimationFrame(gameLoop);
    }

    /**
     * 更新游戏状态
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        const updateStart = performance.now();
        
        // 根据游戏状态更新
        switch (this.state) {
            case 'playing':
                this.updatePlaying(deltaTime);
                break;
            case 'paused':
                // 暂停状态下只更新UI
                break;
            case 'menu':
                this.updateMenu(deltaTime);
                break;
        }
        
        // 重置输入状态
        this.input.mouse.justPressed = false;
        
        this.performance.updateTime = performance.now() - updateStart;
    }

    /**
     * 更新游戏进行状态
     * @param {number} deltaTime - 时间增量
     */
    updatePlaying(deltaTime) {
        // 更新游戏时间
        this.gameTime += deltaTime;
        
        // 更新各个系统
        this.timeline.update(deltaTime);
        this.anchorManager.update(deltaTime);
        this.physicsWorld.update(deltaTime);
        this.particleSystem.update(deltaTime);
        
        // 检查胜利条件
        this.checkVictoryConditions();
        
        // 更新UI
        this.updateGameUI();
    }

    /**
     * 更新菜单状态
     * @param {number} deltaTime - 时间增量
     */
    updateMenu(deltaTime) {
        // 更新背景粒子效果
        this.particleSystem.update(deltaTime);
    }

    /**
     * 渲染游戏
     */
    render() {
        const renderStart = performance.now();
        
        // 清空画布
        this.ctx.clearRect(0, 0, this.width, this.height);
        
        // 根据状态渲染
        switch (this.state) {
            case 'playing':
            case 'paused':
                this.renderGame();
                break;
            case 'menu':
                this.renderMenu();
                break;
            case 'loading':
                this.renderLoading();
                break;
        }
        
        // 渲染调试信息
        if (this.settings.debugMode) {
            this.renderDebugInfo();
        }
        
        this.performance.renderTime = performance.now() - renderStart;
        this.frameCount++;
    }

    /**
     * 渲染游戏画面
     */
    renderGame() {
        // 渲染背景
        this.renderBackground();
        
        // 渲染物理对象
        this.physicsWorld.render(this.ctx);
        
        // 渲染锚点和连接
        this.anchorManager.render();
        
        // 渲染粒子效果
        this.particleSystem.render(this.ctx);
        
        // 渲染暂停覆盖层
        if (this.state === 'paused') {
            this.renderPauseOverlay();
        }
    }

    /**
     * 渲染背景
     */
    renderBackground() {
        // 创建时间相关的背景渐变
        const gradient = this.ctx.createLinearGradient(0, 0, this.width, this.height);
        
        const timeState = this.timeline?.currentState || 'present';
        const gradients = {
            past: ['#1a1a2e', '#16213e', '#0f3460'],
            present: ['#0f172a', '#1e293b', '#334155'],
            future: ['#1e1b4b', '#312e81', '#3730a3']
        };
        
        const colors = gradients[timeState];
        gradient.addColorStop(0, colors[0]);
        gradient.addColorStop(0.5, colors[1]);
        gradient.addColorStop(1, colors[2]);
        
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.width, this.height);
        
        // 添加星空效果
        this.renderStars();
    }

    /**
     * 渲染星空效果
     */
    renderStars() {
        this.ctx.fillStyle = '#ffffff';
        
        // 使用固定的随机种子确保星星位置一致
        const starCount = 50;
        for (let i = 0; i < starCount; i++) {
            const x = (i * 137.5) % this.width;
            const y = (i * 73.3) % this.height;
            const size = (i % 3) + 1;
            const alpha = 0.3 + (i % 7) * 0.1;
            
            this.ctx.globalAlpha = alpha;
            this.ctx.beginPath();
            this.ctx.arc(x, y, size, 0, Math.PI * 2);
            this.ctx.fill();
        }
        
        this.ctx.globalAlpha = 1.0;
    }

    /**
     * 渲染菜单
     */
    renderMenu() {
        this.renderBackground();
        this.particleSystem.render(this.ctx);
        
        // 菜单标题
        this.ctx.save();
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = 'bold 48px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText('时空织梦者', this.width / 2, this.height / 2 - 100);
        
        this.ctx.font = '24px Arial';
        this.ctx.fillText('点击开始游戏', this.width / 2, this.height / 2 + 50);
        this.ctx.restore();
    }

    /**
     * 渲染加载画面
     */
    renderLoading() {
        this.ctx.fillStyle = '#0f172a';
        this.ctx.fillRect(0, 0, this.width, this.height);
        
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = '24px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText('游戏加载中...', this.width / 2, this.height / 2);
    }

    /**
     * 渲染暂停覆盖层
     */
    renderPauseOverlay() {
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        this.ctx.fillRect(0, 0, this.width, this.height);
        
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = 'bold 36px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText('游戏暂停', this.width / 2, this.height / 2);
        
        this.ctx.font = '18px Arial';
        this.ctx.fillText('按空格键继续', this.width / 2, this.height / 2 + 50);
    }

    /**
     * 渲染调试信息
     */
    renderDebugInfo() {
        this.ctx.save();
        this.ctx.fillStyle = '#00ff00';
        this.ctx.font = '12px monospace';
        this.ctx.textAlign = 'left';
        
        const debugInfo = [
            `FPS: ${Math.round(1 / this.deltaTime)}`,
            `更新时间: ${this.performance.updateTime.toFixed(2)}ms`,
            `渲染时间: ${this.performance.renderTime.toFixed(2)}ms`,
            `物理对象: ${this.physicsWorld?.stats.activeObjects || 0}`,
            `粒子数量: ${this.particleSystem?.particles.length || 0}`,
            `锚点数量: ${this.anchorManager?.anchors.length || 0}`,
            `时间状态: ${this.timeline?.currentState || 'unknown'}`,
            `时间能量: ${Math.floor(this.timeline?.timeEnergy || 0)}`
        ];
        
        debugInfo.forEach((info, index) => {
            this.ctx.fillText(info, 10, 20 + index * 15);
        });
        
        this.ctx.restore();
    }

    /**
     * 更新性能统计
     */
    updatePerformanceStats() {
        this.performance.frameTime = this.deltaTime * 1000;
        
        // 每秒更新一次内存使用情况
        if (this.frameCount % 60 === 0 && performance.memory) {
            this.performance.memoryUsage = performance.memory.usedJSHeapSize / 1024 / 1024;
        }
    }

    /**
     * 更新游戏UI
     */
    updateGameUI() {
        // 更新分数显示
        const scoreElement = DOMUtils.$('#score');
        if (scoreElement) {
            scoreElement.textContent = this.score;
        }
        
        // 更新关卡显示
        const levelElement = DOMUtils.$('#level');
        if (levelElement) {
            levelElement.textContent = this.currentLevel;
        }
        
        // 更新生命值显示
        const livesElement = DOMUtils.$('#lives');
        if (livesElement) {
            livesElement.textContent = this.lives;
        }
        
        // 更新游戏时间显示
        const timeElement = DOMUtils.$('#game-time');
        if (timeElement) {
            timeElement.textContent = TimeUtils.formatTime(this.gameTime);
        }
    }

    /**
     * 检查胜利条件
     */
    checkVictoryConditions() {
        const currentLevel = levelManager.getCurrentLevel();
        if (!currentLevel) return;

        // 构建游戏状态对象
        const gameState = {
            connections: this.anchorManager.connections.length,
            gameTime: this.gameTime,
            currentEnergy: this.timeline.timeEnergy,
            initialEnergy: this.timeline.maxEnergy,
            timeStatesUsed: this.timeline.getUsedTimeStates ? this.timeline.getUsedTimeStates() : ['present'],
            orbsCollected: this.physicsWorld.getCollectedOrbs ? this.physicsWorld.getCollectedOrbs() : 0,
            anchorsCreated: this.anchorManager.anchors.length,
            anchorsUsed: this.anchorManager.connections.length * 2
        };

        // 检查关卡完成条件
        const completionResult = levelManager.completeLevel(gameState);
        if (completionResult.success) {
            this.victory();
        }
    }

    /**
     * 设置游戏状态
     * @param {string} newState - 新状态
     */
    setState(newState) {
        this.previousState = this.state;
        this.state = newState;
        
        console.log(`游戏状态切换: ${this.previousState} -> ${newState}`);
        
        // 状态切换处理
        this.onStateChange(newState, this.previousState);
    }

    /**
     * 状态切换处理
     * @param {string} newState - 新状态
     * @param {string} oldState - 旧状态
     */
    onStateChange(newState, oldState) {
        // 隐藏所有菜单
        this.hideAllMenus();
        
        switch (newState) {
            case 'playing':
                this.showGameUI();
                break;
            case 'menu':
                this.showMainMenu();
                break;
            case 'paused':
                this.showPauseMenu();
                break;
            case 'victory':
                this.showVictoryMenu();
                break;
        }
    }

    /**
     * 隐藏所有菜单
     */
    hideAllMenus() {
        const menus = ['#main-menu', '#pause-menu', '#settings-menu', '#help-menu', '#victory-menu'];
        menus.forEach(selector => {
            const menu = DOMUtils.$(selector);
            if (menu) {
                menu.style.display = 'none';
            }
        });
    }

    /**
     * 显示游戏UI
     */
    showGameUI() {
        const gameUI = DOMUtils.$('#game-ui');
        if (gameUI) {
            gameUI.style.display = 'block';
        }
    }

    /**
     * 显示主菜单
     */
    showMainMenu() {
        const mainMenu = DOMUtils.$('#main-menu');
        if (mainMenu) {
            mainMenu.style.display = 'flex';
        }
    }

    /**
     * 显示暂停菜单
     */
    showPauseMenu() {
        const pauseMenu = DOMUtils.$('#pause-menu');
        if (pauseMenu) {
            pauseMenu.style.display = 'flex';
        }
    }

    /**
     * 显示胜利菜单
     */
    showVictoryMenu() {
        const victoryMenu = DOMUtils.$('#victory-menu');
        if (victoryMenu) {
            victoryMenu.style.display = 'flex';
        }
    }

    /**
     * 开始游戏
     */
    startGame() {
        // 加载第一关
        if (!levelManager.loadLevel(1)) {
            console.error('无法加载第一关');
            return;
        }

        this.resetGame();
        this.setState('playing');

        // 创建初始对象
        this.createInitialObjects();
    }

    /**
     * 创建初始对象
     */
    createInitialObjects() {
        // 加载当前关卡
        const currentLevel = levelManager.getCurrentLevel();
        if (currentLevel && currentLevel.initialObjects) {
            currentLevel.initialObjects.forEach(obj => {
                if (obj.type === 'energyOrb') {
                    this.physicsWorld.createEnergyOrb(obj.x, obj.y, obj.timeState);
                }
            });
        } else {
            // 创建默认能量球
            for (let i = 0; i < 3; i++) {
                const x = MathUtils.random(50, this.width - 50);
                const y = MathUtils.random(50, this.height - 50);
                const timeState = ['past', 'present', 'future'][i % 3];

                this.physicsWorld.createEnergyOrb(x, y, timeState);
            }
        }

        // 创建背景粒子效果
        this.particleSystem.createTimeParticles(
            this.width / 2, this.height / 2, 'present'
        );
    }

    /**
     * 暂停/继续游戏
     */
    togglePause() {
        if (this.state === 'playing') {
            this.setState('paused');
        } else if (this.state === 'paused') {
            this.setState('playing');
        }
    }

    /**
     * 重置关卡
     */
    resetLevel() {
        // 清除所有对象
        this.anchorManager.clear();
        this.physicsWorld.clear();
        this.particleSystem.clear();
        
        // 重置时间线
        this.timeline.reset();
        
        // 重新创建初始对象
        this.createInitialObjects();
        
        console.log('关卡已重置');
    }

    /**
     * 重置游戏
     */
    resetGame() {
        this.score = 0;
        this.lives = 3;
        this.gameTime = 0;
        this.currentLevel = 1;
        
        this.resetLevel();
    }

    /**
     * 添加分数
     * @param {number} points - 分数
     */
    addScore(points) {
        this.score += points;
        
        // 创建分数粒子效果
        this.particleSystem.createScoreEffect(
            this.input.mouse.x, this.input.mouse.y, points
        );
    }

    /**
     * 胜利
     */
    victory() {
        this.setState('victory');
        this.currentLevel++;
        
        // 保存进度
        this.saveProgress();
        
        console.log(`关卡 ${this.currentLevel - 1} 完成！`);
    }

    /**
     * 显示消息
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型
     */
    showMessage(message, type = 'info') {
        console.log(`[${type.toUpperCase()}] ${message}`);
        
        // 这里可以添加UI消息显示逻辑
    }

    /**
     * 显示错误
     * @param {string} error - 错误信息
     */
    showError(error) {
        console.error(error);
        
        // 这里可以添加错误UI显示逻辑
    }

    /**
     * 显示设置菜单
     */
    showSettings() {
        const settingsMenu = DOMUtils.$('#settings-menu');
        if (settingsMenu) {
            settingsMenu.style.display = 'flex';
        }
    }

    /**
     * 显示帮助菜单
     */
    showHelp() {
        const helpMenu = DOMUtils.$('#help-menu');
        if (helpMenu) {
            helpMenu.style.display = 'flex';
        }
    }

    /**
     * 显示主菜单
     */
    showMenu() {
        this.setState('menu');
    }

    /**
     * 加载设置
     */
    async loadSettings() {
        try {
            const savedSettings = await this.storage.getSettings();
            this.settings = { ...this.settings, ...savedSettings };
            
            console.log('设置已加载:', this.settings);
        } catch (error) {
            console.warn('加载设置失败:', error);
        }
    }

    /**
     * 保存设置
     */
    async saveSettings() {
        try {
            await this.storage.saveSettings(this.settings);
            console.log('设置已保存');
        } catch (error) {
            console.error('保存设置失败:', error);
        }
    }

    /**
     * 保存进度
     */
    async saveProgress() {
        try {
            const gameData = {
                currentLevel: this.currentLevel,
                score: this.score,
                gameTime: this.gameTime,
                timestamp: Date.now()
            };
            
            await this.storage.saveGameData(gameData);
            console.log('进度已保存');
        } catch (error) {
            console.error('保存进度失败:', error);
        }
    }

    /**
     * 获取游戏统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            state: this.state,
            level: this.currentLevel,
            score: this.score,
            gameTime: this.gameTime,
            fps: Math.round(1 / this.deltaTime),
            performance: this.performance,
            physics: this.physicsWorld?.getStats(),
            anchors: this.anchorManager?.getStats(),
            timeline: this.timeline?.getTimeInfo()
        };
    }
}

// 创建全局游戏实例
const game = new TimeWeaverGame();
