/**
 * 时空织梦者 - 时间线系统
 * 负责管理游戏中的时间流控制，包括过去、现在、未来三个时间状态
 */

class Timeline {
    constructor() {
        // 时间状态
        this.currentTime = 0.5; // 0=过去, 0.5=现在, 1=未来
        this.timeStates = ['past', 'present', 'future'];
        this.currentState = 'present';
        
        // 时间流速度
        this.timeSpeed = 1.0;
        this.maxTimeSpeed = 3.0;
        this.minTimeSpeed = 0.1;
        
        // 时间能量系统
        this.timeEnergy = 100;
        this.maxTimeEnergy = 100;
        this.energyRegenRate = 5; // 每秒恢复的能量
        this.energyCosts = {
            timeShift: 10,
            anchorCreate: 15,
            anchorLink: 20,
            timeReverse: 25
        };
        
        // 时间历史记录
        this.timeHistory = [];
        this.maxHistoryLength = 100;
        this.historyIndex = 0;
        
        // 时间锚点
        this.anchors = [];
        this.maxAnchors = 10;
        
        // 事件系统
        this.eventListeners = {};
        
        // 时间扭曲效果
        this.timeWarp = {
            active: false,
            strength: 0,
            duration: 0,
            elapsed: 0
        };
        
        this.init();
    }

    /**
     * 初始化时间线系统
     */
    init() {
        // 记录初始状态
        this.recordTimeState();
        
        // 绑定UI事件
        this.bindEvents();
        
        console.log('时间线系统初始化完成');
    }

    /**
     * 绑定UI事件
     */
    bindEvents() {
        // 时间线按钮
        const pastBtn = DOMUtils.$('#past-btn');
        const presentBtn = DOMUtils.$('#present-btn');
        const futureBtn = DOMUtils.$('#future-btn');
        
        if (pastBtn) {
            DOMUtils.addEvent(pastBtn, 'click', () => this.setTimeState('past'));
        }
        if (presentBtn) {
            DOMUtils.addEvent(presentBtn, 'click', () => this.setTimeState('present'));
        }
        if (futureBtn) {
            DOMUtils.addEvent(futureBtn, 'click', () => this.setTimeState('future'));
        }
        
        // 时间滑块
        const timeSlider = DOMUtils.$('#time-slider');
        if (timeSlider) {
            DOMUtils.addEvent(timeSlider, 'input', (e) => {
                this.setTimeValue(parseFloat(e.target.value) / 100);
            });
        }
        
        // 移动端控制
        const mobilePast = DOMUtils.$('#mobile-past');
        const mobilePresent = DOMUtils.$('#mobile-present');
        const mobileFuture = DOMUtils.$('#mobile-future');
        
        if (mobilePast) {
            DOMUtils.addEvent(mobilePast, 'click', () => this.setTimeState('past'));
        }
        if (mobilePresent) {
            DOMUtils.addEvent(mobilePresent, 'click', () => this.setTimeState('present'));
        }
        if (mobileFuture) {
            DOMUtils.addEvent(mobileFuture, 'click', () => this.setTimeState('future'));
        }
    }

    /**
     * 设置时间状态
     * @param {string} state - 时间状态 (past, present, future)
     */
    setTimeState(state) {
        if (!this.timeStates.includes(state)) {
            console.warn('无效的时间状态:', state);
            return;
        }
        
        // 检查能量消耗
        if (state !== this.currentState && !this.consumeEnergy('timeShift')) {
            this.emit('energyInsufficient', { action: 'timeShift' });
            return;
        }
        
        const oldState = this.currentState;
        this.currentState = state;
        
        // 更新时间值
        switch (state) {
            case 'past':
                this.currentTime = 0.2;
                break;
            case 'present':
                this.currentTime = 0.5;
                break;
            case 'future':
                this.currentTime = 0.8;
                break;
        }
        
        // 记录状态变化
        this.recordTimeState();
        
        // 更新UI
        this.updateUI();
        
        // 触发事件
        this.emit('timeStateChanged', {
            oldState,
            newState: state,
            timeValue: this.currentTime
        });
        
        console.log(`时间状态切换: ${oldState} -> ${state}`);
    }

    /**
     * 设置精确的时间值
     * @param {number} value - 时间值 (0-1)
     */
    setTimeValue(value) {
        value = MathUtils.clamp(value, 0, 1);
        
        const oldTime = this.currentTime;
        this.currentTime = value;
        
        // 根据时间值确定状态
        if (value < 0.33) {
            this.currentState = 'past';
        } else if (value < 0.67) {
            this.currentState = 'present';
        } else {
            this.currentState = 'future';
        }
        
        // 记录状态
        this.recordTimeState();
        
        // 更新UI
        this.updateUI();
        
        // 触发事件
        this.emit('timeValueChanged', {
            oldValue: oldTime,
            newValue: value,
            state: this.currentState
        });
    }

    /**
     * 创建时间锚点
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {Object} options - 锚点选项
     * @returns {Object|null} 创建的锚点或null
     */
    createAnchor(x, y, options = {}) {
        if (this.anchors.length >= this.maxAnchors) {
            this.emit('anchorLimitReached');
            return null;
        }
        
        if (!this.consumeEnergy('anchorCreate')) {
            this.emit('energyInsufficient', { action: 'anchorCreate' });
            return null;
        }
        
        const anchor = {
            id: Date.now() + Math.random(),
            x,
            y,
            timeState: this.currentState,
            timeValue: this.currentTime,
            connections: [],
            active: true,
            createdAt: Date.now(),
            ...options
        };
        
        this.anchors.push(anchor);
        
        // 触发事件
        this.emit('anchorCreated', anchor);
        
        console.log('时间锚点已创建:', anchor);
        return anchor;
    }

    /**
     * 连接两个时间锚点
     * @param {string} anchorId1 - 第一个锚点ID
     * @param {string} anchorId2 - 第二个锚点ID
     * @returns {boolean} 是否连接成功
     */
    linkAnchors(anchorId1, anchorId2) {
        if (!this.consumeEnergy('anchorLink')) {
            this.emit('energyInsufficient', { action: 'anchorLink' });
            return false;
        }
        
        const anchor1 = this.anchors.find(a => a.id === anchorId1);
        const anchor2 = this.anchors.find(a => a.id === anchorId2);
        
        if (!anchor1 || !anchor2) {
            console.warn('找不到指定的锚点');
            return false;
        }
        
        // 检查是否已经连接
        if (anchor1.connections.includes(anchorId2)) {
            console.warn('锚点已经连接');
            return false;
        }
        
        // 创建双向连接
        anchor1.connections.push(anchorId2);
        anchor2.connections.push(anchorId1);
        
        // 触发事件
        this.emit('anchorsLinked', { anchor1, anchor2 });
        
        console.log('锚点连接成功:', anchorId1, '<->', anchorId2);
        return true;
    }

    /**
     * 移除时间锚点
     * @param {string} anchorId - 锚点ID
     * @returns {boolean} 是否移除成功
     */
    removeAnchor(anchorId) {
        const anchorIndex = this.anchors.findIndex(a => a.id === anchorId);
        if (anchorIndex === -1) {
            console.warn('找不到指定的锚点');
            return false;
        }
        
        const anchor = this.anchors[anchorIndex];
        
        // 移除所有相关连接
        anchor.connections.forEach(connectedId => {
            const connectedAnchor = this.anchors.find(a => a.id === connectedId);
            if (connectedAnchor) {
                const connectionIndex = connectedAnchor.connections.indexOf(anchorId);
                if (connectionIndex !== -1) {
                    connectedAnchor.connections.splice(connectionIndex, 1);
                }
            }
        });
        
        // 移除锚点
        this.anchors.splice(anchorIndex, 1);
        
        // 触发事件
        this.emit('anchorRemoved', anchor);
        
        console.log('锚点已移除:', anchorId);
        return true;
    }

    /**
     * 时间倒流
     * @param {number} steps - 倒流步数
     * @returns {boolean} 是否成功
     */
    reverseTime(steps = 1) {
        if (!this.consumeEnergy('timeReverse')) {
            this.emit('energyInsufficient', { action: 'timeReverse' });
            return false;
        }
        
        if (this.historyIndex - steps < 0) {
            console.warn('无法倒流更多步数');
            return false;
        }
        
        this.historyIndex -= steps;
        const targetState = this.timeHistory[this.historyIndex];
        
        if (targetState) {
            this.currentTime = targetState.timeValue;
            this.currentState = targetState.state;
            this.updateUI();
            
            this.emit('timeReversed', {
                steps,
                newState: targetState
            });
            
            console.log(`时间倒流 ${steps} 步`);
            return true;
        }
        
        return false;
    }

    /**
     * 消耗时间能量
     * @param {string} action - 动作类型
     * @returns {boolean} 是否有足够能量
     */
    consumeEnergy(action) {
        const cost = this.energyCosts[action] || 0;
        
        if (this.timeEnergy >= cost) {
            this.timeEnergy -= cost;
            this.updateEnergyUI();
            
            this.emit('energyConsumed', {
                action,
                cost,
                remaining: this.timeEnergy
            });
            
            return true;
        }
        
        return false;
    }

    /**
     * 恢复时间能量
     * @param {number} deltaTime - 时间增量
     */
    regenerateEnergy(deltaTime) {
        if (this.timeEnergy < this.maxTimeEnergy) {
            this.timeEnergy = Math.min(
                this.maxTimeEnergy,
                this.timeEnergy + this.energyRegenRate * deltaTime
            );
            this.updateEnergyUI();
        }
    }

    /**
     * 记录时间状态
     */
    recordTimeState() {
        const state = {
            timeValue: this.currentTime,
            state: this.currentState,
            timestamp: Date.now(),
            anchors: [...this.anchors]
        };
        
        // 如果不是在历史中导航，则添加新状态
        if (this.historyIndex === this.timeHistory.length - 1) {
            this.timeHistory.push(state);
            
            // 限制历史长度
            if (this.timeHistory.length > this.maxHistoryLength) {
                this.timeHistory.shift();
            } else {
                this.historyIndex++;
            }
        }
    }

    /**
     * 更新UI显示
     */
    updateUI() {
        // 更新时间线按钮状态
        const buttons = {
            past: DOMUtils.$('#past-btn'),
            present: DOMUtils.$('#present-btn'),
            future: DOMUtils.$('#future-btn')
        };
        
        Object.keys(buttons).forEach(state => {
            const btn = buttons[state];
            if (btn) {
                if (state === this.currentState) {
                    btn.classList.add('active');
                } else {
                    btn.classList.remove('active');
                }
            }
        });
        
        // 更新时间滑块
        const timeSlider = DOMUtils.$('#time-slider');
        if (timeSlider) {
            timeSlider.value = this.currentTime * 100;
        }
        
        // 更新锚点计数
        const anchorCount = DOMUtils.$('#anchor-count');
        if (anchorCount) {
            anchorCount.textContent = this.anchors.length;
        }
    }

    /**
     * 更新能量UI
     */
    updateEnergyUI() {
        const energyFill = DOMUtils.$('#energy-fill');
        const energyText = DOMUtils.$('#energy-text');
        
        if (energyFill) {
            const percentage = (this.timeEnergy / this.maxTimeEnergy) * 100;
            energyFill.style.width = `${percentage}%`;
        }
        
        if (energyText) {
            energyText.textContent = Math.floor(this.timeEnergy);
        }
        
        const timeEnergySpan = DOMUtils.$('#time-energy');
        if (timeEnergySpan) {
            timeEnergySpan.textContent = Math.floor(this.timeEnergy);
        }
    }

    /**
     * 更新时间线系统
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        // 恢复能量
        this.regenerateEnergy(deltaTime);
        
        // 更新时间扭曲效果
        if (this.timeWarp.active) {
            this.timeWarp.elapsed += deltaTime;
            
            if (this.timeWarp.elapsed >= this.timeWarp.duration) {
                this.timeWarp.active = false;
                this.timeWarp.strength = 0;
            }
        }
        
        // 触发更新事件
        this.emit('timelineUpdate', {
            deltaTime,
            currentTime: this.currentTime,
            currentState: this.currentState,
            timeEnergy: this.timeEnergy
        });
    }

    /**
     * 添加事件监听器
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    on(event, callback) {
        if (!this.eventListeners[event]) {
            this.eventListeners[event] = [];
        }
        this.eventListeners[event].push(callback);
    }

    /**
     * 移除事件监听器
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    off(event, callback) {
        if (this.eventListeners[event]) {
            const index = this.eventListeners[event].indexOf(callback);
            if (index !== -1) {
                this.eventListeners[event].splice(index, 1);
            }
        }
    }

    /**
     * 触发事件
     * @param {string} event - 事件名称
     * @param {*} data - 事件数据
     */
    emit(event, data) {
        if (this.eventListeners[event]) {
            this.eventListeners[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error('事件处理器错误:', error);
                }
            });
        }
    }

    /**
     * 获取当前时间状态信息
     * @returns {Object} 时间状态信息
     */
    getTimeInfo() {
        return {
            currentTime: this.currentTime,
            currentState: this.currentState,
            timeEnergy: this.timeEnergy,
            anchors: this.anchors.length,
            historyLength: this.timeHistory.length
        };
    }

    /**
     * 重置时间线系统
     */
    reset() {
        this.currentTime = 0.5;
        this.currentState = 'present';
        this.timeEnergy = this.maxTimeEnergy;
        this.anchors = [];
        this.timeHistory = [];
        this.historyIndex = 0;
        this.timeWarp.active = false;
        
        this.recordTimeState();
        this.updateUI();
        this.updateEnergyUI();
        
        this.emit('timelineReset');
        
        console.log('时间线系统已重置');
    }
}

// 创建全局时间线实例
const timeline = new Timeline();
