/**
 * 时空织梦者 - 关卡系统
 * 负责关卡的生成、管理和进度跟踪
 */

class Level {
    constructor(id, config) {
        this.id = id;
        this.name = config.name || `关卡 ${id}`;
        this.description = config.description || '';
        this.difficulty = config.difficulty || 1;
        
        // 关卡目标
        this.objectives = config.objectives || [];
        this.requiredConnections = config.requiredConnections || 3;
        this.timeLimit = config.timeLimit || 0; // 0表示无时间限制
        this.maxAnchors = config.maxAnchors || 10;
        
        // 初始设置
        this.initialObjects = config.initialObjects || [];
        this.initialTimeState = config.initialTimeState || 'present';
        this.initialEnergy = config.initialEnergy || 100;
        
        // 环境设置
        this.gravity = config.gravity || { x: 0, y: 0 };
        this.boundaries = config.boundaries || true;
        this.backgroundType = config.backgroundType || 'stars';
        
        // 奖励设置
        this.baseScore = config.baseScore || 100;
        this.timeBonus = config.timeBonus || true;
        this.perfectBonus = config.perfectBonus || 50;
        
        // 解锁条件
        this.unlockConditions = config.unlockConditions || [];
        this.isUnlocked = config.isUnlocked || false;
        
        // 完成状态
        this.isCompleted = false;
        this.bestScore = 0;
        this.bestTime = Infinity;
        this.completionCount = 0;
    }

    /**
     * 检查关卡是否解锁
     * @param {Object} gameProgress - 游戏进度
     * @returns {boolean} 是否解锁
     */
    checkUnlock(gameProgress) {
        if (this.isUnlocked) return true;
        
        return this.unlockConditions.every(condition => {
            switch (condition.type) {
                case 'level_completed':
                    return gameProgress.completedLevels.includes(condition.levelId);
                case 'score_reached':
                    return gameProgress.totalScore >= condition.score;
                case 'time_played':
                    return gameProgress.totalTime >= condition.time;
                default:
                    return false;
            }
        });
    }

    /**
     * 检查关卡完成条件
     * @param {Object} gameState - 当前游戏状态
     * @returns {Object} 完成状态信息
     */
    checkCompletion(gameState) {
        const result = {
            completed: false,
            objectives: [],
            score: 0,
            timeBonus: 0,
            perfectBonus: 0
        };
        
        // 检查每个目标
        this.objectives.forEach(objective => {
            const objectiveResult = this.checkObjective(objective, gameState);
            result.objectives.push(objectiveResult);
        });
        
        // 检查主要完成条件
        const connectionsCompleted = gameState.connections >= this.requiredConnections;
        const timeNotExceeded = this.timeLimit === 0 || gameState.gameTime <= this.timeLimit;
        
        result.completed = connectionsCompleted && timeNotExceeded;
        
        if (result.completed) {
            // 计算分数
            result.score = this.calculateScore(gameState);
            
            // 时间奖励
            if (this.timeBonus && this.timeLimit > 0) {
                const timeRatio = 1 - (gameState.gameTime / this.timeLimit);
                result.timeBonus = Math.floor(this.baseScore * timeRatio * 0.5);
            }
            
            // 完美奖励（所有目标完成）
            const allObjectivesCompleted = result.objectives.every(obj => obj.completed);
            if (allObjectivesCompleted) {
                result.perfectBonus = this.perfectBonus;
            }
        }
        
        return result;
    }

    /**
     * 检查单个目标
     * @param {Object} objective - 目标配置
     * @param {Object} gameState - 游戏状态
     * @returns {Object} 目标完成状态
     */
    checkObjective(objective, gameState) {
        const result = {
            id: objective.id,
            name: objective.name,
            completed: false,
            progress: 0,
            target: objective.target
        };
        
        switch (objective.type) {
            case 'connections':
                result.progress = gameState.connections;
                result.completed = result.progress >= objective.target;
                break;
                
            case 'time_states':
                result.progress = gameState.timeStatesUsed.length;
                result.completed = result.progress >= objective.target;
                break;
                
            case 'energy_efficiency':
                const energyUsed = gameState.initialEnergy - gameState.currentEnergy;
                const efficiency = 1 - (energyUsed / gameState.initialEnergy);
                result.progress = Math.floor(efficiency * 100);
                result.completed = result.progress >= objective.target;
                break;
                
            case 'collect_orbs':
                result.progress = gameState.orbsCollected;
                result.completed = result.progress >= objective.target;
                break;
                
            case 'no_anchor_waste':
                result.progress = gameState.anchorsCreated - gameState.anchorsUsed;
                result.completed = result.progress <= objective.target;
                break;
        }
        
        return result;
    }

    /**
     * 计算关卡分数
     * @param {Object} gameState - 游戏状态
     * @returns {number} 分数
     */
    calculateScore(gameState) {
        let score = this.baseScore;
        
        // 连接奖励
        score += gameState.connections * 25;
        
        // 效率奖励
        const energyEfficiency = gameState.currentEnergy / gameState.initialEnergy;
        score += Math.floor(energyEfficiency * 50);
        
        // 时间奖励
        if (this.timeLimit > 0) {
            const timeRatio = Math.max(0, 1 - gameState.gameTime / this.timeLimit);
            score += Math.floor(timeRatio * 100);
        }
        
        return score;
    }

    /**
     * 更新最佳记录
     * @param {number} score - 分数
     * @param {number} time - 时间
     */
    updateBestRecord(score, time) {
        if (score > this.bestScore) {
            this.bestScore = score;
        }
        
        if (time < this.bestTime) {
            this.bestTime = time;
        }
        
        this.completionCount++;
        this.isCompleted = true;
    }

    /**
     * 获取关卡信息
     * @returns {Object} 关卡信息
     */
    getInfo() {
        return {
            id: this.id,
            name: this.name,
            description: this.description,
            difficulty: this.difficulty,
            isUnlocked: this.isUnlocked,
            isCompleted: this.isCompleted,
            bestScore: this.bestScore,
            bestTime: this.bestTime === Infinity ? null : this.bestTime,
            completionCount: this.completionCount,
            objectives: this.objectives.map(obj => ({
                id: obj.id,
                name: obj.name,
                type: obj.type,
                target: obj.target
            }))
        };
    }
}

/**
 * 关卡管理器
 */
class LevelManager {
    constructor() {
        this.levels = [];
        this.currentLevel = null;
        this.currentLevelId = 1;
        
        // 游戏进度
        this.gameProgress = {
            completedLevels: [],
            totalScore: 0,
            totalTime: 0,
            unlockedLevels: [1] // 第一关默认解锁
        };
        
        this.init();
    }

    /**
     * 初始化关卡管理器
     */
    init() {
        this.createLevels();
        console.log('关卡管理器初始化完成');
    }

    /**
     * 创建所有关卡
     */
    createLevels() {
        // 关卡1：基础教程
        this.addLevel(1, {
            name: '时间初探',
            description: '学习创建时间锚点和基本连接',
            difficulty: 1,
            requiredConnections: 2,
            maxAnchors: 5,
            objectives: [
                {
                    id: 'tutorial_connections',
                    name: '创建2个时间连接',
                    type: 'connections',
                    target: 2
                }
            ],
            initialObjects: [
                { type: 'energyOrb', x: 200, y: 150, timeState: 'present' },
                { type: 'energyOrb', x: 400, y: 250, timeState: 'present' }
            ],
            baseScore: 100,
            isUnlocked: true
        });

        // 关卡2：时间状态
        this.addLevel(2, {
            name: '时间之流',
            description: '探索过去、现在、未来三种时间状态',
            difficulty: 2,
            requiredConnections: 3,
            maxAnchors: 8,
            objectives: [
                {
                    id: 'use_time_states',
                    name: '使用所有时间状态',
                    type: 'time_states',
                    target: 3
                },
                {
                    id: 'efficient_energy',
                    name: '保持80%以上能量效率',
                    type: 'energy_efficiency',
                    target: 80
                }
            ],
            initialObjects: [
                { type: 'energyOrb', x: 150, y: 100, timeState: 'past' },
                { type: 'energyOrb', x: 300, y: 200, timeState: 'present' },
                { type: 'energyOrb', x: 450, y: 150, timeState: 'future' }
            ],
            baseScore: 200,
            unlockConditions: [
                { type: 'level_completed', levelId: 1 }
            ]
        });

        // 关卡3：复杂网络
        this.addLevel(3, {
            name: '时空网络',
            description: '构建复杂的时间锚点网络',
            difficulty: 3,
            requiredConnections: 5,
            maxAnchors: 12,
            timeLimit: 120, // 2分钟时间限制
            objectives: [
                {
                    id: 'complex_network',
                    name: '创建5个时间连接',
                    type: 'connections',
                    target: 5
                },
                {
                    id: 'collect_orbs',
                    name: '收集3个能量球',
                    type: 'collect_orbs',
                    target: 3
                },
                {
                    id: 'no_waste',
                    name: '浪费锚点不超过2个',
                    type: 'no_anchor_waste',
                    target: 2
                }
            ],
            initialObjects: [
                { type: 'energyOrb', x: 100, y: 100, timeState: 'past' },
                { type: 'energyOrb', x: 200, y: 300, timeState: 'present' },
                { type: 'energyOrb', x: 400, y: 150, timeState: 'future' },
                { type: 'energyOrb', x: 500, y: 250, timeState: 'past' },
                { type: 'energyOrb', x: 300, y: 100, timeState: 'future' }
            ],
            baseScore: 300,
            unlockConditions: [
                { type: 'level_completed', levelId: 2 }
            ]
        });

        // 关卡4：时间挑战
        this.addLevel(4, {
            name: '时间大师',
            description: '在重力环境下完成复杂的时间操控',
            difficulty: 4,
            requiredConnections: 6,
            maxAnchors: 15,
            timeLimit: 180,
            gravity: { x: 0, y: 50 }, // 添加重力
            objectives: [
                {
                    id: 'master_connections',
                    name: '创建6个时间连接',
                    type: 'connections',
                    target: 6
                },
                {
                    id: 'time_efficiency',
                    name: '在3分钟内完成',
                    type: 'time_limit',
                    target: 180
                },
                {
                    id: 'energy_master',
                    name: '保持90%以上能量效率',
                    type: 'energy_efficiency',
                    target: 90
                }
            ],
            initialObjects: [
                { type: 'energyOrb', x: 150, y: 50, timeState: 'past' },
                { type: 'energyOrb', x: 250, y: 100, timeState: 'present' },
                { type: 'energyOrb', x: 350, y: 50, timeState: 'future' },
                { type: 'energyOrb', x: 450, y: 100, timeState: 'past' },
                { type: 'energyOrb', x: 200, y: 200, timeState: 'present' },
                { type: 'energyOrb', x: 400, y: 200, timeState: 'future' }
            ],
            baseScore: 500,
            unlockConditions: [
                { type: 'level_completed', levelId: 3 },
                { type: 'score_reached', score: 800 }
            ]
        });

        // 关卡5：终极挑战
        this.addLevel(5, {
            name: '时空织梦者',
            description: '成为真正的时空织梦者',
            difficulty: 5,
            requiredConnections: 8,
            maxAnchors: 20,
            timeLimit: 300,
            gravity: { x: 0, y: 30 },
            objectives: [
                {
                    id: 'ultimate_network',
                    name: '创建8个时间连接',
                    type: 'connections',
                    target: 8
                },
                {
                    id: 'collect_all_orbs',
                    name: '收集所有能量球',
                    type: 'collect_orbs',
                    target: 8
                },
                {
                    id: 'perfect_efficiency',
                    name: '保持95%以上能量效率',
                    type: 'energy_efficiency',
                    target: 95
                },
                {
                    id: 'no_anchor_waste_ultimate',
                    name: '不浪费任何锚点',
                    type: 'no_anchor_waste',
                    target: 0
                }
            ],
            initialObjects: [
                { type: 'energyOrb', x: 100, y: 50, timeState: 'past' },
                { type: 'energyOrb', x: 200, y: 80, timeState: 'present' },
                { type: 'energyOrb', x: 300, y: 50, timeState: 'future' },
                { type: 'energyOrb', x: 400, y: 80, timeState: 'past' },
                { type: 'energyOrb', x: 500, y: 50, timeState: 'present' },
                { type: 'energyOrb', x: 150, y: 200, timeState: 'future' },
                { type: 'energyOrb', x: 350, y: 220, timeState: 'past' },
                { type: 'energyOrb', x: 450, y: 200, timeState: 'future' }
            ],
            baseScore: 1000,
            perfectBonus: 200,
            unlockConditions: [
                { type: 'level_completed', levelId: 4 },
                { type: 'score_reached', score: 2000 }
            ]
        });

        console.log(`已创建 ${this.levels.length} 个关卡`);
    }

    /**
     * 添加关卡
     * @param {number} id - 关卡ID
     * @param {Object} config - 关卡配置
     */
    addLevel(id, config) {
        const level = new Level(id, config);
        this.levels.push(level);
    }

    /**
     * 获取关卡
     * @param {number} levelId - 关卡ID
     * @returns {Level|null} 关卡对象
     */
    getLevel(levelId) {
        return this.levels.find(level => level.id === levelId) || null;
    }

    /**
     * 获取当前关卡
     * @returns {Level|null} 当前关卡
     */
    getCurrentLevel() {
        return this.currentLevel;
    }

    /**
     * 加载关卡
     * @param {number} levelId - 关卡ID
     * @returns {boolean} 是否加载成功
     */
    loadLevel(levelId) {
        const level = this.getLevel(levelId);
        if (!level) {
            console.error('关卡不存在:', levelId);
            return false;
        }

        if (!level.checkUnlock(this.gameProgress)) {
            console.warn('关卡未解锁:', levelId);
            return false;
        }

        this.currentLevel = level;
        this.currentLevelId = levelId;
        
        console.log('关卡已加载:', level.name);
        return true;
    }

    /**
     * 完成当前关卡
     * @param {Object} gameState - 游戏状态
     * @returns {Object} 完成结果
     */
    completeLevel(gameState) {
        if (!this.currentLevel) {
            return { success: false, message: '没有当前关卡' };
        }

        const completionResult = this.currentLevel.checkCompletion(gameState);
        
        if (completionResult.completed) {
            // 更新关卡记录
            const totalScore = completionResult.score + completionResult.timeBonus + completionResult.perfectBonus;
            this.currentLevel.updateBestRecord(totalScore, gameState.gameTime);
            
            // 更新游戏进度
            if (!this.gameProgress.completedLevels.includes(this.currentLevelId)) {
                this.gameProgress.completedLevels.push(this.currentLevelId);
            }
            
            this.gameProgress.totalScore += totalScore;
            this.gameProgress.totalTime += gameState.gameTime;
            
            // 解锁下一关卡
            this.unlockNextLevels();
            
            return {
                success: true,
                result: completionResult,
                totalScore,
                newUnlocks: this.checkNewUnlocks()
            };
        }
        
        return {
            success: false,
            result: completionResult,
            message: '关卡未完成'
        };
    }

    /**
     * 解锁下一关卡
     */
    unlockNextLevels() {
        this.levels.forEach(level => {
            if (!level.isUnlocked && level.checkUnlock(this.gameProgress)) {
                level.isUnlocked = true;
                this.gameProgress.unlockedLevels.push(level.id);
                console.log('新关卡解锁:', level.name);
            }
        });
    }

    /**
     * 检查新解锁的关卡
     * @returns {Level[]} 新解锁的关卡
     */
    checkNewUnlocks() {
        return this.levels.filter(level => 
            level.isUnlocked && !this.gameProgress.unlockedLevels.includes(level.id)
        );
    }

    /**
     * 获取关卡列表
     * @returns {Object[]} 关卡信息列表
     */
    getLevelList() {
        return this.levels.map(level => level.getInfo());
    }

    /**
     * 获取游戏进度
     * @returns {Object} 游戏进度
     */
    getGameProgress() {
        return {
            ...this.gameProgress,
            currentLevel: this.currentLevelId,
            totalLevels: this.levels.length,
            completionRate: (this.gameProgress.completedLevels.length / this.levels.length) * 100
        };
    }

    /**
     * 重置进度
     */
    resetProgress() {
        this.gameProgress = {
            completedLevels: [],
            totalScore: 0,
            totalTime: 0,
            unlockedLevels: [1]
        };
        
        this.levels.forEach(level => {
            level.isCompleted = false;
            level.bestScore = 0;
            level.bestTime = Infinity;
            level.completionCount = 0;
            level.isUnlocked = level.id === 1;
        });
        
        console.log('游戏进度已重置');
    }

    /**
     * 加载进度数据
     * @param {Object} progressData - 进度数据
     */
    loadProgress(progressData) {
        if (progressData) {
            this.gameProgress = { ...this.gameProgress, ...progressData };
            
            // 更新关卡状态
            this.levels.forEach(level => {
                const levelData = progressData.levels?.[level.id];
                if (levelData) {
                    level.isCompleted = levelData.isCompleted || false;
                    level.bestScore = levelData.bestScore || 0;
                    level.bestTime = levelData.bestTime || Infinity;
                    level.completionCount = levelData.completionCount || 0;
                }
                
                level.isUnlocked = this.gameProgress.unlockedLevels.includes(level.id);
            });
            
            console.log('游戏进度已加载');
        }
    }

    /**
     * 保存进度数据
     * @returns {Object} 进度数据
     */
    saveProgress() {
        const progressData = {
            ...this.gameProgress,
            levels: {}
        };
        
        this.levels.forEach(level => {
            progressData.levels[level.id] = {
                isCompleted: level.isCompleted,
                bestScore: level.bestScore,
                bestTime: level.bestTime === Infinity ? null : level.bestTime,
                completionCount: level.completionCount
            };
        });
        
        return progressData;
    }
}

// 创建全局关卡管理器实例
const levelManager = new LevelManager();
