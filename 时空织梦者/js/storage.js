/**
 * 时空织梦者 - 存储系统
 * 负责游戏数据的本地存储和读取，支持游戏状态保存和载入
 */

class GameStorage {
    constructor() {
        this.storageKey = 'timeWeaver_gameData';
        this.settingsKey = 'timeWeaver_settings';
        this.achievementsKey = 'timeWeaver_achievements';
        this.statisticsKey = 'timeWeaver_statistics';
        
        // 默认设置
        this.defaultSettings = {
            sfxVolume: 70,
            bgmVolume: 50,
            particleQuality: 'medium',
            autoSave: true,
            language: 'zh-CN',
            theme: 'dark',
            showTutorial: true,
            enableVibration: true
        };
        
        // 默认统计数据
        this.defaultStatistics = {
            totalPlayTime: 0,
            levelsCompleted: 0,
            anchorsPlaced: 0,
            timeManipulations: 0,
            perfectScores: 0,
            totalScore: 0,
            gamesPlayed: 0,
            averageCompletionTime: 0
        };
        
        this.init();
    }

    /**
     * 初始化存储系统
     */
    init() {
        try {
            // 检查localStorage是否可用
            if (!this.isStorageAvailable()) {
                console.warn('本地存储不可用，游戏数据将无法保存');
                return;
            }
            
            // 初始化设置
            if (!this.hasSettings()) {
                this.saveSettings(this.defaultSettings);
            }
            
            // 初始化统计数据
            if (!this.hasStatistics()) {
                this.saveStatistics(this.defaultStatistics);
            }
            
            console.log('存储系统初始化完成');
        } catch (error) {
            console.error('存储系统初始化失败:', error);
        }
    }

    /**
     * 检查localStorage是否可用
     * @returns {boolean} 是否可用
     */
    isStorageAvailable() {
        try {
            const test = '__storage_test__';
            localStorage.setItem(test, test);
            localStorage.removeItem(test);
            return true;
        } catch (e) {
            return false;
        }
    }

    /**
     * 保存游戏数据
     * @param {Object} gameData - 游戏数据对象
     * @returns {boolean} 是否保存成功
     */
    saveGameData(gameData) {
        try {
            const dataToSave = {
                ...gameData,
                timestamp: Date.now(),
                version: '1.0.0'
            };
            
            const jsonData = JSON.stringify(dataToSave);
            localStorage.setItem(this.storageKey, jsonData);
            
            console.log('游戏数据保存成功');
            return true;
        } catch (error) {
            console.error('保存游戏数据失败:', error);
            return false;
        }
    }

    /**
     * 载入游戏数据
     * @returns {Object|null} 游戏数据对象或null
     */
    loadGameData() {
        try {
            const jsonData = localStorage.getItem(this.storageKey);
            if (!jsonData) {
                console.log('没有找到保存的游戏数据');
                return null;
            }
            
            const gameData = JSON.parse(jsonData);
            
            // 验证数据完整性
            if (!this.validateGameData(gameData)) {
                console.warn('游戏数据验证失败，可能已损坏');
                return null;
            }
            
            console.log('游戏数据载入成功');
            return gameData;
        } catch (error) {
            console.error('载入游戏数据失败:', error);
            return null;
        }
    }

    /**
     * 验证游戏数据完整性
     * @param {Object} gameData - 游戏数据
     * @returns {boolean} 是否有效
     */
    validateGameData(gameData) {
        const requiredFields = ['currentLevel', 'score', 'timestamp'];
        return requiredFields.every(field => gameData.hasOwnProperty(field));
    }

    /**
     * 检查是否有保存的游戏数据
     * @returns {boolean} 是否有保存数据
     */
    hasGameData() {
        return localStorage.getItem(this.storageKey) !== null;
    }

    /**
     * 删除游戏数据
     * @returns {boolean} 是否删除成功
     */
    deleteGameData() {
        try {
            localStorage.removeItem(this.storageKey);
            console.log('游戏数据删除成功');
            return true;
        } catch (error) {
            console.error('删除游戏数据失败:', error);
            return false;
        }
    }

    /**
     * 保存游戏设置
     * @param {Object} settings - 设置对象
     * @returns {boolean} 是否保存成功
     */
    saveSettings(settings) {
        try {
            const settingsToSave = {
                ...this.defaultSettings,
                ...settings,
                timestamp: Date.now()
            };
            
            const jsonData = JSON.stringify(settingsToSave);
            localStorage.setItem(this.settingsKey, jsonData);
            
            console.log('游戏设置保存成功');
            return true;
        } catch (error) {
            console.error('保存游戏设置失败:', error);
            return false;
        }
    }

    /**
     * 载入游戏设置
     * @returns {Object} 设置对象
     */
    loadSettings() {
        try {
            const jsonData = localStorage.getItem(this.settingsKey);
            if (!jsonData) {
                return this.defaultSettings;
            }
            
            const settings = JSON.parse(jsonData);
            return { ...this.defaultSettings, ...settings };
        } catch (error) {
            console.error('载入游戏设置失败:', error);
            return this.defaultSettings;
        }
    }

    /**
     * 检查是否有保存的设置
     * @returns {boolean} 是否有设置
     */
    hasSettings() {
        return localStorage.getItem(this.settingsKey) !== null;
    }

    /**
     * 保存统计数据
     * @param {Object} statistics - 统计数据对象
     * @returns {boolean} 是否保存成功
     */
    saveStatistics(statistics) {
        try {
            const statsToSave = {
                ...this.defaultStatistics,
                ...statistics,
                timestamp: Date.now()
            };
            
            const jsonData = JSON.stringify(statsToSave);
            localStorage.setItem(this.statisticsKey, jsonData);
            
            return true;
        } catch (error) {
            console.error('保存统计数据失败:', error);
            return false;
        }
    }

    /**
     * 载入统计数据
     * @returns {Object} 统计数据对象
     */
    loadStatistics() {
        try {
            const jsonData = localStorage.getItem(this.statisticsKey);
            if (!jsonData) {
                return this.defaultStatistics;
            }
            
            const statistics = JSON.parse(jsonData);
            return { ...this.defaultStatistics, ...statistics };
        } catch (error) {
            console.error('载入统计数据失败:', error);
            return this.defaultStatistics;
        }
    }

    /**
     * 检查是否有统计数据
     * @returns {boolean} 是否有统计数据
     */
    hasStatistics() {
        return localStorage.getItem(this.statisticsKey) !== null;
    }

    /**
     * 更新统计数据
     * @param {Object} updates - 要更新的统计数据
     */
    updateStatistics(updates) {
        const currentStats = this.loadStatistics();
        const updatedStats = { ...currentStats, ...updates };
        this.saveStatistics(updatedStats);
    }

    /**
     * 保存成就数据
     * @param {Object} achievements - 成就数据
     * @returns {boolean} 是否保存成功
     */
    saveAchievements(achievements) {
        try {
            const jsonData = JSON.stringify(achievements);
            localStorage.setItem(this.achievementsKey, jsonData);
            return true;
        } catch (error) {
            console.error('保存成就数据失败:', error);
            return false;
        }
    }

    /**
     * 载入成就数据
     * @returns {Object} 成就数据对象
     */
    loadAchievements() {
        try {
            const jsonData = localStorage.getItem(this.achievementsKey);
            if (!jsonData) {
                return {};
            }
            
            return JSON.parse(jsonData);
        } catch (error) {
            console.error('载入成就数据失败:', error);
            return {};
        }
    }

    /**
     * 导出所有游戏数据
     * @returns {Object} 所有游戏数据
     */
    exportAllData() {
        return {
            gameData: this.loadGameData(),
            settings: this.loadSettings(),
            statistics: this.loadStatistics(),
            achievements: this.loadAchievements(),
            exportTime: Date.now()
        };
    }

    /**
     * 导入游戏数据
     * @param {Object} data - 要导入的数据
     * @returns {boolean} 是否导入成功
     */
    importAllData(data) {
        try {
            if (data.gameData) {
                this.saveGameData(data.gameData);
            }
            if (data.settings) {
                this.saveSettings(data.settings);
            }
            if (data.statistics) {
                this.saveStatistics(data.statistics);
            }
            if (data.achievements) {
                this.saveAchievements(data.achievements);
            }
            
            console.log('数据导入成功');
            return true;
        } catch (error) {
            console.error('数据导入失败:', error);
            return false;
        }
    }

    /**
     * 清除所有游戏数据
     * @returns {boolean} 是否清除成功
     */
    clearAllData() {
        try {
            localStorage.removeItem(this.storageKey);
            localStorage.removeItem(this.settingsKey);
            localStorage.removeItem(this.statisticsKey);
            localStorage.removeItem(this.achievementsKey);
            
            console.log('所有游戏数据已清除');
            return true;
        } catch (error) {
            console.error('清除游戏数据失败:', error);
            return false;
        }
    }

    /**
     * 获取存储使用情况
     * @returns {Object} 存储使用情况
     */
    getStorageUsage() {
        try {
            let totalSize = 0;
            const keys = [this.storageKey, this.settingsKey, this.statisticsKey, this.achievementsKey];
            
            keys.forEach(key => {
                const data = localStorage.getItem(key);
                if (data) {
                    totalSize += data.length;
                }
            });
            
            return {
                totalSize,
                formattedSize: this.formatBytes(totalSize),
                keys: keys.length
            };
        } catch (error) {
            console.error('获取存储使用情况失败:', error);
            return { totalSize: 0, formattedSize: '0 B', keys: 0 };
        }
    }

    /**
     * 格式化字节数
     * @param {number} bytes - 字节数
     * @returns {string} 格式化后的字符串
     */
    formatBytes(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// 创建全局存储实例
const gameStorage = new GameStorage();
