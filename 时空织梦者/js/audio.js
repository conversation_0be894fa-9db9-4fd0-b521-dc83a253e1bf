/**
 * 时空织梦者 - 音频系统
 * 负责游戏音效和背景音乐的播放管理
 */

class AudioManager {
    constructor() {
        // 音频上下文
        this.audioContext = null;
        this.masterGain = null;
        this.sfxGain = null;
        this.musicGain = null;
        
        // 音频缓冲区
        this.audioBuffers = new Map();
        this.loadedSounds = new Set();
        
        // 播放中的音频
        this.playingSources = new Map();
        this.currentMusic = null;
        
        // 设置
        this.masterVolume = 0.7;
        this.sfxVolume = 0.8;
        this.musicVolume = 0.5;
        this.enabled = true;
        
        // 音频文件配置
        this.soundConfig = {
            // 界面音效
            click: { frequency: 800, duration: 0.1, type: 'sine' },
            hover: { frequency: 600, duration: 0.05, type: 'sine' },
            
            // 游戏音效
            anchorCreate: { frequency: 440, duration: 0.2, type: 'triangle' },
            anchorConnect: { frequency: 660, duration: 0.3, type: 'sawtooth' },
            anchorRemove: { frequency: 220, duration: 0.15, type: 'square' },
            
            // 时间音效
            timeShift: { frequency: [400, 800, 600], duration: 0.4, type: 'sine' },
            timeRewind: { frequency: [800, 400], duration: 0.6, type: 'triangle' },
            energyLow: { frequency: 200, duration: 0.8, type: 'sawtooth' },
            
            // 成功音效
            levelComplete: { frequency: [523, 659, 784], duration: 1.0, type: 'sine' },
            scoreGain: { frequency: [440, 554, 659], duration: 0.5, type: 'triangle' },
            
            // 错误音效
            error: { frequency: 150, duration: 0.3, type: 'square' },
            energyEmpty: { frequency: [300, 200, 100], duration: 0.8, type: 'sawtooth' }
        };
        
        this.init();
    }

    /**
     * 初始化音频系统
     */
    async init() {
        try {
            // 创建音频上下文
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            // 创建增益节点
            this.masterGain = this.audioContext.createGain();
            this.sfxGain = this.audioContext.createGain();
            this.musicGain = this.audioContext.createGain();
            
            // 连接增益节点
            this.sfxGain.connect(this.masterGain);
            this.musicGain.connect(this.masterGain);
            this.masterGain.connect(this.audioContext.destination);
            
            // 设置初始音量
            this.updateVolumes();
            
            console.log('音频系统初始化完成');
            
        } catch (error) {
            console.warn('音频系统初始化失败:', error);
            this.enabled = false;
        }
    }

    /**
     * 更新音量设置
     */
    updateVolumes() {
        if (!this.enabled || !this.masterGain) return;
        
        this.masterGain.gain.value = this.masterVolume;
        this.sfxGain.gain.value = this.sfxVolume;
        this.musicGain.gain.value = this.musicVolume;
    }

    /**
     * 恢复音频上下文（用户交互后）
     */
    async resumeAudioContext() {
        if (this.audioContext && this.audioContext.state === 'suspended') {
            try {
                await this.audioContext.resume();
                console.log('音频上下文已恢复');
            } catch (error) {
                console.warn('恢复音频上下文失败:', error);
            }
        }
    }

    /**
     * 生成音效
     * @param {string} soundName - 音效名称
     * @param {Object} options - 播放选项
     */
    playSound(soundName, options = {}) {
        if (!this.enabled || !this.audioContext) return;
        
        const config = this.soundConfig[soundName];
        if (!config) {
            console.warn('未找到音效配置:', soundName);
            return;
        }
        
        try {
            this.resumeAudioContext();
            
            if (Array.isArray(config.frequency)) {
                // 播放和弦或序列
                this.playChord(config, options);
            } else {
                // 播放单音
                this.playTone(config, options);
            }
            
        } catch (error) {
            console.warn('播放音效失败:', error);
        }
    }

    /**
     * 播放单音
     * @param {Object} config - 音效配置
     * @param {Object} options - 播放选项
     */
    playTone(config, options = {}) {
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        
        // 设置振荡器
        oscillator.type = config.type || 'sine';
        oscillator.frequency.setValueAtTime(config.frequency, this.audioContext.currentTime);
        
        // 设置音量包络
        const duration = options.duration || config.duration || 0.2;
        const volume = options.volume || 0.3;
        
        gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
        gainNode.gain.linearRampToValueAtTime(volume, this.audioContext.currentTime + 0.01);
        gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + duration);
        
        // 连接节点
        oscillator.connect(gainNode);
        gainNode.connect(this.sfxGain);
        
        // 播放
        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + duration);
        
        // 清理
        oscillator.onended = () => {
            oscillator.disconnect();
            gainNode.disconnect();
        };
    }

    /**
     * 播放和弦或音序
     * @param {Object} config - 音效配置
     * @param {Object} options - 播放选项
     */
    playChord(config, options = {}) {
        const frequencies = config.frequency;
        const duration = options.duration || config.duration || 0.5;
        const isSequence = options.sequence || false;
        
        frequencies.forEach((freq, index) => {
            const delay = isSequence ? (index * duration / frequencies.length) : 0;
            const noteDuration = isSequence ? (duration / frequencies.length) : duration;
            
            setTimeout(() => {
                this.playTone({
                    frequency: freq,
                    type: config.type,
                    duration: noteDuration
                }, options);
            }, delay * 1000);
        });
    }

    /**
     * 播放背景音乐（程序生成）
     * @param {string} theme - 音乐主题
     */
    playBackgroundMusic(theme = 'ambient') {
        if (!this.enabled || !this.audioContext) return;
        
        this.stopBackgroundMusic();
        
        try {
            this.resumeAudioContext();
            
            switch (theme) {
                case 'ambient':
                    this.playAmbientMusic();
                    break;
                case 'tension':
                    this.playTensionMusic();
                    break;
                case 'victory':
                    this.playVictoryMusic();
                    break;
            }
            
        } catch (error) {
            console.warn('播放背景音乐失败:', error);
        }
    }

    /**
     * 播放环境音乐
     */
    playAmbientMusic() {
        const baseFreq = 220; // A3
        const harmonics = [1, 1.5, 2, 2.5, 3];
        
        harmonics.forEach((harmonic, index) => {
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();
            const filterNode = this.audioContext.createBiquadFilter();
            
            oscillator.type = 'sine';
            oscillator.frequency.setValueAtTime(baseFreq * harmonic, this.audioContext.currentTime);
            
            // 添加轻微的频率调制
            const lfo = this.audioContext.createOscillator();
            const lfoGain = this.audioContext.createGain();
            
            lfo.type = 'sine';
            lfo.frequency.setValueAtTime(0.1 + index * 0.05, this.audioContext.currentTime);
            lfoGain.gain.setValueAtTime(2, this.audioContext.currentTime);
            
            lfo.connect(lfoGain);
            lfoGain.connect(oscillator.frequency);
            
            // 设置滤波器
            filterNode.type = 'lowpass';
            filterNode.frequency.setValueAtTime(800 + index * 200, this.audioContext.currentTime);
            filterNode.Q.setValueAtTime(1, this.audioContext.currentTime);
            
            // 设置音量
            const volume = 0.1 / (index + 1); // 高次谐波音量递减
            gainNode.gain.setValueAtTime(volume, this.audioContext.currentTime);
            
            // 连接节点
            oscillator.connect(filterNode);
            filterNode.connect(gainNode);
            gainNode.connect(this.musicGain);
            
            // 开始播放
            oscillator.start();
            lfo.start();
            
            // 保存引用以便停止
            if (!this.currentMusic) {
                this.currentMusic = [];
            }
            this.currentMusic.push({ oscillator, lfo, gainNode, filterNode });
        });
    }

    /**
     * 播放紧张音乐
     */
    playTensionMusic() {
        const baseFreq = 110; // A2
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        const filterNode = this.audioContext.createBiquadFilter();
        
        oscillator.type = 'sawtooth';
        oscillator.frequency.setValueAtTime(baseFreq, this.audioContext.currentTime);
        
        // 添加颤音效果
        const tremolo = this.audioContext.createOscillator();
        const tremoloGain = this.audioContext.createGain();
        
        tremolo.type = 'sine';
        tremolo.frequency.setValueAtTime(6, this.audioContext.currentTime);
        tremoloGain.gain.setValueAtTime(0.3, this.audioContext.currentTime);
        
        tremolo.connect(tremoloGain);
        tremoloGain.connect(gainNode.gain);
        
        // 设置滤波器
        filterNode.type = 'highpass';
        filterNode.frequency.setValueAtTime(200, this.audioContext.currentTime);
        
        // 设置音量
        gainNode.gain.setValueAtTime(0.15, this.audioContext.currentTime);
        
        // 连接节点
        oscillator.connect(filterNode);
        filterNode.connect(gainNode);
        gainNode.connect(this.musicGain);
        
        // 开始播放
        oscillator.start();
        tremolo.start();
        
        this.currentMusic = [{ oscillator, tremolo, gainNode, filterNode }];
    }

    /**
     * 播放胜利音乐
     */
    playVictoryMusic() {
        const melody = [523, 659, 784, 1047]; // C5, E5, G5, C6
        const duration = 0.5;
        
        melody.forEach((freq, index) => {
            setTimeout(() => {
                this.playTone({
                    frequency: freq,
                    type: 'triangle',
                    duration: duration
                }, { volume: 0.4 });
            }, index * duration * 1000);
        });
    }

    /**
     * 停止背景音乐
     */
    stopBackgroundMusic() {
        if (this.currentMusic) {
            this.currentMusic.forEach(({ oscillator, lfo, gainNode, filterNode }) => {
                try {
                    if (oscillator) oscillator.stop();
                    if (lfo) lfo.stop();
                    if (gainNode) gainNode.disconnect();
                    if (filterNode) filterNode.disconnect();
                } catch (error) {
                    // 忽略已经停止的节点错误
                }
            });
            this.currentMusic = null;
        }
    }

    /**
     * 播放时间相关音效
     * @param {string} timeState - 时间状态
     */
    playTimeStateSound(timeState) {
        const frequencies = {
            past: 330,    // E4
            present: 440, // A4
            future: 550   // C#5
        };
        
        const freq = frequencies[timeState] || 440;
        
        this.playTone({
            frequency: freq,
            type: 'sine',
            duration: 0.3
        }, { volume: 0.2 });
    }

    /**
     * 播放连接音效
     * @param {string} fromState - 起始时间状态
     * @param {string} toState - 目标时间状态
     */
    playConnectionSound(fromState, toState) {
        const frequencies = {
            past: 330,
            present: 440,
            future: 550
        };
        
        const fromFreq = frequencies[fromState] || 440;
        const toFreq = frequencies[toState] || 440;
        
        // 播放滑音效果
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        
        oscillator.type = 'triangle';
        oscillator.frequency.setValueAtTime(fromFreq, this.audioContext.currentTime);
        oscillator.frequency.linearRampToValueAtTime(toFreq, this.audioContext.currentTime + 0.3);
        
        gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
        gainNode.gain.linearRampToValueAtTime(0.3, this.audioContext.currentTime + 0.05);
        gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + 0.3);
        
        oscillator.connect(gainNode);
        gainNode.connect(this.sfxGain);
        
        oscillator.start();
        oscillator.stop(this.audioContext.currentTime + 0.3);
        
        oscillator.onended = () => {
            oscillator.disconnect();
            gainNode.disconnect();
        };
    }

    /**
     * 设置主音量
     * @param {number} volume - 音量 (0-1)
     */
    setMasterVolume(volume) {
        this.masterVolume = MathUtils.clamp(volume, 0, 1);
        this.updateVolumes();
    }

    /**
     * 设置音效音量
     * @param {number} volume - 音量 (0-1)
     */
    setSfxVolume(volume) {
        this.sfxVolume = MathUtils.clamp(volume, 0, 1);
        this.updateVolumes();
    }

    /**
     * 设置音乐音量
     * @param {number} volume - 音量 (0-1)
     */
    setMusicVolume(volume) {
        this.musicVolume = MathUtils.clamp(volume, 0, 1);
        this.updateVolumes();
    }

    /**
     * 启用/禁用音频
     * @param {boolean} enabled - 是否启用
     */
    setEnabled(enabled) {
        this.enabled = enabled;
        
        if (!enabled) {
            this.stopBackgroundMusic();
        }
        
        console.log('音频系统', enabled ? '已启用' : '已禁用');
    }

    /**
     * 获取音频设置
     * @returns {Object} 音频设置
     */
    getSettings() {
        return {
            enabled: this.enabled,
            masterVolume: this.masterVolume,
            sfxVolume: this.sfxVolume,
            musicVolume: this.musicVolume
        };
    }

    /**
     * 应用音频设置
     * @param {Object} settings - 音频设置
     */
    applySettings(settings) {
        if (settings.enabled !== undefined) {
            this.setEnabled(settings.enabled);
        }
        if (settings.masterVolume !== undefined) {
            this.setMasterVolume(settings.masterVolume);
        }
        if (settings.sfxVolume !== undefined) {
            this.setSfxVolume(settings.sfxVolume);
        }
        if (settings.musicVolume !== undefined) {
            this.setMusicVolume(settings.musicVolume);
        }
    }

    /**
     * 销毁音频系统
     */
    destroy() {
        this.stopBackgroundMusic();
        
        if (this.audioContext) {
            this.audioContext.close();
        }
        
        this.audioBuffers.clear();
        this.playingSources.clear();
        
        console.log('音频系统已销毁');
    }
}

// 创建全局音频管理器实例
const audioManager = new AudioManager();

// 在用户首次交互时恢复音频上下文
document.addEventListener('click', () => {
    audioManager.resumeAudioContext();
}, { once: true });

document.addEventListener('touchstart', () => {
    audioManager.resumeAudioContext();
}, { once: true });
