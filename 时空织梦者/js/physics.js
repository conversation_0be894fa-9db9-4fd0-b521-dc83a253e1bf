/**
 * 时空织梦者 - 物理引擎系统
 * 负责游戏中物体的物理模拟，包括运动、碰撞检测和时间影响
 */

class Vector2 {
    constructor(x = 0, y = 0) {
        this.x = x;
        this.y = y;
    }

    /**
     * 向量加法
     * @param {Vector2} other - 另一个向量
     * @returns {Vector2} 结果向量
     */
    add(other) {
        return new Vector2(this.x + other.x, this.y + other.y);
    }

    /**
     * 向量减法
     * @param {Vector2} other - 另一个向量
     * @returns {Vector2} 结果向量
     */
    subtract(other) {
        return new Vector2(this.x - other.x, this.y - other.y);
    }

    /**
     * 向量乘法（标量）
     * @param {number} scalar - 标量
     * @returns {Vector2} 结果向量
     */
    multiply(scalar) {
        return new Vector2(this.x * scalar, this.y * scalar);
    }

    /**
     * 向量长度
     * @returns {number} 向量长度
     */
    magnitude() {
        return Math.sqrt(this.x * this.x + this.y * this.y);
    }

    /**
     * 向量归一化
     * @returns {Vector2} 归一化向量
     */
    normalize() {
        const mag = this.magnitude();
        if (mag === 0) return new Vector2(0, 0);
        return new Vector2(this.x / mag, this.y / mag);
    }

    /**
     * 向量点积
     * @param {Vector2} other - 另一个向量
     * @returns {number} 点积结果
     */
    dot(other) {
        return this.x * other.x + this.y * other.y;
    }

    /**
     * 向量距离
     * @param {Vector2} other - 另一个向量
     * @returns {number} 距离
     */
    distance(other) {
        const dx = this.x - other.x;
        const dy = this.y - other.y;
        return Math.sqrt(dx * dx + dy * dy);
    }

    /**
     * 复制向量
     * @returns {Vector2} 复制的向量
     */
    clone() {
        return new Vector2(this.x, this.y);
    }
}

class PhysicsObject {
    constructor(x, y, options = {}) {
        // 位置和运动
        this.position = new Vector2(x, y);
        this.velocity = new Vector2(options.vx || 0, options.vy || 0);
        this.acceleration = new Vector2(0, 0);
        this.previousPosition = this.position.clone();
        
        // 物理属性
        this.mass = options.mass || 1;
        this.radius = options.radius || 10;
        this.friction = options.friction || 0.98;
        this.bounce = options.bounce || 0.8;
        this.gravity = options.gravity || 0;
        
        // 时间相关属性
        this.timeState = options.timeState || 'present';
        this.timeInfluence = options.timeInfluence || 1.0;
        this.timePath = []; // 记录时间路径
        this.maxPathLength = 100;
        
        // 状态
        this.active = true;
        this.static = options.static || false;
        this.id = Date.now() + Math.random();
        
        // 视觉属性
        this.color = options.color || '#06b6d4';
        this.alpha = 1.0;
        this.trail = options.trail || false;
        this.trailPoints = [];
        
        // 碰撞
        this.collisionMask = options.collisionMask || 1;
        this.collisionLayer = options.collisionLayer || 1;
        
        this.init();
    }

    /**
     * 初始化物理对象
     */
    init() {
        // 记录初始位置到时间路径
        this.recordTimeState();
    }

    /**
     * 记录时间状态
     */
    recordTimeState() {
        this.timePath.push({
            position: this.position.clone(),
            velocity: this.velocity.clone(),
            timeState: this.timeState,
            timestamp: Date.now()
        });
        
        // 限制路径长度
        if (this.timePath.length > this.maxPathLength) {
            this.timePath.shift();
        }
    }

    /**
     * 应用力
     * @param {Vector2} force - 力向量
     */
    applyForce(force) {
        if (this.static) return;
        
        // F = ma, 所以 a = F/m
        const acceleration = force.multiply(1 / this.mass);
        this.acceleration = this.acceleration.add(acceleration);
    }

    /**
     * 应用冲量
     * @param {Vector2} impulse - 冲量向量
     */
    applyImpulse(impulse) {
        if (this.static) return;
        
        // 直接改变速度
        this.velocity = this.velocity.add(impulse.multiply(1 / this.mass));
    }

    /**
     * 更新物理状态
     * @param {number} deltaTime - 时间增量
     * @param {Object} timeInfo - 时间信息
     */
    update(deltaTime, timeInfo = {}) {
        if (!this.active || this.static) return;
        
        // 保存上一帧位置
        this.previousPosition = this.position.clone();
        
        // 应用时间影响
        const timeMultiplier = this.getTimeMultiplier(timeInfo);
        const adjustedDeltaTime = deltaTime * timeMultiplier;
        
        // 应用重力
        if (this.gravity !== 0) {
            this.applyForce(new Vector2(0, this.gravity * this.mass));
        }
        
        // 更新速度（积分加速度）
        this.velocity = this.velocity.add(this.acceleration.multiply(adjustedDeltaTime));
        
        // 应用摩擦力
        this.velocity = this.velocity.multiply(this.friction);
        
        // 更新位置（积分速度）
        this.position = this.position.add(this.velocity.multiply(adjustedDeltaTime));
        
        // 重置加速度
        this.acceleration = new Vector2(0, 0);
        
        // 更新轨迹
        if (this.trail) {
            this.updateTrail();
        }
        
        // 记录时间状态
        if (Math.random() < 0.1) { // 10%的概率记录，避免过于频繁
            this.recordTimeState();
        }
    }

    /**
     * 获取时间倍数
     * @param {Object} timeInfo - 时间信息
     * @returns {number} 时间倍数
     */
    getTimeMultiplier(timeInfo) {
        if (!timeInfo.currentState) return 1.0;
        
        // 根据当前时间状态和对象的时间状态计算影响
        const stateMultipliers = {
            past: { past: 1.0, present: 0.5, future: 0.1 },
            present: { past: 0.8, present: 1.0, future: 0.8 },
            future: { past: 0.1, present: 0.5, future: 1.0 }
        };
        
        const multiplier = stateMultipliers[this.timeState]?.[timeInfo.currentState] || 1.0;
        return multiplier * this.timeInfluence;
    }

    /**
     * 更新轨迹
     */
    updateTrail() {
        this.trailPoints.push({
            x: this.position.x,
            y: this.position.y,
            timestamp: Date.now()
        });
        
        // 限制轨迹长度
        const maxTrailLength = 20;
        if (this.trailPoints.length > maxTrailLength) {
            this.trailPoints.shift();
        }
        
        // 移除过老的轨迹点
        const now = Date.now();
        this.trailPoints = this.trailPoints.filter(point => 
            now - point.timestamp < 1000 // 1秒内的轨迹
        );
    }

    /**
     * 检查与另一个物体的碰撞
     * @param {PhysicsObject} other - 另一个物体
     * @returns {boolean} 是否碰撞
     */
    checkCollision(other) {
        if (!this.active || !other.active) return false;
        if ((this.collisionMask & other.collisionLayer) === 0) return false;
        
        const distance = this.position.distance(other.position);
        return distance < (this.radius + other.radius);
    }

    /**
     * 解决碰撞
     * @param {PhysicsObject} other - 另一个物体
     */
    resolveCollision(other) {
        if (this.static && other.static) return;
        
        // 计算碰撞向量
        const collisionVector = other.position.subtract(this.position);
        const distance = collisionVector.magnitude();
        
        if (distance === 0) return; // 避免除零
        
        // 归一化碰撞向量
        const normal = collisionVector.normalize();
        
        // 分离物体
        const overlap = (this.radius + other.radius) - distance;
        const separation = normal.multiply(overlap * 0.5);
        
        if (!this.static) {
            this.position = this.position.subtract(separation);
        }
        if (!other.static) {
            other.position = other.position.add(separation);
        }
        
        // 计算相对速度
        const relativeVelocity = other.velocity.subtract(this.velocity);
        const velocityAlongNormal = relativeVelocity.dot(normal);
        
        // 如果物体正在分离，不需要解决碰撞
        if (velocityAlongNormal > 0) return;
        
        // 计算反弹
        const restitution = Math.min(this.bounce, other.bounce);
        const impulseScalar = -(1 + restitution) * velocityAlongNormal;
        const totalMass = this.mass + other.mass;
        
        if (!this.static) {
            const impulse = normal.multiply(impulseScalar * other.mass / totalMass);
            this.velocity = this.velocity.subtract(impulse);
        }
        
        if (!other.static) {
            const impulse = normal.multiply(impulseScalar * this.mass / totalMass);
            other.velocity = other.velocity.add(impulse);
        }
    }

    /**
     * 检查边界碰撞
     * @param {number} width - 边界宽度
     * @param {number} height - 边界高度
     */
    checkBounds(width, height) {
        let bounced = false;
        
        // 左右边界
        if (this.position.x - this.radius < 0) {
            this.position.x = this.radius;
            this.velocity.x *= -this.bounce;
            bounced = true;
        } else if (this.position.x + this.radius > width) {
            this.position.x = width - this.radius;
            this.velocity.x *= -this.bounce;
            bounced = true;
        }
        
        // 上下边界
        if (this.position.y - this.radius < 0) {
            this.position.y = this.radius;
            this.velocity.y *= -this.bounce;
            bounced = true;
        } else if (this.position.y + this.radius > height) {
            this.position.y = height - this.radius;
            this.velocity.y *= -this.bounce;
            bounced = true;
        }
        
        return bounced;
    }

    /**
     * 渲染物理对象
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    render(ctx) {
        if (!this.active) return;
        
        ctx.save();
        
        // 渲染轨迹
        if (this.trail && this.trailPoints.length > 1) {
            this.renderTrail(ctx);
        }
        
        // 设置透明度
        ctx.globalAlpha = this.alpha;
        
        // 渲染主体
        ctx.fillStyle = this.color;
        ctx.strokeStyle = this.color;
        ctx.lineWidth = 2;
        
        ctx.beginPath();
        ctx.arc(this.position.x, this.position.y, this.radius, 0, Math.PI * 2);
        ctx.fill();
        ctx.stroke();
        
        // 渲染方向指示器
        if (this.velocity.magnitude() > 1) {
            const direction = this.velocity.normalize();
            const arrowLength = this.radius * 1.5;
            
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(this.position.x, this.position.y);
            ctx.lineTo(
                this.position.x + direction.x * arrowLength,
                this.position.y + direction.y * arrowLength
            );
            ctx.stroke();
        }
        
        ctx.restore();
    }

    /**
     * 渲染轨迹
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    renderTrail(ctx) {
        if (this.trailPoints.length < 2) return;
        
        ctx.strokeStyle = this.color;
        ctx.lineWidth = 3;
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';
        
        for (let i = 0; i < this.trailPoints.length - 1; i++) {
            const point = this.trailPoints[i];
            const nextPoint = this.trailPoints[i + 1];
            const alpha = (i / this.trailPoints.length) * this.alpha;
            
            ctx.globalAlpha = alpha;
            ctx.beginPath();
            ctx.moveTo(point.x, point.y);
            ctx.lineTo(nextPoint.x, nextPoint.y);
            ctx.stroke();
        }
    }

    /**
     * 设置时间状态
     * @param {string} timeState - 时间状态
     */
    setTimeState(timeState) {
        this.timeState = timeState;
        
        // 根据时间状态调整颜色
        const colors = {
            past: '#f59e0b',
            present: '#06b6d4',
            future: '#6366f1'
        };
        
        this.color = colors[timeState] || this.color;
    }

    /**
     * 时间回溯
     * @param {number} steps - 回溯步数
     */
    rewindTime(steps = 1) {
        if (this.timePath.length <= steps) return false;
        
        const targetIndex = this.timePath.length - 1 - steps;
        const targetState = this.timePath[targetIndex];
        
        if (targetState) {
            this.position = targetState.position.clone();
            this.velocity = targetState.velocity.clone();
            this.timeState = targetState.timeState;
            return true;
        }
        
        return false;
    }

    /**
     * 获取物体信息
     * @returns {Object} 物体信息
     */
    getInfo() {
        return {
            id: this.id,
            position: { x: this.position.x, y: this.position.y },
            velocity: { x: this.velocity.x, y: this.velocity.y },
            mass: this.mass,
            radius: this.radius,
            timeState: this.timeState,
            active: this.active,
            static: this.static
        };
    }

    /**
     * 销毁物体
     */
    destroy() {
        this.active = false;
        this.timePath = [];
        this.trailPoints = [];
    }
}

/**
 * 物理世界管理器
 */
class PhysicsWorld {
    constructor(width, height, options = {}) {
        this.width = width;
        this.height = height;
        this.objects = [];

        // 物理设置
        this.gravity = new Vector2(0, options.gravity || 0);
        this.airResistance = options.airResistance || 0.999;
        this.timeScale = 1.0;

        // 碰撞检测优化
        this.spatialGrid = null;
        this.gridSize = options.gridSize || 50;
        this.enableSpatialOptimization = options.enableSpatialOptimization || true;

        // 时间相关
        this.timeInfo = {
            currentState: 'present',
            currentTime: 0.5,
            timeEnergy: 100
        };

        // 统计信息
        this.stats = {
            totalObjects: 0,
            activeObjects: 0,
            collisionChecks: 0,
            collisions: 0
        };

        this.init();
    }

    /**
     * 初始化物理世界
     */
    init() {
        if (this.enableSpatialOptimization) {
            this.initSpatialGrid();
        }

        console.log('物理世界初始化完成');
    }

    /**
     * 初始化空间网格
     */
    initSpatialGrid() {
        const cols = Math.ceil(this.width / this.gridSize);
        const rows = Math.ceil(this.height / this.gridSize);

        this.spatialGrid = {
            cols,
            rows,
            cells: new Array(cols * rows).fill(null).map(() => [])
        };
    }

    /**
     * 添加物理对象
     * @param {PhysicsObject} object - 物理对象
     */
    addObject(object) {
        this.objects.push(object);
        this.stats.totalObjects++;
    }

    /**
     * 移除物理对象
     * @param {string} objectId - 对象ID
     * @returns {boolean} 是否移除成功
     */
    removeObject(objectId) {
        const index = this.objects.findIndex(obj => obj.id === objectId);
        if (index !== -1) {
            this.objects.splice(index, 1);
            this.stats.totalObjects--;
            return true;
        }
        return false;
    }

    /**
     * 更新时间信息
     * @param {Object} timeInfo - 时间信息
     */
    updateTimeInfo(timeInfo) {
        this.timeInfo = { ...this.timeInfo, ...timeInfo };
    }

    /**
     * 更新物理世界
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        // 重置统计信息
        this.stats.activeObjects = 0;
        this.stats.collisionChecks = 0;
        this.stats.collisions = 0;

        // 应用时间缩放
        const scaledDeltaTime = deltaTime * this.timeScale;

        // 清理空间网格
        if (this.enableSpatialOptimization) {
            this.clearSpatialGrid();
        }

        // 更新所有对象
        for (let i = this.objects.length - 1; i >= 0; i--) {
            const object = this.objects[i];

            if (!object.active) {
                this.objects.splice(i, 1);
                this.stats.totalObjects--;
                continue;
            }

            // 应用全局重力
            if (!object.static && this.gravity.magnitude() > 0) {
                object.applyForce(this.gravity.multiply(object.mass));
            }

            // 更新对象
            object.update(scaledDeltaTime, this.timeInfo);

            // 边界检查
            object.checkBounds(this.width, this.height);

            // 添加到空间网格
            if (this.enableSpatialOptimization) {
                this.addToSpatialGrid(object);
            }

            this.stats.activeObjects++;
        }

        // 碰撞检测
        this.handleCollisions();
    }

    /**
     * 清理空间网格
     */
    clearSpatialGrid() {
        if (!this.spatialGrid) return;

        this.spatialGrid.cells.forEach(cell => {
            cell.length = 0;
        });
    }

    /**
     * 添加对象到空间网格
     * @param {PhysicsObject} object - 物理对象
     */
    addToSpatialGrid(object) {
        if (!this.spatialGrid) return;

        const minX = Math.floor((object.position.x - object.radius) / this.gridSize);
        const maxX = Math.floor((object.position.x + object.radius) / this.gridSize);
        const minY = Math.floor((object.position.y - object.radius) / this.gridSize);
        const maxY = Math.floor((object.position.y + object.radius) / this.gridSize);

        for (let x = Math.max(0, minX); x <= Math.min(this.spatialGrid.cols - 1, maxX); x++) {
            for (let y = Math.max(0, minY); y <= Math.min(this.spatialGrid.rows - 1, maxY); y++) {
                const cellIndex = y * this.spatialGrid.cols + x;
                this.spatialGrid.cells[cellIndex].push(object);
            }
        }
    }

    /**
     * 处理碰撞
     */
    handleCollisions() {
        if (this.enableSpatialOptimization && this.spatialGrid) {
            this.handleSpatialCollisions();
        } else {
            this.handleBruteForceCollisions();
        }
    }

    /**
     * 使用空间网格处理碰撞
     */
    handleSpatialCollisions() {
        const checkedPairs = new Set();

        this.spatialGrid.cells.forEach(cell => {
            for (let i = 0; i < cell.length; i++) {
                for (let j = i + 1; j < cell.length; j++) {
                    const obj1 = cell[i];
                    const obj2 = cell[j];

                    const pairKey = `${Math.min(obj1.id, obj2.id)}-${Math.max(obj1.id, obj2.id)}`;
                    if (checkedPairs.has(pairKey)) continue;

                    checkedPairs.add(pairKey);
                    this.stats.collisionChecks++;

                    if (obj1.checkCollision(obj2)) {
                        obj1.resolveCollision(obj2);
                        this.stats.collisions++;
                    }
                }
            }
        });
    }

    /**
     * 暴力法处理碰撞
     */
    handleBruteForceCollisions() {
        for (let i = 0; i < this.objects.length; i++) {
            for (let j = i + 1; j < this.objects.length; j++) {
                const obj1 = this.objects[i];
                const obj2 = this.objects[j];

                if (!obj1.active || !obj2.active) continue;

                this.stats.collisionChecks++;

                if (obj1.checkCollision(obj2)) {
                    obj1.resolveCollision(obj2);
                    this.stats.collisions++;
                }
            }
        }
    }

    /**
     * 在指定位置创建物理对象
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {Object} options - 对象选项
     * @returns {PhysicsObject} 创建的对象
     */
    createObject(x, y, options = {}) {
        const object = new PhysicsObject(x, y, options);
        this.addObject(object);
        return object;
    }

    /**
     * 创建能量球
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {string} timeState - 时间状态
     * @returns {PhysicsObject} 能量球对象
     */
    createEnergyOrb(x, y, timeState = 'present') {
        const colors = {
            past: '#f59e0b',
            present: '#06b6d4',
            future: '#6366f1'
        };

        return this.createObject(x, y, {
            radius: MathUtils.random(8, 15),
            mass: 0.5,
            bounce: 0.9,
            friction: 0.995,
            color: colors[timeState],
            timeState,
            trail: true,
            vx: MathUtils.random(-50, 50),
            vy: MathUtils.random(-50, 50)
        });
    }

    /**
     * 应用时间扭曲效果
     * @param {number} x - 中心X坐标
     * @param {number} y - 中心Y坐标
     * @param {number} radius - 影响半径
     * @param {number} strength - 扭曲强度
     */
    applyTimeWarp(x, y, radius, strength) {
        const center = new Vector2(x, y);

        this.objects.forEach(object => {
            if (!object.active || object.static) return;

            const distance = object.position.distance(center);
            if (distance > radius) return;

            // 计算影响强度（距离越近影响越大）
            const influence = (1 - distance / radius) * strength;

            // 应用向心力或离心力
            const direction = center.subtract(object.position).normalize();
            const force = direction.multiply(influence * object.mass * 100);

            object.applyForce(force);
        });
    }

    /**
     * 获取指定区域内的对象
     * @param {number} x - 区域中心X坐标
     * @param {number} y - 区域中心Y坐标
     * @param {number} radius - 区域半径
     * @returns {PhysicsObject[]} 区域内的对象
     */
    getObjectsInRadius(x, y, radius) {
        const center = new Vector2(x, y);
        return this.objects.filter(object => {
            if (!object.active) return false;
            return object.position.distance(center) <= radius;
        });
    }

    /**
     * 清除所有对象
     */
    clear() {
        this.objects.forEach(object => object.destroy());
        this.objects = [];
        this.stats.totalObjects = 0;
        this.stats.activeObjects = 0;
    }

    /**
     * 设置重力
     * @param {number} x - X方向重力
     * @param {number} y - Y方向重力
     */
    setGravity(x, y) {
        this.gravity = new Vector2(x, y);
    }

    /**
     * 设置时间缩放
     * @param {number} scale - 时间缩放比例
     */
    setTimeScale(scale) {
        this.timeScale = MathUtils.clamp(scale, 0.1, 3.0);
    }

    /**
     * 渲染所有物理对象
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    render(ctx) {
        this.objects.forEach(object => {
            if (object.active) {
                object.render(ctx);
            }
        });

        // 渲染调试信息
        if (window.DEBUG_PHYSICS) {
            this.renderDebugInfo(ctx);
        }
    }

    /**
     * 渲染调试信息
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    renderDebugInfo(ctx) {
        ctx.save();
        ctx.fillStyle = '#ffffff';
        ctx.font = '12px Arial';
        ctx.textAlign = 'left';

        const debugInfo = [
            `对象总数: ${this.stats.totalObjects}`,
            `活跃对象: ${this.stats.activeObjects}`,
            `碰撞检测: ${this.stats.collisionChecks}`,
            `碰撞次数: ${this.stats.collisions}`,
            `时间缩放: ${this.timeScale.toFixed(2)}`
        ];

        debugInfo.forEach((info, index) => {
            ctx.fillText(info, 10, 20 + index * 15);
        });

        // 渲染空间网格
        if (this.enableSpatialOptimization && this.spatialGrid) {
            this.renderSpatialGrid(ctx);
        }

        ctx.restore();
    }

    /**
     * 渲染空间网格
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    renderSpatialGrid(ctx) {
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
        ctx.lineWidth = 1;

        // 垂直线
        for (let x = 0; x <= this.width; x += this.gridSize) {
            ctx.beginPath();
            ctx.moveTo(x, 0);
            ctx.lineTo(x, this.height);
            ctx.stroke();
        }

        // 水平线
        for (let y = 0; y <= this.height; y += this.gridSize) {
            ctx.beginPath();
            ctx.moveTo(0, y);
            ctx.lineTo(this.width, y);
            ctx.stroke();
        }
    }

    /**
     * 获取世界统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return { ...this.stats };
    }
}

// 创建全局物理世界实例（将在游戏初始化时创建）
let physicsWorld = null;
