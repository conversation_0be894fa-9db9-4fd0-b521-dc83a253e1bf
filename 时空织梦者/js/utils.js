/**
 * 时空织梦者 - 工具函数库
 * 提供游戏中常用的数学计算、DOM操作和辅助功能
 */

// 数学工具函数
const MathUtils = {
    /**
     * 线性插值函数
     * @param {number} start - 起始值
     * @param {number} end - 结束值
     * @param {number} t - 插值参数 (0-1)
     * @returns {number} 插值结果
     */
    lerp(start, end, t) {
        return start + (end - start) * t;
    },

    /**
     * 限制数值在指定范围内
     * @param {number} value - 要限制的值
     * @param {number} min - 最小值
     * @param {number} max - 最大值
     * @returns {number} 限制后的值
     */
    clamp(value, min, max) {
        return Math.min(Math.max(value, min), max);
    },

    /**
     * 计算两点之间的距离
     * @param {number} x1 - 第一个点的x坐标
     * @param {number} y1 - 第一个点的y坐标
     * @param {number} x2 - 第二个点的x坐标
     * @param {number} y2 - 第二个点的y坐标
     * @returns {number} 距离
     */
    distance(x1, y1, x2, y2) {
        const dx = x2 - x1;
        const dy = y2 - y1;
        return Math.sqrt(dx * dx + dy * dy);
    },

    /**
     * 计算两点之间的角度
     * @param {number} x1 - 第一个点的x坐标
     * @param {number} y1 - 第一个点的y坐标
     * @param {number} x2 - 第二个点的x坐标
     * @param {number} y2 - 第二个点的y坐标
     * @returns {number} 角度（弧度）
     */
    angle(x1, y1, x2, y2) {
        return Math.atan2(y2 - y1, x2 - x1);
    },

    /**
     * 生成指定范围内的随机数
     * @param {number} min - 最小值
     * @param {number} max - 最大值
     * @returns {number} 随机数
     */
    random(min, max) {
        return Math.random() * (max - min) + min;
    },

    /**
     * 生成指定范围内的随机整数
     * @param {number} min - 最小值
     * @param {number} max - 最大值
     * @returns {number} 随机整数
     */
    randomInt(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    },

    /**
     * 将角度转换为弧度
     * @param {number} degrees - 角度
     * @returns {number} 弧度
     */
    toRadians(degrees) {
        return degrees * Math.PI / 180;
    },

    /**
     * 将弧度转换为角度
     * @param {number} radians - 弧度
     * @returns {number} 角度
     */
    toDegrees(radians) {
        return radians * 180 / Math.PI;
    },

    /**
     * 平滑步进函数
     * @param {number} t - 输入值 (0-1)
     * @returns {number} 平滑后的值
     */
    smoothStep(t) {
        return t * t * (3 - 2 * t);
    },

    /**
     * 更平滑的步进函数
     * @param {number} t - 输入值 (0-1)
     * @returns {number} 平滑后的值
     */
    smootherStep(t) {
        return t * t * t * (t * (t * 6 - 15) + 10);
    }
};

// DOM工具函数
const DOMUtils = {
    /**
     * 获取元素
     * @param {string} selector - CSS选择器
     * @returns {Element|null} DOM元素
     */
    $(selector) {
        return document.querySelector(selector);
    },

    /**
     * 获取多个元素
     * @param {string} selector - CSS选择器
     * @returns {NodeList} DOM元素列表
     */
    $$(selector) {
        return document.querySelectorAll(selector);
    },

    /**
     * 创建元素
     * @param {string} tag - 标签名
     * @param {Object} attributes - 属性对象
     * @param {string} content - 内容
     * @returns {Element} 创建的元素
     */
    createElement(tag, attributes = {}, content = '') {
        const element = document.createElement(tag);
        
        Object.keys(attributes).forEach(key => {
            if (key === 'className') {
                element.className = attributes[key];
            } else if (key === 'style') {
                Object.assign(element.style, attributes[key]);
            } else {
                element.setAttribute(key, attributes[key]);
            }
        });
        
        if (content) {
            element.textContent = content;
        }
        
        return element;
    },

    /**
     * 添加事件监听器
     * @param {Element} element - DOM元素
     * @param {string} event - 事件类型
     * @param {Function} handler - 事件处理函数
     * @param {Object} options - 选项
     */
    addEvent(element, event, handler, options = {}) {
        element.addEventListener(event, handler, options);
    },

    /**
     * 移除事件监听器
     * @param {Element} element - DOM元素
     * @param {string} event - 事件类型
     * @param {Function} handler - 事件处理函数
     */
    removeEvent(element, event, handler) {
        element.removeEventListener(event, handler);
    },

    /**
     * 获取元素的边界矩形
     * @param {Element} element - DOM元素
     * @returns {DOMRect} 边界矩形
     */
    getBounds(element) {
        return element.getBoundingClientRect();
    },

    /**
     * 检查元素是否在视口内
     * @param {Element} element - DOM元素
     * @returns {boolean} 是否在视口内
     */
    isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= window.innerHeight &&
            rect.right <= window.innerWidth
        );
    },

    /**
     * 平滑滚动到指定元素
     * @param {Element} element - 目标元素
     * @param {Object} options - 滚动选项
     */
    scrollToElement(element, options = {}) {
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'center',
            ...options
        });
    }
};

// 颜色工具函数
const ColorUtils = {
    /**
     * 将HSL颜色转换为RGB
     * @param {number} h - 色相 (0-360)
     * @param {number} s - 饱和度 (0-100)
     * @param {number} l - 亮度 (0-100)
     * @returns {Object} RGB颜色对象
     */
    hslToRgb(h, s, l) {
        h /= 360;
        s /= 100;
        l /= 100;

        const c = (1 - Math.abs(2 * l - 1)) * s;
        const x = c * (1 - Math.abs((h * 6) % 2 - 1));
        const m = l - c / 2;

        let r, g, b;

        if (0 <= h && h < 1/6) {
            r = c; g = x; b = 0;
        } else if (1/6 <= h && h < 2/6) {
            r = x; g = c; b = 0;
        } else if (2/6 <= h && h < 3/6) {
            r = 0; g = c; b = x;
        } else if (3/6 <= h && h < 4/6) {
            r = 0; g = x; b = c;
        } else if (4/6 <= h && h < 5/6) {
            r = x; g = 0; b = c;
        } else {
            r = c; g = 0; b = x;
        }

        return {
            r: Math.round((r + m) * 255),
            g: Math.round((g + m) * 255),
            b: Math.round((b + m) * 255)
        };
    },

    /**
     * 创建渐变色字符串
     * @param {Array} colors - 颜色数组
     * @param {string} direction - 渐变方向
     * @returns {string} CSS渐变字符串
     */
    createGradient(colors, direction = 'to right') {
        return `linear-gradient(${direction}, ${colors.join(', ')})`;
    },

    /**
     * 调整颜色透明度
     * @param {string} color - 颜色值
     * @param {number} alpha - 透明度 (0-1)
     * @returns {string} 带透明度的颜色
     */
    withAlpha(color, alpha) {
        if (color.startsWith('#')) {
            const r = parseInt(color.slice(1, 3), 16);
            const g = parseInt(color.slice(3, 5), 16);
            const b = parseInt(color.slice(5, 7), 16);
            return `rgba(${r}, ${g}, ${b}, ${alpha})`;
        }
        return color;
    }
};

// 时间工具函数
const TimeUtils = {
    /**
     * 格式化时间显示
     * @param {number} seconds - 秒数
     * @returns {string} 格式化的时间字符串
     */
    formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    },

    /**
     * 获取当前时间戳
     * @returns {number} 时间戳
     */
    now() {
        return Date.now();
    },

    /**
     * 创建延迟执行的Promise
     * @param {number} ms - 延迟毫秒数
     * @returns {Promise} Promise对象
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    },

    /**
     * 创建防抖函数
     * @param {Function} func - 要防抖的函数
     * @param {number} wait - 等待时间
     * @returns {Function} 防抖后的函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    /**
     * 创建节流函数
     * @param {Function} func - 要节流的函数
     * @param {number} limit - 限制时间
     * @returns {Function} 节流后的函数
     */
    throttle(func, limit) {
        let inThrottle;
        return function executedFunction(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
};

// 设备检测工具
const DeviceUtils = {
    /**
     * 检查是否为移动设备
     * @returns {boolean} 是否为移动设备
     */
    isMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    },

    /**
     * 检查是否为触摸设备
     * @returns {boolean} 是否为触摸设备
     */
    isTouchDevice() {
        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    },

    /**
     * 获取设备像素比
     * @returns {number} 设备像素比
     */
    getPixelRatio() {
        return window.devicePixelRatio || 1;
    },

    /**
     * 获取视口尺寸
     * @returns {Object} 视口尺寸对象
     */
    getViewportSize() {
        return {
            width: window.innerWidth,
            height: window.innerHeight
        };
    }
};

// 导出工具函数（如果使用模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        MathUtils,
        DOMUtils,
        ColorUtils,
        TimeUtils,
        DeviceUtils
    };
}
