/**
 * 时空织梦者 - Service Worker
 * 提供离线缓存和PWA功能支持
 */

const CACHE_NAME = 'time-weaver-v1.0.0';
const CACHE_URLS = [
    './',
    './index.html',
    './manifest.json',
    './css/main.css',
    './css/animations.css',
    './css/responsive.css',
    './js/utils.js',
    './js/storage.js',
    './js/particles.js',
    './js/timeline.js',
    './js/anchors.js',
    './js/physics.js',
    './js/levels.js',
    './js/audio.js',
    './js/game.js'
];

// 安装事件 - 缓存资源
self.addEventListener('install', event => {
    console.log('Service Worker 安装中...');
    
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => {
                console.log('缓存已打开');
                return cache.addAll(CACHE_URLS);
            })
            .then(() => {
                console.log('所有资源已缓存');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('缓存资源失败:', error);
            })
    );
});

// 激活事件 - 清理旧缓存
self.addEventListener('activate', event => {
    console.log('Service Worker 激活中...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== CACHE_NAME) {
                            console.log('删除旧缓存:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker 已激活');
                return self.clients.claim();
            })
    );
});

// 拦截请求 - 缓存优先策略
self.addEventListener('fetch', event => {
    // 只处理同源请求
    if (!event.request.url.startsWith(self.location.origin)) {
        return;
    }
    
    event.respondWith(
        caches.match(event.request)
            .then(response => {
                // 如果缓存中有，直接返回
                if (response) {
                    return response;
                }
                
                // 否则从网络获取
                return fetch(event.request)
                    .then(response => {
                        // 检查响应是否有效
                        if (!response || response.status !== 200 || response.type !== 'basic') {
                            return response;
                        }
                        
                        // 克隆响应用于缓存
                        const responseToCache = response.clone();
                        
                        caches.open(CACHE_NAME)
                            .then(cache => {
                                cache.put(event.request, responseToCache);
                            });
                        
                        return response;
                    })
                    .catch(error => {
                        console.error('网络请求失败:', error);
                        
                        // 如果是HTML请求且网络失败，返回离线页面
                        if (event.request.destination === 'document') {
                            return caches.match('./index.html');
                        }
                        
                        throw error;
                    });
            })
    );
});

// 消息处理
self.addEventListener('message', event => {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
});

// 后台同步（如果支持）
if ('sync' in self.registration) {
    self.addEventListener('sync', event => {
        if (event.tag === 'background-sync') {
            console.log('执行后台同步');
            // 这里可以添加后台同步逻辑
        }
    });
}

// 推送通知（如果需要）
self.addEventListener('push', event => {
    if (event.data) {
        const data = event.data.json();
        
        const options = {
            body: data.body || '时空织梦者有新消息',
            icon: './assets/images/icon-192.png',
            badge: './assets/images/icon-72.png',
            vibrate: [100, 50, 100],
            data: data.data || {},
            actions: [
                {
                    action: 'open',
                    title: '打开游戏',
                    icon: './assets/images/icon-72.png'
                },
                {
                    action: 'close',
                    title: '关闭',
                    icon: './assets/images/icon-72.png'
                }
            ]
        };
        
        event.waitUntil(
            self.registration.showNotification(data.title || '时空织梦者', options)
        );
    }
});

// 通知点击处理
self.addEventListener('notificationclick', event => {
    event.notification.close();
    
    if (event.action === 'open') {
        event.waitUntil(
            clients.openWindow('./')
        );
    }
});

console.log('Service Worker 已加载');
