<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>元素守护者 - 功能测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 20px;
            background: #2d3436;
            color: white;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #636e72;
            border-radius: 8px;
        }
        .test-result {
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .success {
            background: #00b894;
            color: white;
        }
        .error {
            background: #e17055;
            color: white;
        }
        .warning {
            background: #fdcb6e;
            color: #2d3436;
        }
        button {
            background: #0984e3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #74b9ff;
        }
        #test-canvas {
            border: 1px solid #636e72;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🎮 元素守护者 - 功能测试</h1>
    
    <div class="test-section">
        <h2>📋 基础功能测试</h2>
        <button onclick="testBasicFunctions()">运行基础测试</button>
        <div id="basic-results"></div>
    </div>
    
    <div class="test-section">
        <h2>🎨 渲染系统测试</h2>
        <button onclick="testRenderer()">测试渲染器</button>
        <canvas id="test-canvas" width="400" height="300"></canvas>
        <div id="render-results"></div>
    </div>
    
    <div class="test-section">
        <h2>🏗️ 游戏系统测试</h2>
        <button onclick="testGameSystems()">测试游戏系统</button>
        <div id="system-results"></div>
    </div>
    
    <div class="test-section">
        <h2>💾 存储系统测试</h2>
        <button onclick="testStorage()">测试存储功能</button>
        <div id="storage-results"></div>
    </div>
    
    <div class="test-section">
        <h2>📱 移动端测试</h2>
        <button onclick="testMobile()">测试移动端功能</button>
        <div id="mobile-results"></div>
    </div>

    <script type="module">
        import { Vector2 } from './js/utils/Vector2.js';
        import { GameState } from './js/utils/GameState.js';
        import { Renderer } from './js/core/Renderer.js';
        import { SaveManager } from './js/systems/SaveManager.js';
        
        window.testModules = {
            Vector2,
            GameState,
            Renderer,
            SaveManager
        };
        
        // 基础功能测试
        window.testBasicFunctions = function() {
            const results = document.getElementById('basic-results');
            results.innerHTML = '';
            
            const tests = [
                {
                    name: 'Vector2 类测试',
                    test: () => {
                        const v1 = new Vector2(3, 4);
                        const v2 = new Vector2(1, 2);
                        const sum = Vector2.add(v1, v2);
                        return sum.x === 4 && sum.y === 6;
                    }
                },
                {
                    name: 'GameState 类测试',
                    test: () => {
                        const state = new GameState();
                        state.addGold(100);
                        return state.gold === 150; // 初始50 + 100
                    }
                },
                {
                    name: 'Canvas 支持测试',
                    test: () => {
                        const canvas = document.createElement('canvas');
                        return canvas.getContext('2d') !== null;
                    }
                },
                {
                    name: 'LocalStorage 支持测试',
                    test: () => {
                        try {
                            localStorage.setItem('test', 'value');
                            const result = localStorage.getItem('test') === 'value';
                            localStorage.removeItem('test');
                            return result;
                        } catch (e) {
                            return false;
                        }
                    }
                },
                {
                    name: 'ES6 模块支持测试',
                    test: () => {
                        return typeof window.testModules.Vector2 === 'function';
                    }
                }
            ];
            
            tests.forEach(test => {
                try {
                    const passed = test.test();
                    const div = document.createElement('div');
                    div.className = `test-result ${passed ? 'success' : 'error'}`;
                    div.textContent = `${passed ? '✅' : '❌'} ${test.name}`;
                    results.appendChild(div);
                } catch (error) {
                    const div = document.createElement('div');
                    div.className = 'test-result error';
                    div.textContent = `❌ ${test.name}: ${error.message}`;
                    results.appendChild(div);
                }
            });
        };
        
        // 渲染系统测试
        window.testRenderer = function() {
            const canvas = document.getElementById('test-canvas');
            const results = document.getElementById('render-results');
            results.innerHTML = '';
            
            try {
                const renderer = new window.testModules.Renderer(canvas);
                
                // 清空画布
                renderer.clear();
                
                // 绘制测试图形
                renderer.drawCircle(50, 50, 20, '#ff6b35');
                renderer.drawCircle(150, 50, 20, '#74b9ff');
                renderer.drawCircle(250, 50, 20, '#6c5ce7');
                renderer.drawCircle(350, 50, 20, '#00cec9');
                
                // 绘制文字
                renderer.drawText('元素守护者渲染测试', 200, 150, '16px Arial', 'white');
                
                // 绘制线条
                renderer.drawLine(50, 200, 350, 200, '#ffffff', 2);
                
                const div = document.createElement('div');
                div.className = 'test-result success';
                div.textContent = '✅ 渲染系统测试通过';
                results.appendChild(div);
                
            } catch (error) {
                const div = document.createElement('div');
                div.className = 'test-result error';
                div.textContent = `❌ 渲染系统测试失败: ${error.message}`;
                results.appendChild(div);
            }
        };
        
        // 游戏系统测试
        window.testGameSystems = function() {
            const results = document.getElementById('system-results');
            results.innerHTML = '';
            
            const tests = [
                {
                    name: '游戏状态管理',
                    test: () => {
                        const state = new window.testModules.GameState();
                        state.addGold(50);
                        state.spendGold(30);
                        return state.gold === 70; // 50 + 50 - 30
                    }
                },
                {
                    name: '向量数学运算',
                    test: () => {
                        const v1 = new window.testModules.Vector2(3, 4);
                        const length = v1.length();
                        return Math.abs(length - 5) < 0.001;
                    }
                },
                {
                    name: '游戏状态序列化',
                    test: () => {
                        const state = new window.testModules.GameState();
                        state.addGold(100);
                        const json = state.toJSON();
                        return json.gold === 150;
                    }
                }
            ];
            
            tests.forEach(test => {
                try {
                    const passed = test.test();
                    const div = document.createElement('div');
                    div.className = `test-result ${passed ? 'success' : 'error'}`;
                    div.textContent = `${passed ? '✅' : '❌'} ${test.name}`;
                    results.appendChild(div);
                } catch (error) {
                    const div = document.createElement('div');
                    div.className = 'test-result error';
                    div.textContent = `❌ ${test.name}: ${error.message}`;
                    results.appendChild(div);
                }
            });
        };
        
        // 存储系统测试
        window.testStorage = function() {
            const results = document.getElementById('storage-results');
            results.innerHTML = '';
            
            try {
                const saveManager = new window.testModules.SaveManager();
                const gameState = new window.testModules.GameState();
                
                // 测试保存
                const saved = saveManager.saveGame(gameState);
                
                // 测试加载
                const loaded = saveManager.loadGame();
                
                // 测试设置
                const settings = { volume: 0.8, difficulty: 'normal' };
                saveManager.saveSettings(settings);
                const loadedSettings = saveManager.loadSettings();
                
                const div = document.createElement('div');
                div.className = 'test-result success';
                div.textContent = '✅ 存储系统测试通过';
                results.appendChild(div);
                
            } catch (error) {
                const div = document.createElement('div');
                div.className = 'test-result error';
                div.textContent = `❌ 存储系统测试失败: ${error.message}`;
                results.appendChild(div);
            }
        };
        
        // 移动端测试
        window.testMobile = function() {
            const results = document.getElementById('mobile-results');
            results.innerHTML = '';
            
            const tests = [
                {
                    name: '触摸事件支持',
                    test: () => 'ontouchstart' in window
                },
                {
                    name: '设备像素比',
                    test: () => window.devicePixelRatio >= 1
                },
                {
                    name: '视口设置',
                    test: () => {
                        const viewport = document.querySelector('meta[name="viewport"]');
                        return viewport && viewport.content.includes('width=device-width');
                    }
                },
                {
                    name: 'PWA 支持',
                    test: () => {
                        const manifest = document.querySelector('link[rel="manifest"]');
                        return manifest !== null;
                    }
                }
            ];
            
            tests.forEach(test => {
                try {
                    const passed = test.test();
                    const div = document.createElement('div');
                    div.className = `test-result ${passed ? 'success' : 'warning'}`;
                    div.textContent = `${passed ? '✅' : '⚠️'} ${test.name}`;
                    results.appendChild(div);
                } catch (error) {
                    const div = document.createElement('div');
                    div.className = 'test-result error';
                    div.textContent = `❌ ${test.name}: ${error.message}`;
                    results.appendChild(div);
                }
            });
        };
    </script>
</body>
</html>
