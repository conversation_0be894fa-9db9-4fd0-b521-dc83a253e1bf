<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>元素守护者 - Elemental Guardians</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/ui.css">
    <link rel="stylesheet" href="styles/mobile.css">
    <!-- 预加载关键资源 -->
    <link rel="preload" href="js/core/GameEngine.js" as="script">
    <link rel="preload" href="js/core/Renderer.js" as="script">
    <meta name="description" content="元素守护者 - 一款创新的塔防游戏，支持元素融合、动态地形和敌人进化系统">
    <meta name="keywords" content="塔防游戏,元素融合,HTML5游戏,移动端游戏">

    <!-- PWA支持 -->
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#2c3e50">

    <!-- 移动端优化 -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="元素守护者">

    <!-- 防止双击缩放 -->
    <meta name="format-detection" content="telephone=no">
</head>
<body>
    <!-- 加载界面 -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">
                <div class="element-circle fire"></div>
                <div class="element-circle water"></div>
                <div class="element-circle earth"></div>
                <div class="element-circle air"></div>
            </div>
            <h1>元素守护者</h1>
            <div class="loading-bar">
                <div class="loading-progress" id="loading-progress"></div>
            </div>
            <p id="loading-text">正在加载游戏资源...</p>
        </div>
    </div>

    <!-- 主游戏界面 -->
    <div id="game-container" class="game-container" style="display: none;">
        <!-- 游戏画布 -->
        <canvas id="game-canvas" class="game-canvas"></canvas>

        <!-- 游戏UI覆盖层 -->
        <div id="game-ui" class="game-ui">
            <!-- 顶部状态栏 -->
            <div class="top-bar">
                <div class="resource-display">
                    <div class="resource-item">
                        <span class="resource-icon gold">💰</span>
                        <span id="gold-amount">500</span>
                    </div>
                    <div class="resource-item">
                        <span class="resource-icon life">❤️</span>
                        <span id="life-amount">20</span>
                    </div>
                    <div class="resource-item">
                        <span class="resource-icon wave">🌊</span>
                        <span id="wave-info">第 1 波</span>
                    </div>
                </div>

                <div class="game-controls">
                    <button id="pause-btn" class="control-btn">⏸️</button>
                    <button id="speed-btn" class="control-btn" data-speed="1">1x</button>
                    <button id="menu-btn" class="control-btn">☰</button>
                </div>
            </div>

            <!-- 塔防建造面板 -->
            <div id="tower-panel" class="tower-panel">
                <div class="panel-header">
                    <h3>防御塔</h3>
                    <button id="close-panel" class="close-btn">×</button>
                </div>

                <div class="tower-grid">
                    <!-- 基础元素塔 -->
                    <div class="tower-item" data-tower="fire" data-cost="50">
                        <div class="tower-icon fire-tower">🔥</div>
                        <div class="tower-info">
                            <div class="tower-name">火焰塔</div>
                            <div class="tower-cost">50金</div>
                        </div>
                    </div>

                    <div class="tower-item" data-tower="water" data-cost="60">
                        <div class="tower-icon water-tower">💧</div>
                        <div class="tower-info">
                            <div class="tower-name">冰霜塔</div>
                            <div class="tower-cost">60金</div>
                        </div>
                    </div>

                    <div class="tower-item" data-tower="earth" data-cost="80">
                        <div class="tower-icon earth-tower">🗿</div>
                        <div class="tower-info">
                            <div class="tower-name">岩石塔</div>
                            <div class="tower-cost">80金</div>
                        </div>
                    </div>

                    <div class="tower-item" data-tower="air" data-cost="70">
                        <div class="tower-icon air-tower">💨</div>
                        <div class="tower-info">
                            <div class="tower-name">风暴塔</div>
                            <div class="tower-cost">70金</div>
                        </div>
                    </div>
                </div>

                <!-- 融合塔区域 -->
                <div class="fusion-section">
                    <h4>元素融合</h4>
                    <div class="fusion-grid" id="fusion-towers">
                        <!-- 动态生成融合塔选项 -->
                    </div>
                </div>
            </div>

            <!-- 塔升级面板 -->
            <div id="upgrade-panel" class="upgrade-panel" style="display: none;">
                <div class="panel-header">
                    <h3>塔升级</h3>
                    <button id="close-upgrade" class="close-btn">×</button>
                </div>

                <div class="tower-stats">
                    <div class="stat-item">
                        <span>攻击力:</span>
                        <span id="tower-damage">0</span>
                    </div>
                    <div class="stat-item">
                        <span>射程:</span>
                        <span id="tower-range">0</span>
                    </div>
                    <div class="stat-item">
                        <span>攻击速度:</span>
                        <span id="tower-speed">0</span>
                    </div>
                </div>

                <div class="upgrade-options">
                    <button id="upgrade-damage" class="upgrade-btn">
                        <span>提升攻击力</span>
                        <span class="upgrade-cost">100金</span>
                    </button>
                    <button id="upgrade-range" class="upgrade-btn">
                        <span>扩大射程</span>
                        <span class="upgrade-cost">80金</span>
                    </button>
                    <button id="upgrade-speed" class="upgrade-btn">
                        <span>提升速度</span>
                        <span class="upgrade-cost">120金</span>
                    </button>
                    <button id="sell-tower" class="sell-btn">
                        <span>出售塔</span>
                        <span class="sell-value">0金</span>
                    </button>
                </div>
            </div>

            <!-- 波次信息面板 -->
            <div id="wave-info-panel" class="wave-info-panel">
                <div class="next-wave-info">
                    <h4>下一波敌人</h4>
                    <div class="enemy-preview" id="enemy-preview">
                        <!-- 动态显示即将到来的敌人类型 -->
                    </div>
                    <button id="start-wave" class="start-wave-btn">开始下一波</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 游戏菜单 -->
    <div id="game-menu" class="game-menu" style="display: none;">
        <div class="menu-content">
            <h2>游戏菜单</h2>
            <div class="menu-buttons">
                <button id="resume-game" class="menu-btn">继续游戏</button>
                <button id="save-game" class="menu-btn">保存游戏</button>
                <button id="load-game" class="menu-btn">载入游戏</button>
                <button id="restart-game" class="menu-btn">重新开始</button>
                <button id="settings" class="menu-btn">设置</button>
                <button id="help" class="menu-btn">帮助</button>
            </div>
        </div>
    </div>

    <!-- 设置面板 -->
    <div id="settings-panel" class="settings-panel" style="display: none;">
        <div class="panel-content">
            <h3>游戏设置</h3>
            <div class="setting-item">
                <label>音效音量:</label>
                <input type="range" id="sfx-volume" min="0" max="100" value="70">
                <span id="sfx-value">70%</span>
            </div>
            <div class="setting-item">
                <label>背景音乐:</label>
                <input type="range" id="bgm-volume" min="0" max="100" value="50">
                <span id="bgm-value">50%</span>
            </div>
            <div class="setting-item">
                <label>画质设置:</label>
                <select id="quality-setting">
                    <option value="low">低</option>
                    <option value="medium" selected>中</option>
                    <option value="high">高</option>
                </select>
            </div>
            <div class="setting-item">
                <label>粒子效果:</label>
                <input type="checkbox" id="particle-effects" checked>
            </div>
            <button id="close-settings" class="close-btn">关闭</button>
        </div>
    </div>

    <!-- 帮助面板 -->
    <div id="help-panel" class="help-panel" style="display: none;">
        <div class="panel-content">
            <h3>游戏帮助</h3>
            <div class="help-content">
                <h4>基础玩法</h4>
                <p>• 点击空地建造防御塔阻止敌人到达终点</p>
                <p>• 不同元素的塔有不同的特殊效果</p>
                <p>• 相邻的塔可以进行元素融合产生更强效果</p>

                <h4>元素特性</h4>
                <p>🔥 火焰：造成持续伤害</p>
                <p>💧 冰霜：减缓敌人移动速度</p>
                <p>🗿 岩石：高攻击力，可阻挡敌人</p>
                <p>💨 风暴：范围攻击，击退效果</p>

                <h4>融合系统</h4>
                <p>• 火+水=蒸汽：范围减速+伤害</p>
                <p>• 火+土=熔岩：高伤害+地形改变</p>
                <p>• 水+土=泥沼：强力减速</p>
                <p>• 风+火=爆炸：范围高伤害</p>
            </div>
            <button id="close-help" class="close-btn">关闭</button>
        </div>
    </div>

    <!-- 游戏结束界面 -->
    <div id="game-over" class="game-over" style="display: none;">
        <div class="game-over-content">
            <h2 id="game-over-title">游戏结束</h2>
            <div class="final-stats">
                <p>存活波数: <span id="final-wave">0</span></p>
                <p>击杀敌人: <span id="final-kills">0</span></p>
                <p>获得金币: <span id="final-gold">0</span></p>
                <p>游戏时长: <span id="final-time">0</span></p>
            </div>
            <div class="game-over-buttons">
                <button id="restart-btn" class="game-over-btn">重新开始</button>
                <button id="main-menu-btn" class="game-over-btn">主菜单</button>
            </div>
        </div>
    </div>

    <!-- JavaScript模块加载 -->
    <script type="module" src="js/main.js"></script>

    <!-- Service Worker 注册 -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('./sw.js')
                    .then(registration => {
                        console.log('✅ Service Worker 注册成功:', registration.scope);
                    })
                    .catch(error => {
                        console.log('❌ Service Worker 注册失败:', error);
                    });
            });
        }
    </script>
</body>
</html>