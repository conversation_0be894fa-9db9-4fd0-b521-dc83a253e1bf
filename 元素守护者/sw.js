/**
 * Service Worker for 元素守护者
 * 提供离线缓存和PWA功能
 */

const CACHE_NAME = 'elemental-guardians-v1.0.0';
const urlsToCache = [
    './',
    './index.html',
    './manifest.json',
    './styles/main.css',
    './styles/ui.css',
    './styles/mobile.css',
    './js/main.js',
    './js/core/GameEngine.js',
    './js/core/Renderer.js',
    './js/core/InputManager.js',
    './js/core/AudioManager.js',
    './js/systems/TowerManager.js',
    './js/systems/EnemyManager.js',
    './js/systems/ProjectileManager.js',
    './js/systems/WaveManager.js',
    './js/systems/TerrainManager.js',
    './js/systems/SaveManager.js',
    './js/systems/UIManager.js',
    './js/utils/Vector2.js',
    './js/utils/GameState.js'
];

// 安装事件 - 缓存资源
self.addEventListener('install', event => {
    console.log('🔧 Service Worker 安装中...');
    
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => {
                console.log('📦 缓存游戏资源');
                return cache.addAll(urlsToCache);
            })
            .then(() => {
                console.log('✅ Service Worker 安装完成');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('❌ Service Worker 安装失败:', error);
            })
    );
});

// 激活事件 - 清理旧缓存
self.addEventListener('activate', event => {
    console.log('🚀 Service Worker 激活中...');
    
    event.waitUntil(
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames.map(cacheName => {
                    if (cacheName !== CACHE_NAME) {
                        console.log('🗑️ 删除旧缓存:', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        }).then(() => {
            console.log('✅ Service Worker 激活完成');
            return self.clients.claim();
        })
    );
});

// 拦截网络请求 - 缓存优先策略
self.addEventListener('fetch', event => {
    // 只处理同源请求
    if (!event.request.url.startsWith(self.location.origin)) {
        return;
    }
    
    event.respondWith(
        caches.match(event.request)
            .then(response => {
                // 如果缓存中有，直接返回
                if (response) {
                    console.log('📦 从缓存返回:', event.request.url);
                    return response;
                }
                
                // 否则从网络获取
                console.log('🌐 从网络获取:', event.request.url);
                return fetch(event.request).then(response => {
                    // 检查响应是否有效
                    if (!response || response.status !== 200 || response.type !== 'basic') {
                        return response;
                    }
                    
                    // 克隆响应，因为响应流只能使用一次
                    const responseToCache = response.clone();
                    
                    // 将新资源添加到缓存
                    caches.open(CACHE_NAME)
                        .then(cache => {
                            cache.put(event.request, responseToCache);
                        });
                    
                    return response;
                });
            })
            .catch(error => {
                console.error('❌ 网络请求失败:', error);
                
                // 如果是HTML请求且网络失败，返回离线页面
                if (event.request.destination === 'document') {
                    return caches.match('./index.html');
                }
                
                // 其他资源返回错误
                return new Response('网络错误', {
                    status: 408,
                    headers: { 'Content-Type': 'text/plain; charset=utf-8' }
                });
            })
    );
});

// 处理消息事件
self.addEventListener('message', event => {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        console.log('⏭️ 跳过等待，立即激活新版本');
        self.skipWaiting();
    }
    
    if (event.data && event.data.type === 'GET_VERSION') {
        event.ports[0].postMessage({
            version: CACHE_NAME
        });
    }
});

// 推送通知支持（可选）
self.addEventListener('push', event => {
    if (event.data) {
        const data = event.data.json();
        
        const options = {
            body: data.body || '游戏有新的更新！',
            icon: './manifest.json',
            badge: './manifest.json',
            vibrate: [100, 50, 100],
            data: {
                dateOfArrival: Date.now(),
                primaryKey: data.primaryKey || 'default'
            },
            actions: [
                {
                    action: 'explore',
                    title: '查看详情',
                    icon: './manifest.json'
                },
                {
                    action: 'close',
                    title: '关闭',
                    icon: './manifest.json'
                }
            ]
        };
        
        event.waitUntil(
            self.registration.showNotification(data.title || '元素守护者', options)
        );
    }
});

// 通知点击事件
self.addEventListener('notificationclick', event => {
    event.notification.close();
    
    if (event.action === 'explore') {
        // 打开游戏
        event.waitUntil(
            clients.openWindow('./')
        );
    }
});

console.log('🎮 元素守护者 Service Worker 已加载');
