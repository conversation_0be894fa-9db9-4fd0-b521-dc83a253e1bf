/* 元素守护者 - 移动端适配样式 */

/* 移动端基础适配 */
@media (max-width: 768px) {
    /* 顶部状态栏移动端优化 */
    .top-bar {
        height: 50px;
        padding: 0 15px;
    }

    .resource-display {
        gap: 15px;
    }

    .resource-item {
        padding: 6px 12px;
        font-size: 1rem;
    }

    .resource-icon {
        font-size: 1.1rem;
    }

    .control-btn {
        width: 40px;
        height: 40px;
        font-size: 1.1rem;
    }

    /* 游戏画布触摸优化 */
    .game-canvas {
        touch-action: none;
        cursor: default;
    }

    /* 塔防面板移动端布局 */
    .tower-panel {
        bottom: 10px;
        left: 10px;
        right: 10px;
        width: auto;
        max-height: 40vh;
        overflow-y: auto;
    }

    .tower-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 8px;
        padding: 15px;
    }

    .tower-item {
        flex-direction: column;
        text-align: center;
        padding: 8px;
        gap: 6px;
    }

    .tower-icon {
        width: 40px;
        height: 40px;
        font-size: 1.4rem;
    }

    .tower-name {
        font-size: 0.85rem;
        line-height: 1.2;
    }

    .tower-cost {
        font-size: 0.75rem;
    }

    /* 融合区域移动端优化 */
    .fusion-section {
        padding: 15px;
    }

    .fusion-section h4 {
        font-size: 1rem;
        margin-bottom: 10px;
    }

    .fusion-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 8px;
    }

    .fusion-tower {
        flex-direction: column;
        text-align: center;
        padding: 8px;
        gap: 4px;
    }

    .fusion-icon {
        width: 30px;
        height: 30px;
        font-size: 1rem;
    }

    .fusion-info {
        font-size: 0.8rem;
    }

    .fusion-name {
        font-size: 0.75rem;
    }

    .fusion-cost {
        font-size: 0.7rem;
    }

    /* 升级面板移动端适配 */
    .upgrade-panel {
        bottom: 50%;
        left: 50%;
        right: auto;
        transform: translate(-50%, 50%);
        width: 90%;
        max-width: 350px;
    }

    .tower-stats {
        padding: 15px;
    }

    .stat-item {
        font-size: 0.9rem;
        margin-bottom: 8px;
    }

    .upgrade-options {
        padding: 15px;
        gap: 10px;
    }

    .upgrade-btn {
        padding: 10px 12px;
        font-size: 0.9rem;
    }

    /* 波次信息面板移动端 */
    .wave-info-panel {
        top: 60px;
        left: 10px;
        right: 10px;
        width: auto;
        padding: 15px;
    }

    .next-wave-info h4 {
        font-size: 1.1rem;
        margin-bottom: 12px;
    }

    .enemy-preview {
        gap: 6px;
        margin-bottom: 15px;
    }

    .enemy-icon {
        width: 35px;
        height: 35px;
        font-size: 1.1rem;
    }

    .enemy-count {
        width: 16px;
        height: 16px;
        font-size: 0.65rem;
        top: -3px;
        right: -3px;
    }

    .start-wave-btn {
        padding: 12px;
        font-size: 1rem;
    }

    /* 菜单和面板移动端优化 */
    .menu-content {
        padding: 30px 25px;
        margin: 20px;
        width: calc(100% - 40px);
        max-width: 350px;
    }

    .menu-content h2 {
        font-size: 1.8rem;
        margin-bottom: 25px;
    }

    .menu-buttons {
        min-width: auto;
        gap: 12px;
    }

    .menu-btn {
        padding: 12px 25px;
        font-size: 1rem;
    }

    /* 设置和帮助面板移动端 */
    .panel-content {
        padding: 25px 20px;
        margin: 20px;
        width: calc(100% - 40px);
        max-height: 70vh;
    }

    .panel-content h3 {
        font-size: 1.5rem;
        margin-bottom: 20px;
    }

    .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        margin-bottom: 15px;
    }

    .setting-item input[type="range"] {
        width: 100%;
        margin: 0;
    }

    .setting-item select {
        width: 100%;
        padding: 8px;
    }

    .help-content h4 {
        font-size: 1.2rem;
        margin: 15px 0 8px 0;
    }

    .help-content p {
        font-size: 0.95rem;
        margin-bottom: 6px;
    }

    /* 游戏结束界面移动端 */
    .game-over-content {
        padding: 30px 25px;
        margin: 20px;
        width: calc(100% - 40px);
        max-width: 350px;
    }

    #game-over-title {
        font-size: 2rem;
        margin-bottom: 25px;
    }

    .final-stats p {
        font-size: 1rem;
        margin: 8px 0;
    }

    .game-over-buttons {
        flex-direction: column;
        gap: 10px;
    }

    .game-over-btn {
        width: 100%;
        padding: 12px;
        font-size: 1rem;
    }
}