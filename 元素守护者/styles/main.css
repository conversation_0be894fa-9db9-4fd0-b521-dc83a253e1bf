/* 元素守护者 - 主样式文件 */

/* 全局重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    overflow: hidden;
    user-select: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
}

/* 加载界面样式 */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-logo {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
    gap: 15px;
}

.element-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    animation: elementPulse 2s infinite ease-in-out;
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

.element-circle.fire {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    animation-delay: 0s;
}

.element-circle.water {
    background: linear-gradient(45deg, #74b9ff, #0984e3);
    animation-delay: 0.5s;
}

.element-circle.earth {
    background: linear-gradient(45deg, #a29bfe, #6c5ce7);
    animation-delay: 1s;
}

.element-circle.air {
    background: linear-gradient(45deg, #fd79a8, #e84393);
    animation-delay: 1.5s;
}

@keyframes elementPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
}

.loading-content h1 {
    font-size: 2.5rem;
    margin-bottom: 30px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    background: linear-gradient(45deg, #ffecd2, #fcb69f);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.loading-bar {
    width: 300px;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
    margin: 20px auto;
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, #ff6b6b, #74b9ff, #a29bfe, #fd79a8);
    background-size: 200% 100%;
    border-radius: 4px;
    width: 0%;
    transition: width 0.3s ease;
    animation: progressGlow 2s infinite;
}

@keyframes progressGlow {
    0% { background-position: 0% 50%; }
    100% { background-position: 200% 50%; }
}

#loading-text {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-top: 10px;
}

/* 游戏容器样式 */
.game-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
}

.game-canvas {
    display: block;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    cursor: crosshair;
}

/* 游戏UI覆盖层 */
.game-ui {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 100;
}

.game-ui > * {
    pointer-events: auto;
}

/* 顶部状态栏 */
.top-bar {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: linear-gradient(135deg, rgba(44, 62, 80, 0.95), rgba(52, 73, 94, 0.95));
    backdrop-filter: blur(10px);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    border-bottom: 2px solid rgba(255, 255, 255, 0.1);
}

.resource-display {
    display: flex;
    gap: 30px;
}

.resource-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: white;
    font-weight: bold;
    font-size: 1.1rem;
    padding: 8px 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.resource-icon {
    font-size: 1.3rem;
    filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.5));
}

.game-controls {
    display: flex;
    gap: 10px;
}

.control-btn {
    width: 45px;
    height: 45px;
    border: none;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.control-btn:active {
    transform: translateY(0);
}

/* 面板通用样式 */
.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-radius: 10px 10px 0 0;
}

.panel-header h3 {
    font-size: 1.3rem;
    font-weight: bold;
}

.close-btn {
    width: 30px;
    height: 30px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* 游戏菜单样式 */
.game-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.menu-content {
    background: linear-gradient(135deg, rgba(44, 62, 80, 0.95), rgba(52, 73, 94, 0.95));
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.1);
}

.menu-content h2 {
    color: white;
    font-size: 2rem;
    margin-bottom: 30px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.menu-buttons {
    display: flex;
    flex-direction: column;
    gap: 15px;
    min-width: 250px;
}

.menu-btn {
    padding: 15px 30px;
    border: none;
    border-radius: 25px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.menu-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    background: linear-gradient(135deg, #764ba2, #667eea);
}

.menu-btn:active {
    transform: translateY(0);
}

/* 游戏结束界面 */
.game-over {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(15px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1001;
}

.game-over-content {
    background: linear-gradient(135deg, rgba(44, 62, 80, 0.95), rgba(52, 73, 94, 0.95));
    padding: 50px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
    border: 2px solid rgba(255, 255, 255, 0.1);
    max-width: 400px;
    width: 90%;
}

#game-over-title {
    color: #ff6b6b;
    font-size: 2.5rem;
    margin-bottom: 30px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.final-stats {
    color: white;
    margin-bottom: 30px;
}

.final-stats p {
    margin: 10px 0;
    font-size: 1.1rem;
    display: flex;
    justify-content: space-between;
}

.game-over-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.game-over-btn {
    padding: 12px 25px;
    border: none;
    border-radius: 20px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.game-over-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}