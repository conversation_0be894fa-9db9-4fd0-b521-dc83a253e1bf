/* 元素守护者 - UI界面样式 */

/* 塔防建造面板 */
.tower-panel {
    position: absolute;
    bottom: 20px;
    left: 20px;
    width: 350px;
    background: rgba(44, 62, 80, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.1);
    transform: translateY(100%);
    transition: transform 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.tower-panel.active {
    transform: translateY(0);
}

.tower-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    padding: 20px;
}

.tower-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.tower-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

.tower-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.tower-item.disabled:hover {
    transform: none;
    background: rgba(255, 255, 255, 0.1);
    border-color: transparent;
}

.tower-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

.fire-tower {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    animation: fireGlow 2s infinite ease-in-out;
}

.water-tower {
    background: linear-gradient(45deg, #74b9ff, #0984e3);
    animation: waterGlow 2s infinite ease-in-out;
}

.earth-tower {
    background: linear-gradient(45deg, #a29bfe, #6c5ce7);
    animation: earthGlow 2s infinite ease-in-out;
}

.air-tower {
    background: linear-gradient(45deg, #fd79a8, #e84393);
    animation: airGlow 2s infinite ease-in-out;
}

@keyframes fireGlow {
    0%, 100% { box-shadow: 0 0 15px rgba(255, 107, 107, 0.5); }
    50% { box-shadow: 0 0 25px rgba(255, 107, 107, 0.8); }
}

@keyframes waterGlow {
    0%, 100% { box-shadow: 0 0 15px rgba(116, 185, 255, 0.5); }
    50% { box-shadow: 0 0 25px rgba(116, 185, 255, 0.8); }
}

@keyframes earthGlow {
    0%, 100% { box-shadow: 0 0 15px rgba(162, 155, 254, 0.5); }
    50% { box-shadow: 0 0 25px rgba(162, 155, 254, 0.8); }
}

@keyframes airGlow {
    0%, 100% { box-shadow: 0 0 15px rgba(253, 121, 168, 0.5); }
    50% { box-shadow: 0 0 25px rgba(253, 121, 168, 0.8); }
}

.tower-info {
    flex: 1;
    color: white;
}

.tower-name {
    font-size: 1.1rem;
    font-weight: bold;
    margin-bottom: 4px;
}

.tower-cost {
    font-size: 0.9rem;
    opacity: 0.8;
    color: #ffd700;
}

/* 融合塔区域 */
.fusion-section {
    border-top: 2px solid rgba(255, 255, 255, 0.1);
    padding: 20px;
}

.fusion-section h4 {
    color: white;
    font-size: 1.2rem;
    margin-bottom: 15px;
    text-align: center;
    background: linear-gradient(45deg, #ffecd2, #fcb69f);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.fusion-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
}

.fusion-tower {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.fusion-tower:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.02);
}

.fusion-icon {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    background: linear-gradient(45deg, #667eea, #764ba2);
}

.fusion-info {
    flex: 1;
    color: white;
    font-size: 0.9rem;
}

.fusion-name {
    font-weight: bold;
    margin-bottom: 2px;
}

.fusion-cost {
    opacity: 0.7;
    color: #ffd700;
    font-size: 0.8rem;
}

/* 塔升级面板 */
.upgrade-panel {
    position: absolute;
    bottom: 20px;
    right: 20px;
    width: 300px;
    background: rgba(44, 62, 80, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.1);
}

.tower-stats {
    padding: 20px;
    border-bottom: 2px solid rgba(255, 255, 255, 0.1);
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: white;
    margin-bottom: 10px;
    font-size: 1rem;
}

.stat-item:last-child {
    margin-bottom: 0;
}

.upgrade-options {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.upgrade-btn {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    border: none;
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.upgrade-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    background: linear-gradient(135deg, #764ba2, #667eea);
}

.upgrade-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.upgrade-cost {
    color: #ffd700;
    font-weight: bold;
}

.sell-btn {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    margin-top: 10px;
}

.sell-btn:hover {
    background: linear-gradient(135deg, #ee5a24, #ff6b6b);
}

.sell-value {
    color: #90ee90;
    font-weight: bold;
}

/* 波次信息面板 */
.wave-info-panel {
    position: absolute;
    top: 80px;
    right: 20px;
    width: 280px;
    background: rgba(44, 62, 80, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.1);
    padding: 20px;
}

.next-wave-info h4 {
    color: white;
    font-size: 1.2rem;
    margin-bottom: 15px;
    text-align: center;
    background: linear-gradient(45deg, #ffecd2, #fcb69f);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.enemy-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 20px;
    justify-content: center;
}

.enemy-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    box-shadow: 0 0 10px rgba(255, 107, 107, 0.5);
    position: relative;
}

.enemy-count {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ffd700;
    color: #333;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: bold;
}

.start-wave-btn {
    width: 100%;
    padding: 15px;
    border: none;
    border-radius: 25px;
    background: linear-gradient(135deg, #00b894, #00a085);
    color: white;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.start-wave-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    background: linear-gradient(135deg, #00a085, #00b894);
}

.start-wave-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* 设置和帮助面板 */
.settings-panel,
.help-panel {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.panel-content {
    background: linear-gradient(135deg, rgba(44, 62, 80, 0.95), rgba(52, 73, 94, 0.95));
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.1);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.panel-content h3 {
    color: white;
    font-size: 1.8rem;
    margin-bottom: 25px;
    text-align: center;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    color: white;
}

.setting-item label {
    font-size: 1.1rem;
    font-weight: bold;
}

.setting-item input[type="range"] {
    width: 120px;
    margin: 0 10px;
}

.setting-item select {
    padding: 5px 10px;
    border-radius: 5px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 1rem;
}

.setting-item input[type="checkbox"] {
    width: 20px;
    height: 20px;
    accent-color: #667eea;
}

.help-content {
    color: white;
    line-height: 1.6;
}

.help-content h4 {
    color: #ffd700;
    font-size: 1.3rem;
    margin: 20px 0 10px 0;
    border-bottom: 2px solid rgba(255, 215, 0, 0.3);
    padding-bottom: 5px;
}

.help-content p {
    margin-bottom: 8px;
    font-size: 1rem;
}

/* 响应式设计预览 */
@media (max-width: 768px) {
    .tower-panel {
        width: calc(100% - 40px);
        left: 20px;
        right: 20px;
    }

    .upgrade-panel {
        width: calc(100% - 40px);
        right: 20px;
        left: 20px;
        bottom: 200px;
    }

    .wave-info-panel {
        width: calc(100% - 40px);
        right: 20px;
        left: 20px;
        top: 70px;
    }

    .tower-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 10px;
    }

    .tower-item {
        flex-direction: column;
        text-align: center;
        padding: 10px;
    }

    .tower-info {
        margin-top: 5px;
    }

    .tower-name {
        font-size: 0.9rem;
    }

    .tower-cost {
        font-size: 0.8rem;
    }
}