#!/bin/bash

# 元素守护者部署脚本
# 用于将游戏部署到静态托管服务

echo "🎮 开始部署元素守护者..."

# 检查必要文件
echo "📋 检查必要文件..."
required_files=(
    "index.html"
    "manifest.json"
    "sw.js"
    "styles/main.css"
    "styles/ui.css" 
    "styles/mobile.css"
    "js/main.js"
    "README.md"
)

missing_files=()
for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -ne 0 ]; then
    echo "❌ 缺少必要文件:"
    printf '%s\n' "${missing_files[@]}"
    exit 1
fi

echo "✅ 所有必要文件都存在"

# 创建部署目录
DEPLOY_DIR="dist"
echo "📁 创建部署目录: $DEPLOY_DIR"
rm -rf "$DEPLOY_DIR"
mkdir -p "$DEPLOY_DIR"

# 复制文件到部署目录
echo "📦 复制文件到部署目录..."
cp -r * "$DEPLOY_DIR/" 2>/dev/null || true

# 排除不需要的文件
echo "🧹 清理不需要的文件..."
cd "$DEPLOY_DIR"
rm -f deploy.sh
rm -f .gitignore
rm -rf .git
rm -rf node_modules
rm -rf .vscode

# 压缩CSS和JS文件（如果有相关工具）
if command -v terser &> /dev/null; then
    echo "🗜️ 压缩JavaScript文件..."
    find js -name "*.js" -type f -exec terser {} --compress --mangle -o {} \;
fi

if command -v cleancss &> /dev/null; then
    echo "🗜️ 压缩CSS文件..."
    find styles -name "*.css" -type f -exec cleancss {} -o {} \;
fi

cd ..

# 生成部署信息
echo "📝 生成部署信息..."
cat > "$DEPLOY_DIR/deploy-info.json" << EOF
{
    "name": "元素守护者",
    "version": "1.0.0",
    "deployTime": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
    "deployBy": "$(whoami)",
    "gitCommit": "$(git rev-parse HEAD 2>/dev/null || echo 'unknown')",
    "files": $(find "$DEPLOY_DIR" -type f | wc -l),
    "size": "$(du -sh "$DEPLOY_DIR" | cut -f1)"
}
EOF

# 显示部署统计
echo ""
echo "📊 部署统计:"
echo "   文件数量: $(find "$DEPLOY_DIR" -type f | wc -l)"
echo "   总大小: $(du -sh "$DEPLOY_DIR" | cut -f1)"
echo "   部署目录: $DEPLOY_DIR"

echo ""
echo "🚀 部署准备完成！"
echo ""
echo "📋 部署选项:"
echo "   1. 本地测试: cd $DEPLOY_DIR && python -m http.server 8000"
echo "   2. GitHub Pages: 将 $DEPLOY_DIR 内容推送到 gh-pages 分支"
echo "   3. Netlify: 拖拽 $DEPLOY_DIR 文件夹到 Netlify"
echo "   4. Vercel: vercel --prod $DEPLOY_DIR"
echo "   5. Firebase: firebase deploy --public $DEPLOY_DIR"
echo ""

# 可选：自动部署到特定平台
if [ "$1" = "netlify" ]; then
    if command -v netlify &> /dev/null; then
        echo "🌐 部署到 Netlify..."
        cd "$DEPLOY_DIR"
        netlify deploy --prod --dir .
        cd ..
    else
        echo "❌ Netlify CLI 未安装"
    fi
elif [ "$1" = "vercel" ]; then
    if command -v vercel &> /dev/null; then
        echo "▲ 部署到 Vercel..."
        vercel --prod "$DEPLOY_DIR"
    else
        echo "❌ Vercel CLI 未安装"
    fi
elif [ "$1" = "firebase" ]; then
    if command -v firebase &> /dev/null; then
        echo "🔥 部署到 Firebase..."
        firebase deploy --public "$DEPLOY_DIR"
    else
        echo "❌ Firebase CLI 未安装"
    fi
elif [ "$1" = "surge" ]; then
    if command -v surge &> /dev/null; then
        echo "⚡ 部署到 Surge..."
        cd "$DEPLOY_DIR"
        surge . --domain elemental-guardians.surge.sh
        cd ..
    else
        echo "❌ Surge CLI 未安装"
    fi
fi

echo "✨ 部署脚本执行完成！"
