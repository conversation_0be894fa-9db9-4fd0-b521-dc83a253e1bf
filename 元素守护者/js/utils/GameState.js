/**
 * 游戏状态管理类
 * 存储和管理游戏的各种状态数据
 */

export class GameState {
    constructor() {
        // 基础游戏状态
        this.isPlaying = false;
        this.isPaused = false;
        this.gameStartTime = 0;
        this.gameTime = 0;
        this.debugMode = false;

        // 玩家资源
        this.gold = 200;           // 初始金币
        this.lives = 20;           // 生命值
        this.score = 0;            // 分数

        // 波次信息
        this.currentWave = 1;      // 当前波次
        this.totalWaves = 10;      // 总波次数
        this.waveProgress = 0;     // 当前波次进度 (0-1)
        this.timeBetweenWaves = 5; // 波次间隔时间（秒）

        // 统计信息
        this.enemiesKilled = 0;    // 击杀敌人数量
        this.towersBuilt = 0;      // 建造塔的数量
        this.totalGoldEarned = 0;  // 总共获得的金币
        this.totalDamageDealt = 0; // 总伤害输出
        this.accuracyRate = 0;     // 命中率

        // 游戏设置
        this.gameSpeed = 1.0;      // 游戏速度倍率
        this.soundEnabled = true;  // 音效开关
        this.musicEnabled = true;  // 音乐开关
        this.showFPS = false;      // 显示FPS
        this.showGrid = false;     // 显示网格

        // 难度设置
        this.difficulty = 'normal'; // easy, normal, hard
        this.difficultyMultiplier = 1.0;

        // 元素融合系统状态
        this.unlockedFusions = new Set(); // 已解锁的融合组合
        this.fusionExperience = new Map(); // 融合经验值

        // 时间循环系统状态
        this.timeLoopAvailable = false;    // 时间循环是否可用
        this.timeLoopCooldown = 0;         // 时间循环冷却时间
        this.timeLoopCharges = 3;          // 时间循环使用次数
        this.savedStates = [];             // 保存的游戏状态快照

        // 地形变化状态
        this.terrainEvents = [];           // 地形事件列表
        this.weatherEffect = 'none';       // 当前天气效果

        console.log('📊 游戏状态管理器初始化完成');
    }

    /**
     * 重置游戏状态
     */
    reset() {
        this.isPlaying = true;
        this.isPaused = false;
        this.gameStartTime = Date.now();
        this.gameTime = 0;

        this.gold = 200;
        this.lives = 20;
        this.score = 0;

        this.currentWave = 1;
        this.waveProgress = 0;

        this.enemiesKilled = 0;
        this.towersBuilt = 0;
        this.totalGoldEarned = 200; // 包含初始金币
        this.totalDamageDealt = 0;
        this.accuracyRate = 0;

        this.unlockedFusions.clear();
        this.fusionExperience.clear();

        this.timeLoopAvailable = false;
        this.timeLoopCooldown = 0;
        this.timeLoopCharges = 3;
        this.savedStates = [];

        this.terrainEvents = [];
        this.weatherEffect = 'none';

        console.log('🔄 游戏状态已重置');
    }

    /**
     * 更新游戏时间
     */
    updateGameTime(deltaTime) {
        if (this.isPlaying && !this.isPaused) {
            this.gameTime += deltaTime * this.gameSpeed;
        }
    }

    /**
     * 添加金币
     */
    addGold(amount) {
        this.gold += amount;
        this.totalGoldEarned += amount;
        console.log(`💰 获得金币: +${amount} (总计: ${this.gold})`);
    }

    /**
     * 消耗金币
     */
    spendGold(amount) {
        if (this.gold >= amount) {
            this.gold -= amount;
            console.log(`💸 消耗金币: -${amount} (剩余: ${this.gold})`);
            return true;
        }
        return false;
    }

    /**
     * 失去生命
     */
    loseLife(amount = 1) {
        this.lives = Math.max(0, this.lives - amount);
        console.log(`💔 失去生命: -${amount} (剩余: ${this.lives})`);

        if (this.lives <= 0) {
            this.isPlaying = false;
            console.log('💀 游戏结束 - 生命耗尽');
        }
    }

    /**
     * 增加分数
     */
    addScore(points) {
        this.score += points;
        console.log(`⭐ 获得分数: +${points} (总计: ${this.score})`);
    }

    /**
     * 击杀敌人
     */
    killEnemy(enemy) {
        this.enemiesKilled++;
        this.addScore(enemy.scoreValue || 10);
        this.addGold(enemy.goldReward || 5);

        // 更新融合经验
        if (enemy.killedByElement) {
            this.addFusionExperience(enemy.killedByElement, 1);
        }
    }

    /**
     * 建造塔
     */
    buildTower(towerType, cost) {
        if (this.spendGold(cost)) {
            this.towersBuilt++;
            console.log(`🏗️ 建造了${towerType}塔 (总计: ${this.towersBuilt})`);
            return true;
        }
        return false;
    }

    /**
     * 添加融合经验
     */
    addFusionExperience(elements, amount) {
        const fusionKey = Array.isArray(elements) ? elements.sort().join('-') : elements;
        const currentExp = this.fusionExperience.get(fusionKey) || 0;
        this.fusionExperience.set(fusionKey, currentExp + amount);

        // 检查是否解锁新融合
        if (currentExp + amount >= 10 && !this.unlockedFusions.has(fusionKey)) {
            this.unlockedFusions.add(fusionKey);
            console.log(`🔓 解锁新融合: ${fusionKey}`);
        }
    }

    /**
     * 检查融合是否已解锁
     */
    isFusionUnlocked(elements) {
        const fusionKey = Array.isArray(elements) ? elements.sort().join('-') : elements;
        return this.unlockedFusions.has(fusionKey);
    }

    /**
     * 使用时间循环
     */
    useTimeLoop() {
        if (this.timeLoopCharges > 0 && this.timeLoopCooldown <= 0) {
            this.timeLoopCharges--;
            this.timeLoopCooldown = 60; // 60秒冷却
            console.log(`⏰ 使用时间循环 (剩余次数: ${this.timeLoopCharges})`);
            return true;
        }
        return false;
    }

    /**
     * 更新时间循环冷却
     */
    updateTimeLoopCooldown(deltaTime) {
        if (this.timeLoopCooldown > 0) {
            this.timeLoopCooldown = Math.max(0, this.timeLoopCooldown - deltaTime);
        }
    }

    /**
     * 保存游戏状态快照
     */
    saveStateSnapshot() {
        const snapshot = {
            timestamp: this.gameTime,
            gold: this.gold,
            lives: this.lives,
            score: this.score,
            currentWave: this.currentWave,
            enemiesKilled: this.enemiesKilled,
            towersBuilt: this.towersBuilt
        };

        this.savedStates.push(snapshot);

        // 只保留最近的5个快照
        if (this.savedStates.length > 5) {
            this.savedStates.shift();
        }

        console.log('📸 保存游戏状态快照');
    }

    /**
     * 恢复到指定快照
     */
    restoreFromSnapshot(index) {
        if (index >= 0 && index < this.savedStates.length) {
            const snapshot = this.savedStates[index];

            this.gold = snapshot.gold;
            this.lives = snapshot.lives;
            this.score = snapshot.score;
            this.currentWave = snapshot.currentWave;
            this.enemiesKilled = snapshot.enemiesKilled;
            this.towersBuilt = snapshot.towersBuilt;

            console.log(`⏪ 恢复到快照 ${index + 1}`);
            return true;
        }
        return false;
    }

    /**
     * 设置难度
     */
    setDifficulty(difficulty) {
        this.difficulty = difficulty;

        switch (difficulty) {
            case 'easy':
                this.difficultyMultiplier = 0.7;
                this.lives = 30;
                this.gold = 300;
                break;
            case 'normal':
                this.difficultyMultiplier = 1.0;
                this.lives = 20;
                this.gold = 200;
                break;
            case 'hard':
                this.difficultyMultiplier = 1.5;
                this.lives = 15;
                this.gold = 150;
                break;
        }

        console.log(`🎯 难度设置为: ${difficulty} (倍率: ${this.difficultyMultiplier})`);
    }

    /**
     * 获取游戏统计信息
     */
    getStatistics() {
        return {
            gameTime: this.gameTime,
            currentWave: this.currentWave,
            enemiesKilled: this.enemiesKilled,
            towersBuilt: this.towersBuilt,
            totalGoldEarned: this.totalGoldEarned,
            totalDamageDealt: this.totalDamageDealt,
            accuracyRate: this.accuracyRate,
            score: this.score,
            difficulty: this.difficulty,
            unlockedFusions: Array.from(this.unlockedFusions),
            timeLoopUsed: 3 - this.timeLoopCharges
        };
    }

    /**
     * 转换为JSON（用于存档）
     */
    toJSON() {
        return {
            // 基础状态
            gameTime: this.gameTime,
            gold: this.gold,
            lives: this.lives,
            score: this.score,
            currentWave: this.currentWave,

            // 统计信息
            enemiesKilled: this.enemiesKilled,
            towersBuilt: this.towersBuilt,
            totalGoldEarned: this.totalGoldEarned,
            totalDamageDealt: this.totalDamageDealt,

            // 设置
            difficulty: this.difficulty,
            gameSpeed: this.gameSpeed,
            soundEnabled: this.soundEnabled,
            musicEnabled: this.musicEnabled,

            // 特殊系统状态
            unlockedFusions: Array.from(this.unlockedFusions),
            fusionExperience: Object.fromEntries(this.fusionExperience),
            timeLoopCharges: this.timeLoopCharges,
            weatherEffect: this.weatherEffect
        };
    }

    /**
     * 从JSON恢复状态
     */
    fromJSON(data) {
        // 基础状态
        this.gameTime = data.gameTime || 0;
        this.gold = data.gold || 200;
        this.lives = data.lives || 20;
        this.score = data.score || 0;
        this.currentWave = data.currentWave || 1;

        // 统计信息
        this.enemiesKilled = data.enemiesKilled || 0;
        this.towersBuilt = data.towersBuilt || 0;
        this.totalGoldEarned = data.totalGoldEarned || 200;
        this.totalDamageDealt = data.totalDamageDealt || 0;

        // 设置
        this.difficulty = data.difficulty || 'normal';
        this.gameSpeed = data.gameSpeed || 1.0;
        this.soundEnabled = data.soundEnabled !== undefined ? data.soundEnabled : true;
        this.musicEnabled = data.musicEnabled !== undefined ? data.musicEnabled : true;

        // 特殊系统状态
        this.unlockedFusions = new Set(data.unlockedFusions || []);
        this.fusionExperience = new Map(Object.entries(data.fusionExperience || {}));
        this.timeLoopCharges = data.timeLoopCharges || 3;
        this.weatherEffect = data.weatherEffect || 'none';

        console.log('📥 游戏状态已从存档恢复');
    }
}