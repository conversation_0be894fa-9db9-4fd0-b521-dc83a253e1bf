/**
 * 音频管理器
 * 处理游戏音效和背景音乐
 */

export class AudioManager {
    constructor() {
        // 音频上下文
        this.audioContext = null;
        this.masterGain = null;
        this.sfxGain = null;
        this.musicGain = null;

        // 音频缓存
        this.audioBuffers = new Map();
        this.audioSources = new Map();

        // 音量设置
        this.masterVolume = 1.0;
        this.sfxVolume = 0.7;
        this.musicVolume = 0.5;

        // 当前播放的背景音乐
        this.currentMusic = null;

        // 音频文件路径
        this.audioFiles = {
            // 音效
            towerBuild: 'assets/sounds/tower_build.mp3',
            towerShoot: 'assets/sounds/tower_shoot.mp3',
            enemyHit: 'assets/sounds/enemy_hit.mp3',
            enemyDeath: 'assets/sounds/enemy_death.mp3',
            waveStart: 'assets/sounds/wave_start.mp3',
            waveComplete: 'assets/sounds/wave_complete.mp3',
            gameOver: 'assets/sounds/game_over.mp3',
            victory: 'assets/sounds/victory.mp3',

            // 背景音乐
            bgmMain: 'assets/sounds/bgm_main.mp3',
            bgmBattle: 'assets/sounds/bgm_battle.mp3'
        };

        this.init();
        console.log('🔊 音频管理器初始化完成');
    }

    /**
     * 初始化音频系统
     */
    async init() {
        try {
            // 创建音频上下文
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();

            // 创建增益节点
            this.masterGain = this.audioContext.createGain();
            this.sfxGain = this.audioContext.createGain();
            this.musicGain = this.audioContext.createGain();

            // 连接音频图
            this.sfxGain.connect(this.masterGain);
            this.musicGain.connect(this.masterGain);
            this.masterGain.connect(this.audioContext.destination);

            // 设置初始音量
            this.updateVolumes();

            // 处理浏览器自动播放策略
            this.handleAutoplayPolicy();

        } catch (error) {
            console.warn('⚠️ 音频初始化失败，将使用静音模式:', error);
        }
    }

    /**
     * 处理浏览器自动播放策略
     */
    handleAutoplayPolicy() {
        const resumeAudio = () => {
            if (this.audioContext && this.audioContext.state === 'suspended') {
                this.audioContext.resume().then(() => {
                    console.log('🔊 音频上下文已恢复');
                });
            }
        };

        // 在用户首次交互时恢复音频上下文
        document.addEventListener('click', resumeAudio, { once: true });
        document.addEventListener('touchstart', resumeAudio, { once: true });
        document.addEventListener('keydown', resumeAudio, { once: true });
    }

    /**
     * 加载音频文件
     */
    async loadAudio(name, url) {
        if (this.audioBuffers.has(name)) {
            return this.audioBuffers.get(name);
        }

        try {
            const response = await fetch(url);
            const arrayBuffer = await response.arrayBuffer();
            const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);

            this.audioBuffers.set(name, audioBuffer);
            console.log(`🎵 音频加载成功: ${name}`);
            return audioBuffer;

        } catch (error) {
            console.warn(`⚠️ 音频加载失败: ${name}`, error);
            return null;
        }
    }

    /**
     * 预加载所有音频文件
     */
    async preloadAll() {
        const loadPromises = Object.entries(this.audioFiles).map(([name, url]) =>
            this.loadAudio(name, url)
        );

        await Promise.allSettled(loadPromises);
        console.log('🎵 所有音频文件预加载完成');
    }

    /**
     * 播放音效
     */
    playSFX(name, options = {}) {
        if (!this.audioContext || !this.audioBuffers.has(name)) {
            return null;
        }

        const buffer = this.audioBuffers.get(name);
        if (!buffer) return null;

        const source = this.audioContext.createBufferSource();
        const gainNode = this.audioContext.createGain();

        source.buffer = buffer;
        source.connect(gainNode);
        gainNode.connect(this.sfxGain);

        // 设置音效参数
        const volume = options.volume !== undefined ? options.volume : 1.0;
        const pitch = options.pitch !== undefined ? options.pitch : 1.0;
        const delay = options.delay !== undefined ? options.delay : 0;

        gainNode.gain.value = volume;
        source.playbackRate.value = pitch;

        // 播放音效
        const startTime = this.audioContext.currentTime + delay;
        source.start(startTime);

        // 自动清理
        source.onended = () => {
            source.disconnect();
            gainNode.disconnect();
        };

        return source;
    }

    /**
     * 播放背景音乐
     */
    playMusic(name, options = {}) {
        if (!this.audioContext || !this.audioBuffers.has(name)) {
            return null;
        }

        // 停止当前音乐
        this.stopMusic();

        const buffer = this.audioBuffers.get(name);
        if (!buffer) return null;

        const source = this.audioContext.createBufferSource();
        const gainNode = this.audioContext.createGain();

        source.buffer = buffer;
        source.loop = options.loop !== undefined ? options.loop : true;
        source.connect(gainNode);
        gainNode.connect(this.musicGain);

        // 设置音乐参数
        const volume = options.volume !== undefined ? options.volume : 1.0;
        const fadeIn = options.fadeIn !== undefined ? options.fadeIn : 0;

        if (fadeIn > 0) {
            gainNode.gain.value = 0;
            gainNode.gain.linearRampToValueAtTime(volume, this.audioContext.currentTime + fadeIn);
        } else {
            gainNode.gain.value = volume;
        }

        source.start();

        this.currentMusic = {
            source: source,
            gainNode: gainNode,
            name: name
        };

        return source;
    }

    /**
     * 停止背景音乐
     */
    stopMusic(fadeOut = 0) {
        if (!this.currentMusic) return;

        const { source, gainNode } = this.currentMusic;

        if (fadeOut > 0) {
            gainNode.gain.linearRampToValueAtTime(0, this.audioContext.currentTime + fadeOut);
            setTimeout(() => {
                source.stop();
                source.disconnect();
                gainNode.disconnect();
            }, fadeOut * 1000);
        } else {
            source.stop();
            source.disconnect();
            gainNode.disconnect();
        }

        this.currentMusic = null;
    }

    /**
     * 暂停背景音乐
     */
    pauseMusic() {
        if (this.currentMusic) {
            this.currentMusic.gainNode.gain.value = 0;
        }
    }

    /**
     * 恢复背景音乐
     */
    resumeMusic() {
        if (this.currentMusic) {
            this.currentMusic.gainNode.gain.value = this.musicVolume;
        }
    }

    /**
     * 设置主音量
     */
    setMasterVolume(volume) {
        this.masterVolume = Math.max(0, Math.min(1, volume));
        this.updateVolumes();
    }

    /**
     * 设置音效音量
     */
    setSFXVolume(volume) {
        this.sfxVolume = Math.max(0, Math.min(1, volume));
        this.updateVolumes();
    }

    /**
     * 设置音乐音量
     */
    setMusicVolume(volume) {
        this.musicVolume = Math.max(0, Math.min(1, volume));
        this.updateVolumes();
    }

    /**
     * 更新所有音量
     */
    updateVolumes() {
        if (this.masterGain) {
            this.masterGain.gain.value = this.masterVolume;
        }
        if (this.sfxGain) {
            this.sfxGain.gain.value = this.sfxVolume;
        }
        if (this.musicGain) {
            this.musicGain.gain.value = this.musicVolume;
        }
    }

    /**
     * 创建合成音效（用于没有音频文件时的替代）
     */
    createSynthSound(frequency, duration, type = 'sine', volume = 0.3) {
        if (!this.audioContext) return null;

        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.type = type;
        oscillator.frequency.value = frequency;

        gainNode.gain.value = volume;
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + duration);

        oscillator.connect(gainNode);
        gainNode.connect(this.sfxGain);

        oscillator.start();
        oscillator.stop(this.audioContext.currentTime + duration);

        return oscillator;
    }

    /**
     * 播放合成音效的便捷方法
     */
    playBeep(frequency = 440, duration = 0.2) {
        return this.createSynthSound(frequency, duration);
    }

    /**
     * 获取音频状态
     */
    getStatus() {
        return {
            contextState: this.audioContext ? this.audioContext.state : 'unavailable',
            masterVolume: this.masterVolume,
            sfxVolume: this.sfxVolume,
            musicVolume: this.musicVolume,
            currentMusic: this.currentMusic ? this.currentMusic.name : null,
            loadedSounds: Array.from(this.audioBuffers.keys())
        };
    }
}