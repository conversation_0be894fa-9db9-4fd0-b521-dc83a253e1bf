/**
 * 输入管理器
 * 处理鼠标、触摸和键盘输入
 */

export class InputManager {
    constructor(canvas) {
        this.canvas = canvas;

        // 鼠标状态
        this.mousePos = { x: 0, y: 0 };
        this.isMouseDown = false;
        this.mouseButton = -1;

        // 触摸状态
        this.touches = new Map();
        this.lastTouchTime = 0;
        this.touchThreshold = 300; // 双击检测阈值（毫秒）

        // 键盘状态
        this.keysPressed = new Set();

        // 事件回调
        this.onMouseClick = null;
        this.onMouseMove = null;
        this.onMouseDown = null;
        this.onMouseUp = null;
        this.onTouchStart = null;
        this.onTouchMove = null;
        this.onTouchEnd = null;
        this.onKeyDown = null;
        this.onKeyUp = null;

        this.bindEvents();
        console.log('🎮 输入管理器初始化完成');
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 鼠标事件
        this.canvas.addEventListener('mousedown', (e) => this.handleMouseDown(e));
        this.canvas.addEventListener('mouseup', (e) => this.handleMouseUp(e));
        this.canvas.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        this.canvas.addEventListener('click', (e) => this.handleMouseClick(e));

        // 触摸事件
        this.canvas.addEventListener('touchstart', (e) => this.handleTouchStart(e), { passive: false });
        this.canvas.addEventListener('touchmove', (e) => this.handleTouchMove(e), { passive: false });
        this.canvas.addEventListener('touchend', (e) => this.handleTouchEnd(e), { passive: false });

        // 键盘事件
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        document.addEventListener('keyup', (e) => this.handleKeyUp(e));

        // 防止右键菜单
        this.canvas.addEventListener('contextmenu', (e) => e.preventDefault());

        // 防止双击选择文本
        this.canvas.addEventListener('selectstart', (e) => e.preventDefault());
    }

    /**
     * 获取相对于画布的坐标
     */
    getCanvasCoordinates(clientX, clientY) {
        const rect = this.canvas.getBoundingClientRect();
        const scaleX = this.canvas.width / rect.width;
        const scaleY = this.canvas.height / rect.height;

        return {
            x: (clientX - rect.left) * scaleX,
            y: (clientY - rect.top) * scaleY
        };
    }

    /**
     * 处理鼠标按下
     */
    handleMouseDown(e) {
        e.preventDefault();

        const coords = this.getCanvasCoordinates(e.clientX, e.clientY);
        this.mousePos = coords;
        this.isMouseDown = true;
        this.mouseButton = e.button;

        if (this.onMouseDown) {
            this.onMouseDown(coords.x, coords.y, e.button);
        }
    }

    /**
     * 处理鼠标释放
     */
    handleMouseUp(e) {
        e.preventDefault();

        const coords = this.getCanvasCoordinates(e.clientX, e.clientY);
        this.mousePos = coords;
        this.isMouseDown = false;

        if (this.onMouseUp) {
            this.onMouseUp(coords.x, coords.y, this.mouseButton);
        }

        this.mouseButton = -1;
    }

    /**
     * 处理鼠标移动
     */
    handleMouseMove(e) {
        const coords = this.getCanvasCoordinates(e.clientX, e.clientY);
        this.mousePos = coords;

        if (this.onMouseMove) {
            this.onMouseMove(coords.x, coords.y);
        }
    }

    /**
     * 处理鼠标点击
     */
    handleMouseClick(e) {
        e.preventDefault();

        const coords = this.getCanvasCoordinates(e.clientX, e.clientY);

        if (this.onMouseClick) {
            this.onMouseClick(coords.x, coords.y, e.button);
        }
    }

    /**
     * 处理触摸开始
     */
    handleTouchStart(e) {
        e.preventDefault();

        const currentTime = Date.now();

        for (let i = 0; i < e.changedTouches.length; i++) {
            const touch = e.changedTouches[i];
            const coords = this.getCanvasCoordinates(touch.clientX, touch.clientY);

            this.touches.set(touch.identifier, {
                x: coords.x,
                y: coords.y,
                startTime: currentTime,
                startX: coords.x,
                startY: coords.y
            });

            // 第一个触摸点作为主要输入
            if (i === 0) {
                this.mousePos = coords;

                if (this.onTouchStart) {
                    this.onTouchStart(coords.x, coords.y);
                }

                // 模拟鼠标点击
                if (this.onMouseClick) {
                    this.onMouseClick(coords.x, coords.y, 0);
                }
            }
        }
    }

    /**
     * 处理触摸移动
     */
    handleTouchMove(e) {
        e.preventDefault();

        for (let i = 0; i < e.changedTouches.length; i++) {
            const touch = e.changedTouches[i];
            const coords = this.getCanvasCoordinates(touch.clientX, touch.clientY);

            if (this.touches.has(touch.identifier)) {
                const touchData = this.touches.get(touch.identifier);
                touchData.x = coords.x;
                touchData.y = coords.y;

                // 第一个触摸点作为主要输入
                if (i === 0) {
                    this.mousePos = coords;

                    if (this.onTouchMove) {
                        this.onTouchMove(coords.x, coords.y);
                    }

                    // 模拟鼠标移动
                    if (this.onMouseMove) {
                        this.onMouseMove(coords.x, coords.y);
                    }
                }
            }
        }
    }

    /**
     * 处理触摸结束
     */
    handleTouchEnd(e) {
        e.preventDefault();

        for (let i = 0; i < e.changedTouches.length; i++) {
            const touch = e.changedTouches[i];

            if (this.touches.has(touch.identifier)) {
                const touchData = this.touches.get(touch.identifier);
                const coords = this.getCanvasCoordinates(touch.clientX, touch.clientY);

                if (this.onTouchEnd) {
                    this.onTouchEnd(coords.x, coords.y);
                }

                this.touches.delete(touch.identifier);
            }
        }
    }

    /**
     * 处理键盘按下
     */
    handleKeyDown(e) {
        this.keysPressed.add(e.key.toLowerCase());

        if (this.onKeyDown) {
            this.onKeyDown(e.key, e.code);
        }
    }

    /**
     * 处理键盘释放
     */
    handleKeyUp(e) {
        this.keysPressed.delete(e.key.toLowerCase());

        if (this.onKeyUp) {
            this.onKeyUp(e.key, e.code);
        }
    }

    /**
     * 检查键是否被按下
     */
    isKeyPressed(key) {
        return this.keysPressed.has(key.toLowerCase());
    }

    /**
     * 获取当前鼠标位置
     */
    getMousePosition() {
        return { ...this.mousePos };
    }

    /**
     * 检查鼠标是否按下
     */
    isMousePressed() {
        return this.isMouseDown;
    }

    /**
     * 获取触摸点数量
     */
    getTouchCount() {
        return this.touches.size;
    }

    /**
     * 获取所有触摸点
     */
    getTouches() {
        return Array.from(this.touches.values());
    }

    /**
     * 清理资源
     */
    destroy() {
        // 移除所有事件监听器
        this.canvas.removeEventListener('mousedown', this.handleMouseDown);
        this.canvas.removeEventListener('mouseup', this.handleMouseUp);
        this.canvas.removeEventListener('mousemove', this.handleMouseMove);
        this.canvas.removeEventListener('click', this.handleMouseClick);
        this.canvas.removeEventListener('touchstart', this.handleTouchStart);
        this.canvas.removeEventListener('touchmove', this.handleTouchMove);
        this.canvas.removeEventListener('touchend', this.handleTouchEnd);
        this.canvas.removeEventListener('contextmenu', (e) => e.preventDefault());
        this.canvas.removeEventListener('selectstart', (e) => e.preventDefault());

        document.removeEventListener('keydown', this.handleKeyDown);
        document.removeEventListener('keyup', this.handleKeyUp);

        console.log('🎮 输入管理器已销毁');
    }
}