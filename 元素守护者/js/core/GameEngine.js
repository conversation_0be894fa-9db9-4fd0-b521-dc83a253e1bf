/**
 * 游戏引擎核心类
 * 负责游戏循环、状态管理、实体更新等核心功能
 */

import { TowerManager } from '../systems/TowerManager.js';
import { EnemyManager } from '../systems/EnemyManager.js';
import { ProjectileManager } from '../systems/ProjectileManager.js';
import { TerrainManager } from '../systems/TerrainManager.js';
import { WaveManager } from '../systems/WaveManager.js';
import { SaveManager } from '../systems/SaveManager.js';
import { UIManager } from '../systems/UIManager.js';
import { GameState } from '../utils/GameState.js';
import { Vector2 } from '../utils/Vector2.js';

export class GameEngine {
    constructor(canvas, renderer, inputManager, audioManager) {
        this.canvas = canvas;
        this.renderer = renderer;
        this.inputManager = inputManager;
        this.audioManager = audioManager;

        this.isRunning = false;
        this.isPaused = false;
        this.lastTime = 0;
        this.deltaTime = 0;
        this.fps = 60;
        this.targetFrameTime = 1000 / this.fps;

        // 游戏状态
        this.gameState = new GameState();

        // 游戏系统管理器
        this.saveManager = new SaveManager();
        this.towerManager = new TowerManager(this);
        this.enemyManager = new EnemyManager(this);
        this.projectileManager = new ProjectileManager(this);
        this.terrainManager = new TerrainManager(this);
        this.waveManager = new WaveManager(this);
        this.uiManager = new UIManager(this, this.saveManager, this.audioManager);

        // 游戏网格设置
        this.gridSize = 40; // 每个网格的像素大小
        this.gridWidth = Math.floor(canvas.width / this.gridSize);
        this.gridHeight = Math.floor(canvas.height / this.gridSize);

        // 路径点（敌人移动路径）
        this.pathPoints = this.generatePath();

        // 鼠标状态
        this.mousePos = new Vector2(0, 0);
        this.selectedTower = null;
        this.buildingTower = null;

        console.log('🎮 游戏引擎初始化完成');
        console.log(`📐 游戏网格: ${this.gridWidth}x${this.gridHeight}, 网格大小: ${this.gridSize}px`);
    }

    /**
     * 生成敌人移动路径
     */
    generatePath() {
        const path = [];
        const startX = 0;
        const startY = Math.floor(this.gridHeight / 2);
        const endX = this.gridWidth - 1;
        const endY = Math.floor(this.gridHeight / 2);

        // 简单的直线路径（后续可以改为更复杂的路径）
        for (let x = startX; x <= endX; x++) {
            path.push(new Vector2(x * this.gridSize + this.gridSize / 2, startY * this.gridSize + this.gridSize / 2));
        }

        return path;
    }

    /**
     * 启动游戏循环
     */
    start() {
        if (this.isRunning) return;

        this.isRunning = true;
        this.isPaused = false;
        this.lastTime = performance.now();

        // 初始化游戏状态
        this.gameState.reset();

        // 启动第一波敌人
        this.waveManager.startNextWave();

        // 开始游戏循环
        this.gameLoop();

        console.log('🚀 游戏循环启动');
    }

    /**
     * 游戏主循环
     */
    gameLoop() {
        if (!this.isRunning) return;

        const currentTime = performance.now();
        this.deltaTime = currentTime - this.lastTime;

        // 限制帧率
        if (this.deltaTime >= this.targetFrameTime) {
            if (!this.isPaused) {
                this.update(this.deltaTime / 1000); // 转换为秒
            }
            this.lastTime = currentTime;
        }

        requestAnimationFrame(() => this.gameLoop());
    }

    /**
     * 更新游戏状态
     */
    update(deltaTime) {
        // 更新各个系统
        this.waveManager.update(deltaTime);
        this.enemyManager.update(deltaTime);
        this.towerManager.update(deltaTime);
        this.projectileManager.update(deltaTime);
        this.terrainManager.update(deltaTime);

        // 检查游戏结束条件
        this.checkGameOver();

        // 更新UI显示
        this.uiManager.updateResourceDisplay();
    }

    /**
     * 渲染游戏
     */
    render(renderer) {
        // 渲染地形
        this.terrainManager.render(renderer);

        // 渲染路径
        this.renderPath(renderer);

        // 渲染敌人
        this.enemyManager.render(renderer);

        // 渲染塔
        this.towerManager.render(renderer);

        // 渲染子弹
        this.projectileManager.render(renderer);

        // 渲染网格（调试模式）
        if (this.gameState.debugMode) {
            this.renderGrid(renderer);
        }

        // 渲染建造预览
        if (this.buildingTower) {
            this.renderBuildPreview(renderer);
        }

        // 渲染选中塔的范围
        if (this.selectedTower) {
            this.renderTowerRange(renderer);
        }
    }

    /**
     * 渲染路径
     */
    renderPath(renderer) {
        if (this.pathPoints.length < 2) return;

        renderer.ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
        renderer.ctx.lineWidth = 20;
        renderer.ctx.lineCap = 'round';
        renderer.ctx.lineJoin = 'round';

        renderer.ctx.beginPath();
        renderer.ctx.moveTo(this.pathPoints[0].x, this.pathPoints[0].y);

        for (let i = 1; i < this.pathPoints.length; i++) {
            renderer.ctx.lineTo(this.pathPoints[i].x, this.pathPoints[i].y);
        }

        renderer.ctx.stroke();
    }

    /**
     * 渲染网格
     */
    renderGrid(renderer) {
        renderer.ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
        renderer.ctx.lineWidth = 1;

        // 垂直线
        for (let x = 0; x <= this.gridWidth; x++) {
            const xPos = x * this.gridSize;
            renderer.ctx.beginPath();
            renderer.ctx.moveTo(xPos, 0);
            renderer.ctx.lineTo(xPos, this.canvas.height);
            renderer.ctx.stroke();
        }

        // 水平线
        for (let y = 0; y <= this.gridHeight; y++) {
            const yPos = y * this.gridSize;
            renderer.ctx.beginPath();
            renderer.ctx.moveTo(0, yPos);
            renderer.ctx.lineTo(this.canvas.width, yPos);
            renderer.ctx.stroke();
        }
    }

    /**
     * 渲染建造预览
     */
    renderBuildPreview(renderer) {
        const gridPos = this.screenToGrid(this.mousePos.x, this.mousePos.y);
        const screenPos = this.gridToScreen(gridPos.x, gridPos.y);

        // 检查是否可以建造
        const canBuild = this.canBuildAt(gridPos.x, gridPos.y);

        // 绘制预览圆圈
        renderer.ctx.fillStyle = canBuild ? 'rgba(0, 255, 0, 0.3)' : 'rgba(255, 0, 0, 0.3)';
        renderer.ctx.strokeStyle = canBuild ? 'rgba(0, 255, 0, 0.8)' : 'rgba(255, 0, 0, 0.8)';
        renderer.ctx.lineWidth = 2;

        renderer.ctx.beginPath();
        renderer.ctx.arc(screenPos.x, screenPos.y, this.gridSize / 2 - 2, 0, Math.PI * 2);
        renderer.ctx.fill();
        renderer.ctx.stroke();

        // 绘制塔的图标
        renderer.ctx.fillStyle = 'white';
        renderer.ctx.font = '24px Arial';
        renderer.ctx.textAlign = 'center';
        renderer.ctx.textBaseline = 'middle';

        const towerIcon = this.getTowerIcon(this.buildingTower);
        renderer.ctx.fillText(towerIcon, screenPos.x, screenPos.y);

        // 绘制射程范围
        if (canBuild) {
            const towerData = this.getTowerData(this.buildingTower);
            renderer.ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
            renderer.ctx.lineWidth = 1;
            renderer.ctx.setLineDash([5, 5]);

            renderer.ctx.beginPath();
            renderer.ctx.arc(screenPos.x, screenPos.y, towerData.range, 0, Math.PI * 2);
            renderer.ctx.stroke();
            renderer.ctx.setLineDash([]);
        }
    }

    /**
     * 渲染选中塔的射程
     */
    renderTowerRange(renderer) {
        const tower = this.selectedTower;
        if (!tower) return;

        renderer.ctx.strokeStyle = 'rgba(255, 215, 0, 0.5)';
        renderer.ctx.lineWidth = 2;
        renderer.ctx.setLineDash([3, 3]);

        renderer.ctx.beginPath();
        renderer.ctx.arc(tower.x, tower.y, tower.range, 0, Math.PI * 2);
        renderer.ctx.stroke();
        renderer.ctx.setLineDash([]);
    }

    /**
     * 处理鼠标点击
     */
    handleClick(x, y) {
        this.mousePos.set(x, y);
        const gridPos = this.screenToGrid(x, y);

        if (this.buildingTower) {
            // 建造塔
            this.tryBuildTower(gridPos.x, gridPos.y);
        } else {
            // 选择塔或空地
            const tower = this.towerManager.getTowerAt(gridPos.x, gridPos.y);
            if (tower) {
                this.selectTower(tower);
            } else {
                this.selectedTower = null;
                // 显示建造面板
                this.showBuildPanel();
            }
        }
    }

    /**
     * 处理鼠标移动
     */
    handleMouseMove(x, y) {
        this.mousePos.set(x, y);
    }

    /**
     * 处理触摸开始
     */
    handleTouchStart(x, y) {
        this.handleClick(x, y);
    }

    /**
     * 处理键盘按键
     */
    handleKeyDown(key) {
        switch (key) {
            case 'Escape':
                this.cancelBuilding();
                this.selectedTower = null;
                break;
            case ' ':
                this.togglePause();
                break;
            case 'd':
                this.gameState.debugMode = !this.gameState.debugMode;
                break;
        }
    }

    /**
     * 尝试建造塔
     */
    tryBuildTower(gridX, gridY) {
        if (!this.canBuildAt(gridX, gridY)) {
            console.log('❌ 无法在此位置建造塔');
            return false;
        }

        const towerData = this.getTowerData(this.buildingTower);
        if (this.gameState.gold < towerData.cost) {
            console.log('❌ 金币不足');
            return false;
        }

        // 建造塔
        const screenPos = this.gridToScreen(gridX, gridY);
        const tower = this.towerManager.buildTower(this.buildingTower, screenPos.x, screenPos.y, gridX, gridY);

        if (tower) {
            this.gameState.gold -= towerData.cost;
            this.cancelBuilding();
            console.log(`✅ 建造了${towerData.name}，花费${towerData.cost}金币`);
            return true;
        }

        return false;
    }

    /**
     * 检查是否可以在指定位置建造
     */
    canBuildAt(gridX, gridY) {
        // 检查边界
        if (gridX < 0 || gridX >= this.gridWidth || gridY < 0 || gridY >= this.gridHeight) {
            return false;
        }

        // 检查是否已有塔
        if (this.towerManager.getTowerAt(gridX, gridY)) {
            return false;
        }

        // 检查是否在路径上
        const screenPos = this.gridToScreen(gridX, gridY);
        for (const pathPoint of this.pathPoints) {
            const distance = Vector2.distance(screenPos, pathPoint);
            if (distance < this.gridSize / 2) {
                return false;
            }
        }

        return true;
    }

    /**
     * 屏幕坐标转网格坐标
     */
    screenToGrid(x, y) {
        return new Vector2(
            Math.floor(x / this.gridSize),
            Math.floor(y / this.gridSize)
        );
    }

    /**
     * 网格坐标转屏幕坐标（中心点）
     */
    gridToScreen(gridX, gridY) {
        return new Vector2(
            gridX * this.gridSize + this.gridSize / 2,
            gridY * this.gridSize + this.gridSize / 2
        );
    }

    /**
     * 获取塔的数据
     */
    getTowerData(towerType) {
        const towerData = {
            fire: { name: '火焰塔', cost: 50, damage: 25, range: 80, speed: 1.0, element: 'fire' },
            water: { name: '冰霜塔', cost: 60, damage: 15, range: 90, speed: 0.8, element: 'water' },
            earth: { name: '岩石塔', cost: 80, damage: 40, range: 70, speed: 0.6, element: 'earth' },
            air: { name: '风暴塔', cost: 70, damage: 20, range: 100, speed: 1.2, element: 'air' }
        };

        return towerData[towerType] || towerData.fire;
    }

    /**
     * 获取塔的图标
     */
    getTowerIcon(towerType) {
        const icons = {
            fire: '🔥',
            water: '💧',
            earth: '🗿',
            air: '💨'
        };

        return icons[towerType] || '🔥';
    }

    /**
     * 选择塔
     */
    selectTower(tower) {
        this.selectedTower = tower;
        this.showUpgradePanel(tower);
    }

    /**
     * 开始建造塔
     */
    startBuilding(towerType) {
        this.buildingTower = towerType;
        this.selectedTower = null;
        this.hideBuildPanel();
    }

    /**
     * 取消建造
     */
    cancelBuilding() {
        this.buildingTower = null;
    }

    /**
     * 显示建造面板
     */
    showBuildPanel() {
        const panel = document.getElementById('tower-panel');
        if (panel) {
            panel.classList.add('active');
        }
    }

    /**
     * 隐藏建造面板
     */
    hideBuildPanel() {
        const panel = document.getElementById('tower-panel');
        if (panel) {
            panel.classList.remove('active');
        }
    }

    /**
     * 显示升级面板
     */
    showUpgradePanel(tower) {
        const panel = document.getElementById('upgrade-panel');
        if (panel) {
            panel.style.display = 'block';
            // 更新面板信息
            document.getElementById('tower-damage').textContent = tower.damage;
            document.getElementById('tower-range').textContent = Math.round(tower.range);
            document.getElementById('tower-speed').textContent = tower.attackSpeed.toFixed(1);
        }
    }

    /**
     * 检查游戏结束
     */
    checkGameOver() {
        if (this.gameState.lives <= 0) {
            this.gameOver(false);
        } else if (this.waveManager.isAllWavesComplete() && this.enemyManager.enemies.length === 0) {
            this.gameOver(true);
        }
    }

    /**
     * 游戏结束
     */
    gameOver(victory) {
        this.pause();

        const gameOverScreen = document.getElementById('game-over');
        const title = document.getElementById('game-over-title');

        if (victory) {
            title.textContent = '胜利！';
            title.style.color = '#00b894';
        } else {
            title.textContent = '游戏结束';
            title.style.color = '#ff6b6b';
        }

        // 更新统计信息
        document.getElementById('final-wave').textContent = this.gameState.currentWave;
        document.getElementById('final-kills').textContent = this.gameState.enemiesKilled;
        document.getElementById('final-gold').textContent = this.gameState.totalGoldEarned;
        document.getElementById('final-time').textContent = this.formatTime(this.gameState.gameTime);

        gameOverScreen.style.display = 'flex';

        console.log(victory ? '🎉 游戏胜利！' : '💀 游戏失败！');
    }

    /**
     * 格式化时间
     */
    formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }

    /**
     * 更新UI显示
     */
    updateUI() {
        // 更新资源显示
        const goldElement = document.getElementById('gold-amount');
        const lifeElement = document.getElementById('life-amount');
        const waveElement = document.getElementById('wave-info');

        if (goldElement) goldElement.textContent = this.gameState.gold;
        if (lifeElement) lifeElement.textContent = this.gameState.lives;
        if (waveElement) waveElement.textContent = `第 ${this.gameState.currentWave} 波`;
    }

    /**
     * 暂停游戏
     */
    pause() {
        this.isPaused = true;
        console.log('⏸️ 游戏暂停');
    }

    /**
     * 恢复游戏
     */
    resume() {
        this.isPaused = false;
        console.log('▶️ 游戏恢复');
    }

    /**
     * 切换暂停状态
     */
    togglePause() {
        if (this.isPaused) {
            this.resume();
        } else {
            this.pause();
        }
    }

    /**
     * 重启游戏
     */
    restart() {
        this.gameState.reset();
        this.towerManager.clear();
        this.enemyManager.clear();
        this.projectileManager.clear();
        this.waveManager.reset();

        this.selectedTower = null;
        this.buildingTower = null;

        // 隐藏游戏结束界面
        const gameOverScreen = document.getElementById('game-over');
        if (gameOverScreen) {
            gameOverScreen.style.display = 'none';
        }

        // 重新开始第一波
        this.waveManager.startNextWave();

        console.log('🔄 游戏重启');
    }

    /**
     * 停止游戏
     */
    stop() {
        this.isRunning = false;
        console.log('🛑 游戏停止');
    }
}