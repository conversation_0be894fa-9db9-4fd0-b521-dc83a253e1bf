/**
 * 渲染器类
 * 负责所有的绘制操作和视觉效果
 */

export class Renderer {
    constructor(ctx, width, height) {
        this.ctx = ctx;
        this.width = width;
        this.height = height;

        // 渲染设置
        this.pixelRatio = window.devicePixelRatio || 1;

        // 粒子效果数组
        this.particles = [];

        console.log('🎨 渲染器初始化完成');
    }

    /**
     * 清空画布
     */
    clear() {
        this.ctx.clearRect(0, 0, this.width, this.height);

        // 绘制背景渐变
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.height);
        gradient.addColorStop(0, '#2c3e50');
        gradient.addColorStop(1, '#34495e');

        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.width, this.height);
    }

    /**
     * 绘制圆形
     */
    drawCircle(x, y, radius, fillStyle, strokeStyle = null, lineWidth = 1) {
        this.ctx.beginPath();
        this.ctx.arc(x, y, radius, 0, Math.PI * 2);

        if (fillStyle) {
            this.ctx.fillStyle = fillStyle;
            this.ctx.fill();
        }

        if (strokeStyle) {
            this.ctx.strokeStyle = strokeStyle;
            this.ctx.lineWidth = lineWidth;
            this.ctx.stroke();
        }
    }

    /**
     * 绘制矩形
     */
    drawRect(x, y, width, height, fillStyle, strokeStyle = null, lineWidth = 1) {
        if (fillStyle) {
            this.ctx.fillStyle = fillStyle;
            this.ctx.fillRect(x, y, width, height);
        }

        if (strokeStyle) {
            this.ctx.strokeStyle = strokeStyle;
            this.ctx.lineWidth = lineWidth;
            this.ctx.strokeRect(x, y, width, height);
        }
    }

    /**
     * 绘制文本
     */
    drawText(text, x, y, font = '16px Arial', fillStyle = 'white', textAlign = 'center', textBaseline = 'middle') {
        this.ctx.font = font;
        this.ctx.fillStyle = fillStyle;
        this.ctx.textAlign = textAlign;
        this.ctx.textBaseline = textBaseline;
        this.ctx.fillText(text, x, y);
    }

    /**
     * 绘制带描边的文本
     */
    drawStrokedText(text, x, y, font = '16px Arial', fillStyle = 'white', strokeStyle = 'black', lineWidth = 2, textAlign = 'center', textBaseline = 'middle') {
        this.ctx.font = font;
        this.ctx.textAlign = textAlign;
        this.ctx.textBaseline = textBaseline;

        // 绘制描边
        this.ctx.strokeStyle = strokeStyle;
        this.ctx.lineWidth = lineWidth;
        this.ctx.strokeText(text, x, y);

        // 绘制填充
        this.ctx.fillStyle = fillStyle;
        this.ctx.fillText(text, x, y);
    }

    /**
     * 绘制线条
     */
    drawLine(x1, y1, x2, y2, strokeStyle = 'white', lineWidth = 1, lineDash = []) {
        this.ctx.beginPath();
        this.ctx.moveTo(x1, y1);
        this.ctx.lineTo(x2, y2);
        this.ctx.strokeStyle = strokeStyle;
        this.ctx.lineWidth = lineWidth;
        this.ctx.setLineDash(lineDash);
        this.ctx.stroke();
        this.ctx.setLineDash([]);
    }

    /**
     * 绘制路径
     */
    drawPath(points, strokeStyle = 'white', lineWidth = 2, closed = false) {
        if (points.length < 2) return;

        this.ctx.beginPath();
        this.ctx.moveTo(points[0].x, points[0].y);

        for (let i = 1; i < points.length; i++) {
            this.ctx.lineTo(points[i].x, points[i].y);
        }

        if (closed) {
            this.ctx.closePath();
        }

        this.ctx.strokeStyle = strokeStyle;
        this.ctx.lineWidth = lineWidth;
        this.ctx.stroke();
    }

    /**
     * 绘制渐变圆形
     */
    drawGradientCircle(x, y, radius, innerColor, outerColor) {
        const gradient = this.ctx.createRadialGradient(x, y, 0, x, y, radius);
        gradient.addColorStop(0, innerColor);
        gradient.addColorStop(1, outerColor);

        this.drawCircle(x, y, radius, gradient);
    }

    /**
     * 绘制发光效果
     */
    drawGlow(x, y, radius, color, intensity = 0.5) {
        const oldGlobalAlpha = this.ctx.globalAlpha;

        this.ctx.globalAlpha = intensity;
        this.ctx.shadowColor = color;
        this.ctx.shadowBlur = radius;

        this.drawCircle(x, y, radius / 4, color);

        this.ctx.shadowBlur = 0;
        this.ctx.globalAlpha = oldGlobalAlpha;
    }

    /**
     * 绘制健康条
     */
    drawHealthBar(x, y, width, height, currentHealth, maxHealth, backgroundColor = 'rgba(255, 0, 0, 0.3)', foregroundColor = 'rgba(0, 255, 0, 0.8)') {
        const healthPercent = Math.max(0, currentHealth / maxHealth);

        // 背景
        this.drawRect(x - width / 2, y - height / 2, width, height, backgroundColor);

        // 前景
        this.drawRect(x - width / 2, y - height / 2, width * healthPercent, height, foregroundColor);

        // 边框
        this.drawRect(x - width / 2, y - height / 2, width, height, null, 'rgba(255, 255, 255, 0.5)', 1);
    }

    /**
     * 创建粒子效果
     */
    createParticles(x, y, count, options = {}) {
        const defaults = {
            color: 'white',
            size: 2,
            speed: 50,
            life: 1,
            gravity: 0,
            spread: Math.PI * 2
        };

        const config = { ...defaults, ...options };

        for (let i = 0; i < count; i++) {
            const angle = Math.random() * config.spread - config.spread / 2;
            const speed = config.speed * (0.5 + Math.random() * 0.5);

            this.particles.push({
                x: x,
                y: y,
                vx: Math.cos(angle) * speed,
                vy: Math.sin(angle) * speed,
                size: config.size * (0.5 + Math.random() * 0.5),
                color: config.color,
                life: config.life,
                maxLife: config.life,
                gravity: config.gravity
            });
        }
    }

    /**
     * 更新和渲染粒子
     */
    updateParticles(deltaTime) {
        for (let i = this.particles.length - 1; i >= 0; i--) {
            const particle = this.particles[i];

            // 更新位置
            particle.x += particle.vx * deltaTime;
            particle.y += particle.vy * deltaTime;
            particle.vy += particle.gravity * deltaTime;

            // 更新生命
            particle.life -= deltaTime;

            // 移除死亡的粒子
            if (particle.life <= 0) {
                this.particles.splice(i, 1);
                continue;
            }

            // 渲染粒子
            const alpha = particle.life / particle.maxLife;
            const oldGlobalAlpha = this.ctx.globalAlpha;
            this.ctx.globalAlpha = alpha;

            this.drawCircle(particle.x, particle.y, particle.size, particle.color);

            this.ctx.globalAlpha = oldGlobalAlpha;
        }
    }

    /**
     * 保存画布状态
     */
    save() {
        this.ctx.save();
    }

    /**
     * 恢复画布状态
     */
    restore() {
        this.ctx.restore();
    }

    /**
     * 设置变换
     */
    setTransform(a, b, c, d, e, f) {
        this.ctx.setTransform(a, b, c, d, e, f);
    }

    /**
     * 平移
     */
    translate(x, y) {
        this.ctx.translate(x, y);
    }

    /**
     * 旋转
     */
    rotate(angle) {
        this.ctx.rotate(angle);
    }

    /**
     * 缩放
     */
    scale(x, y) {
        this.ctx.scale(x, y);
    }
}