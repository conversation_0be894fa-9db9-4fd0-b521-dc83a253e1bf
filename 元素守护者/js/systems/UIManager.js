/**
 * UI管理系统
 * 处理用户界面交互和显示更新
 */

export class UIManager {
    constructor(gameEngine, saveManager, audioManager) {
        this.gameEngine = gameEngine;
        this.saveManager = saveManager;
        this.audioManager = audioManager;

        // UI元素引用
        this.elements = {};
        this.initializeElements();
        this.bindEvents();

        console.log('🎨 UI管理系统初始化完成');
    }

    /**
     * 初始化UI元素引用
     */
    initializeElements() {
        // 游戏控制
        this.elements.pauseBtn = document.getElementById('pause-btn');
        this.elements.restartBtn = document.getElementById('restart-btn');
        this.elements.menuBtn = document.getElementById('menu-btn');

        // 资源显示
        this.elements.goldAmount = document.getElementById('gold-amount');
        this.elements.lifeAmount = document.getElementById('life-amount');
        this.elements.waveInfo = document.getElementById('wave-info');

        // 塔建造面板
        this.elements.towerPanel = document.getElementById('tower-panel');
        this.elements.fireTower = document.getElementById('fire-tower');
        this.elements.waterTower = document.getElementById('water-tower');
        this.elements.earthTower = document.getElementById('earth-tower');
        this.elements.airTower = document.getElementById('air-tower');

        // 升级面板
        this.elements.upgradePanel = document.getElementById('upgrade-panel');
        this.elements.upgradePanelClose = document.getElementById('upgrade-panel-close');
        this.elements.upgradeBtn = document.getElementById('upgrade-btn');
        this.elements.sellBtn = document.getElementById('sell-btn');

        // 设置面板
        this.elements.settingsPanel = document.getElementById('settings-panel');
        this.elements.settingsBtn = document.getElementById('settings-btn');
        this.elements.settingsClose = document.getElementById('settings-close');

        // 游戏结束界面
        this.elements.gameOverScreen = document.getElementById('game-over');
        this.elements.gameOverRestart = document.getElementById('game-over-restart');
        this.elements.gameOverMenu = document.getElementById('game-over-menu');
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 游戏控制按钮
        if (this.elements.pauseBtn) {
            this.elements.pauseBtn.addEventListener('click', () => this.togglePause());
        }

        if (this.elements.restartBtn) {
            this.elements.restartBtn.addEventListener('click', () => this.restartGame());
        }

        if (this.elements.menuBtn) {
            this.elements.menuBtn.addEventListener('click', () => this.showMenu());
        }

        // 塔建造按钮
        if (this.elements.fireTower) {
            this.elements.fireTower.addEventListener('click', () => this.selectTowerType('fire'));
        }

        if (this.elements.waterTower) {
            this.elements.waterTower.addEventListener('click', () => this.selectTowerType('water'));
        }

        if (this.elements.earthTower) {
            this.elements.earthTower.addEventListener('click', () => this.selectTowerType('earth'));
        }

        if (this.elements.airTower) {
            this.elements.airTower.addEventListener('click', () => this.selectTowerType('air'));
        }

        // 升级面板
        if (this.elements.upgradePanelClose) {
            this.elements.upgradePanelClose.addEventListener('click', () => this.hideUpgradePanel());
        }

        if (this.elements.upgradeBtn) {
            this.elements.upgradeBtn.addEventListener('click', () => this.upgradeTower());
        }

        if (this.elements.sellBtn) {
            this.elements.sellBtn.addEventListener('click', () => this.sellTower());
        }

        // 设置面板
        if (this.elements.settingsBtn) {
            this.elements.settingsBtn.addEventListener('click', () => this.showSettings());
        }

        if (this.elements.settingsClose) {
            this.elements.settingsClose.addEventListener('click', () => this.hideSettings());
        }

        // 游戏结束界面
        if (this.elements.gameOverRestart) {
            this.elements.gameOverRestart.addEventListener('click', () => this.restartFromGameOver());
        }

        if (this.elements.gameOverMenu) {
            this.elements.gameOverMenu.addEventListener('click', () => this.backToMenu());
        }
    }

    /**
     * 更新资源显示
     */
    updateResourceDisplay() {
        const gameState = this.gameEngine.gameState;

        if (this.elements.goldAmount) {
            this.elements.goldAmount.textContent = gameState.gold;
        }

        if (this.elements.lifeAmount) {
            this.elements.lifeAmount.textContent = gameState.lives;
        }

        if (this.elements.waveInfo) {
            this.elements.waveInfo.textContent = `第 ${gameState.currentWave} 波`;
        }
    }

    /**
     * 切换暂停状态
     */
    togglePause() {
        if (this.gameEngine.isPaused) {
            this.gameEngine.resume();
            if (this.elements.pauseBtn) {
                this.elements.pauseBtn.textContent = '⏸️';
            }
        } else {
            this.gameEngine.pause();
            if (this.elements.pauseBtn) {
                this.elements.pauseBtn.textContent = '▶️';
            }
        }
    }

    /**
     * 重启游戏
     */
    restartGame() {
        if (confirm('确定要重新开始游戏吗？当前进度将会丢失。')) {
            this.gameEngine.restart();
        }
    }

    /**
     * 显示菜单
     */
    showMenu() {
        // 暂停游戏
        this.gameEngine.pause();

        // 显示菜单界面
        // 这里可以添加菜单显示逻辑
        console.log('📋 显示游戏菜单');
    }

    /**
     * 选择塔类型
     */
    selectTowerType(type) {
        const towerData = this.gameEngine.towerManager.towers[0]?.getTowerStats(type) ||
                         { cost: 50, name: '塔' };

        if (this.gameEngine.gameState.gold < towerData.cost) {
            this.showMessage(`金币不足！需要 ${towerData.cost} 金币`, 'error');
            return;
        }

        this.gameEngine.startBuilding(type);
        this.hideTowerPanel();

        console.log(`🏗️ 选择建造${type}塔`);
    }

    /**
     * 显示塔建造面板
     */
    showTowerPanel() {
        if (this.elements.towerPanel) {
            this.elements.towerPanel.classList.add('active');
        }
    }

    /**
     * 隐藏塔建造面板
     */
    hideTowerPanel() {
        if (this.elements.towerPanel) {
            this.elements.towerPanel.classList.remove('active');
        }
    }

    /**
     * 显示升级面板
     */
    showUpgradePanel(tower) {
        if (!this.elements.upgradePanel || !tower) return;

        this.elements.upgradePanel.style.display = 'block';

        // 更新塔信息显示
        const damageElement = document.getElementById('tower-damage');
        const rangeElement = document.getElementById('tower-range');
        const speedElement = document.getElementById('tower-speed');
        const levelElement = document.getElementById('tower-level');
        const upgradeCostElement = document.getElementById('upgrade-cost');
        const sellPriceElement = document.getElementById('sell-price');

        if (damageElement) damageElement.textContent = tower.damage;
        if (rangeElement) rangeElement.textContent = Math.round(tower.range);
        if (speedElement) speedElement.textContent = tower.attackSpeed.toFixed(1);
        if (levelElement) levelElement.textContent = tower.level;
        if (upgradeCostElement) upgradeCostElement.textContent = tower.getUpgradeCost();
        if (sellPriceElement) sellPriceElement.textContent = Math.floor(tower.cost * 0.6);

        // 检查是否可以升级
        const canUpgrade = tower.level < 5 &&
                          this.gameEngine.gameState.gold >= tower.getUpgradeCost();

        if (this.elements.upgradeBtn) {
            this.elements.upgradeBtn.disabled = !canUpgrade;
        }
    }

    /**
     * 隐藏升级面板
     */
    hideUpgradePanel() {
        if (this.elements.upgradePanel) {
            this.elements.upgradePanel.style.display = 'none';
        }

        // 取消选中的塔
        this.gameEngine.selectedTower = null;
    }

    /**
     * 升级塔
     */
    upgradeTower() {
        const tower = this.gameEngine.selectedTower;
        if (!tower) return;

        const success = this.gameEngine.towerManager.upgradeTower(tower);
        if (success) {
            this.showMessage('塔升级成功！', 'success');
            this.showUpgradePanel(tower); // 刷新面板信息
        } else {
            this.showMessage('金币不足或塔已达最高等级', 'error');
        }
    }

    /**
     * 出售塔
     */
    sellTower() {
        const tower = this.gameEngine.selectedTower;
        if (!tower) return;

        if (confirm('确定要出售这座塔吗？')) {
            this.gameEngine.towerManager.sellTower(tower);
            this.hideUpgradePanel();
            this.showMessage('塔已出售', 'info');
        }
    }

    /**
     * 显示设置面板
     */
    showSettings() {
        if (this.elements.settingsPanel) {
            this.elements.settingsPanel.style.display = 'block';
        }

        // 暂停游戏
        this.gameEngine.pause();
    }

    /**
     * 隐藏设置面板
     */
    hideSettings() {
        if (this.elements.settingsPanel) {
            this.elements.settingsPanel.style.display = 'none';
        }

        // 恢复游戏
        this.gameEngine.resume();
    }

    /**
     * 从游戏结束界面重启
     */
    restartFromGameOver() {
        this.gameEngine.restart();

        if (this.elements.gameOverScreen) {
            this.elements.gameOverScreen.style.display = 'none';
        }
    }

    /**
     * 返回主菜单
     */
    backToMenu() {
        // 这里可以添加返回主菜单的逻辑
        console.log('🏠 返回主菜单');
    }

    /**
     * 显示消息提示
     */
    showMessage(message, type = 'info') {
        // 创建消息元素
        const messageElement = document.createElement('div');
        messageElement.className = `game-message ${type}`;
        messageElement.textContent = message;

        // 添加到页面
        document.body.appendChild(messageElement);

        // 自动移除
        setTimeout(() => {
            if (messageElement.parentNode) {
                messageElement.parentNode.removeChild(messageElement);
            }
        }, 3000);

        console.log(`💬 ${type.toUpperCase()}: ${message}`);
    }
}