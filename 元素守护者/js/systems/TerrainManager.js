/**
 * 地形管理系统
 * 处理动态地形变化和环境效果
 */

export class TerrainManager {
    constructor(gameEngine) {
        this.gameEngine = gameEngine;

        // 地形网格
        this.terrainGrid = [];
        this.gridWidth = gameEngine.gridWidth;
        this.gridHeight = gameEngine.gridHeight;
        this.gridSize = gameEngine.gridSize;

        // 地形类型
        this.terrainTypes = {
            normal: { color: '#2d3436', modifier: 1.0 },
            burned: { color: '#d63031', modifier: 1.2 },
            frozen: { color: '#74b9ff', modifier: 0.8 },
            muddy: { color: '#6c5ce7', modifier: 0.6 },
            windy: { color: '#00cec9', modifier: 1.1 }
        };

        // 天气效果
        this.weatherEffect = 'none';
        this.weatherTimer = 0;
        this.weatherDuration = 30; // 天气持续时间（秒）

        // 地形事件
        this.terrainEvents = [];

        this.initializeTerrain();
        console.log('🌍 地形管理系统初始化完成');
    }

    /**
     * 初始化地形网格
     */
    initializeTerrain() {
        this.terrainGrid = [];

        for (let y = 0; y < this.gridHeight; y++) {
            const row = [];
            for (let x = 0; x < this.gridWidth; x++) {
                row.push({
                    type: 'normal',
                    modifier: 1.0,
                    effectTimer: 0,
                    particles: []
                });
            }
            this.terrainGrid.push(row);
        }
    }

    /**
     * 更新地形状态
     */
    update(deltaTime) {
        // 更新天气效果
        this.updateWeather(deltaTime);

        // 更新地形网格
        this.updateTerrainGrid(deltaTime);

        // 更新地形事件
        this.updateTerrainEvents(deltaTime);

        // 随机触发天气变化
        if (Math.random() < 0.001) { // 0.1% 概率每帧
            this.triggerRandomWeather();
        }
    }

    /**
     * 更新天气效果
     */
    updateWeather(deltaTime) {
        if (this.weatherEffect !== 'none') {
            this.weatherTimer -= deltaTime;

            if (this.weatherTimer <= 0) {
                this.weatherEffect = 'none';
                console.log('🌤️ 天气效果结束');
            }
        }
    }

    /**
     * 更新地形网格
     */
    updateTerrainGrid(deltaTime) {
        for (let y = 0; y < this.gridHeight; y++) {
            for (let x = 0; x < this.gridWidth; x++) {
                const tile = this.terrainGrid[y][x];

                // 更新地形效果计时器
                if (tile.effectTimer > 0) {
                    tile.effectTimer -= deltaTime;

                    // 效果结束，恢复正常地形
                    if (tile.effectTimer <= 0) {
                        tile.type = 'normal';
                        tile.modifier = 1.0;
                    }
                }

                // 更新粒子效果
                this.updateTileParticles(tile, deltaTime);
            }
        }
    }

    /**
     * 更新地形事件
     */
    updateTerrainEvents(deltaTime) {
        for (let i = this.terrainEvents.length - 1; i >= 0; i--) {
            const event = this.terrainEvents[i];
            event.timer -= deltaTime;

            if (event.timer <= 0) {
                this.executeTerrainEvent(event);
                this.terrainEvents.splice(i, 1);
            }
        }
    }

    /**
     * 更新地块粒子效果
     */
    updateTileParticles(tile, deltaTime) {
        // 根据地形类型添加粒子效果
        switch (tile.type) {
            case 'burned':
                if (Math.random() < 0.1) {
                    tile.particles.push({
                        type: 'smoke',
                        life: 2,
                        x: Math.random() * this.gridSize,
                        y: Math.random() * this.gridSize
                    });
                }
                break;

            case 'frozen':
                if (Math.random() < 0.05) {
                    tile.particles.push({
                        type: 'ice',
                        life: 3,
                        x: Math.random() * this.gridSize,
                        y: Math.random() * this.gridSize
                    });
                }
                break;
        }

        // 更新现有粒子
        tile.particles = tile.particles.filter(particle => {
            particle.life -= deltaTime;
            return particle.life > 0;
        });
    }

    /**
     * 应用元素效果到地形
     */
    applyElementEffect(x, y, element, intensity = 1.0) {
        const gridX = Math.floor(x / this.gridSize);
        const gridY = Math.floor(y / this.gridSize);

        if (gridX < 0 || gridX >= this.gridWidth || gridY < 0 || gridY >= this.gridHeight) {
            return;
        }

        const tile = this.terrainGrid[gridY][gridX];
        const duration = 10 * intensity; // 效果持续时间

        switch (element) {
            case 'fire':
                tile.type = 'burned';
                tile.modifier = 1.2; // 火焰伤害加成
                tile.effectTimer = duration;
                break;

            case 'water':
                tile.type = 'frozen';
                tile.modifier = 0.8; // 减速效果
                tile.effectTimer = duration;
                break;

            case 'earth':
                tile.type = 'muddy';
                tile.modifier = 0.6; // 强减速效果
                tile.effectTimer = duration;
                break;

            case 'air':
                tile.type = 'windy';
                tile.modifier = 1.1; // 轻微加速效果
                tile.effectTimer = duration;
                break;
        }

        console.log(`🌍 在(${gridX}, ${gridY})应用${element}地形效果`);
    }

    /**
     * 获取地形修正值
     */
    getTerrainModifier(x, y) {
        const gridX = Math.floor(x / this.gridSize);
        const gridY = Math.floor(y / this.gridSize);

        if (gridX < 0 || gridX >= this.gridWidth || gridY < 0 || gridY >= this.gridHeight) {
            return 1.0;
        }

        const tile = this.terrainGrid[gridY][gridX];
        let modifier = tile.modifier;

        // 应用天气效果
        switch (this.weatherEffect) {
            case 'rain':
                modifier *= 0.9; // 雨天减速
                break;
            case 'storm':
                modifier *= 1.1; // 风暴加速
                break;
            case 'fog':
                modifier *= 0.8; // 雾天大幅减速
                break;
        }

        return modifier;
    }

    /**
     * 触发随机天气
     */
    triggerRandomWeather() {
        const weatherTypes = ['rain', 'storm', 'fog'];
        const randomWeather = weatherTypes[Math.floor(Math.random() * weatherTypes.length)];

        this.weatherEffect = randomWeather;
        this.weatherTimer = this.weatherDuration;

        console.log(`🌦️ 天气变化: ${randomWeather}`);
    }

    /**
     * 添加地形事件
     */
    addTerrainEvent(type, x, y, delay = 0) {
        this.terrainEvents.push({
            type: type,
            x: x,
            y: y,
            timer: delay
        });
    }

    /**
     * 执行地形事件
     */
    executeTerrainEvent(event) {
        switch (event.type) {
            case 'earthquake':
                this.createEarthquake(event.x, event.y);
                break;
            case 'volcano':
                this.createVolcano(event.x, event.y);
                break;
            case 'flood':
                this.createFlood(event.x, event.y);
                break;
        }
    }

    /**
     * 创建地震效果
     */
    createEarthquake(centerX, centerY) {
        const radius = 3;

        for (let dy = -radius; dy <= radius; dy++) {
            for (let dx = -radius; dx <= radius; dx++) {
                const distance = Math.sqrt(dx * dx + dy * dy);
                if (distance <= radius) {
                    this.applyElementEffect(
                        (centerX + dx) * this.gridSize,
                        (centerY + dy) * this.gridSize,
                        'earth',
                        1.0 - distance / radius
                    );
                }
            }
        }

        console.log('🌋 地震发生！');
    }

    /**
     * 渲染地形
     */
    render(renderer) {
        // 渲染地形网格
        for (let y = 0; y < this.gridHeight; y++) {
            for (let x = 0; x < this.gridWidth; x++) {
                const tile = this.terrainGrid[y][x];

                if (tile.type !== 'normal') {
                    const screenX = x * this.gridSize;
                    const screenY = y * this.gridSize;

                    // 绘制地形效果
                    const terrainType = this.terrainTypes[tile.type];
                    const alpha = Math.min(0.3, tile.effectTimer / 10);

                    renderer.ctx.fillStyle = terrainType.color;
                    renderer.ctx.globalAlpha = alpha;
                    renderer.ctx.fillRect(screenX, screenY, this.gridSize, this.gridSize);
                    renderer.ctx.globalAlpha = 1.0;

                    // 渲染粒子效果
                    this.renderTileParticles(renderer, tile, screenX, screenY);
                }
            }
        }

        // 渲染天气效果
        this.renderWeatherEffect(renderer);
    }

    /**
     * 渲染地块粒子效果
     */
    renderTileParticles(renderer, tile, screenX, screenY) {
        for (const particle of tile.particles) {
            const alpha = particle.life / 3;
            renderer.ctx.globalAlpha = alpha;

            switch (particle.type) {
                case 'smoke':
                    renderer.drawCircle(
                        screenX + particle.x,
                        screenY + particle.y,
                        3, '#636e72'
                    );
                    break;

                case 'ice':
                    renderer.drawCircle(
                        screenX + particle.x,
                        screenY + particle.y,
                        2, '#74b9ff'
                    );
                    break;
            }
        }

        renderer.ctx.globalAlpha = 1.0;
    }

    /**
     * 渲染天气效果
     */
    renderWeatherEffect(renderer) {
        if (this.weatherEffect === 'none') return;

        const canvas = renderer.ctx.canvas;

        switch (this.weatherEffect) {
            case 'rain':
                this.renderRain(renderer, canvas);
                break;
            case 'fog':
                this.renderFog(renderer, canvas);
                break;
            case 'storm':
                this.renderStorm(renderer, canvas);
                break;
        }
    }

    /**
     * 渲染雨效果
     */
    renderRain(renderer, canvas) {
        renderer.ctx.strokeStyle = 'rgba(174, 182, 191, 0.6)';
        renderer.ctx.lineWidth = 1;

        for (let i = 0; i < 50; i++) {
            const x = Math.random() * canvas.width;
            const y = Math.random() * canvas.height;

            renderer.drawLine(x, y, x + 2, y + 10);
        }
    }

    /**
     * 渲染雾效果
     */
    renderFog(renderer, canvas) {
        renderer.ctx.fillStyle = 'rgba(220, 221, 225, 0.1)';
        renderer.ctx.fillRect(0, 0, canvas.width, canvas.height);
    }

    /**
     * 清空地形效果
     */
    clear() {
        this.initializeTerrain();
        this.weatherEffect = 'none';
        this.weatherTimer = 0;
        this.terrainEvents = [];

        console.log('🧹 地形效果已清空');
    }
}