/**
 * 子弹管理系统
 * 处理塔发射的子弹和特效
 */

import { Vector2 } from '../utils/Vector2.js';

// 子弹类
class Projectile {
    constructor(data) {
        this.element = data.element;
        this.damage = data.damage;
        this.x = data.startX;
        this.y = data.startY;
        this.targetX = data.targetX;
        this.targetY = data.targetY;
        this.target = data.target;
        this.tower = data.tower;
        this.speed = data.speed || 300;
        this.effects = data.effects || [];

        // 计算方向
        const dx = this.targetX - this.x;
        const dy = this.targetY - this.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        this.velocityX = (dx / distance) * this.speed;
        this.velocityY = (dy / distance) * this.speed;

        // 视觉属性
        this.size = 6;
        this.color = this.getElementColor();
        this.trail = [];
        this.maxTrailLength = 8;

        // 状态
        this.isActive = true;
        this.hasHit = false;
        this.lifeTime = 0;
        this.maxLifeTime = 5; // 最大存在时间（秒）

        console.log(`🎯 创建${this.element}元素子弹`);
    }

    /**
     * 获取元素颜色
     */
    getElementColor() {
        const colors = {
            fire: '#ff6b35',
            water: '#74b9ff',
            earth: '#6c5ce7',
            air: '#00cec9'
        };
        return colors[this.element] || '#ffffff';
    }

    /**
     * 更新子弹状态
     */
    update(deltaTime) {
        if (!this.isActive) return;

        this.lifeTime += deltaTime;

        // 检查生命时间
        if (this.lifeTime > this.maxLifeTime) {
            this.isActive = false;
            return;
        }

        // 更新轨迹
        this.trail.push({ x: this.x, y: this.y });
        if (this.trail.length > this.maxTrailLength) {
            this.trail.shift();
        }

        // 如果目标还存在，更新目标位置（追踪子弹）
        if (this.target && this.target.isAlive) {
            this.targetX = this.target.x;
            this.targetY = this.target.y;

            // 重新计算方向
            const dx = this.targetX - this.x;
            const dy = this.targetY - this.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance > 0) {
                this.velocityX = (dx / distance) * this.speed;
                this.velocityY = (dy / distance) * this.speed;
            }
        }

        // 更新位置
        this.x += this.velocityX * deltaTime;
        this.y += this.velocityY * deltaTime;

        // 检查是否到达目标
        const distanceToTarget = Math.sqrt(
            Math.pow(this.targetX - this.x, 2) +
            Math.pow(this.targetY - this.y, 2)
        );

        if (distanceToTarget < this.size) {
            this.hit();
        }
    }

    /**
     * 子弹命中目标
     */
    hit() {
        if (this.hasHit) return;

        this.hasHit = true;
        this.isActive = false;

        // 对目标造成伤害
        if (this.target && this.target.isAlive) {
            this.target.takeDamage(this.damage, this.element);

            // 应用特殊效果
            for (const effect of this.effects) {
                this.target.applyEffect(effect);
            }

            console.log(`💥 ${this.element}子弹命中，造成${this.damage}伤害`);
        }

        // 创建命中特效
        this.createHitEffect();
    }

    /**
     * 创建命中特效
     */
    createHitEffect() {
        // 这里可以添加粒子效果
        console.log(`✨ 在(${this.x}, ${this.y})创建${this.element}命中特效`);
    }

    /**
     * 渲染子弹
     */
    render(renderer) {
        if (!this.isActive) return;

        // 绘制轨迹
        this.renderTrail(renderer);

        // 绘制子弹主体
        renderer.drawGradientCircle(
            this.x, this.y, this.size,
            this.color, 'rgba(255, 255, 255, 0.8)'
        );

        // 绘制元素效果
        this.renderElementEffect(renderer);
    }

    /**
     * 渲染轨迹
     */
    renderTrail(renderer) {
        if (this.trail.length < 2) return;

        for (let i = 0; i < this.trail.length - 1; i++) {
            const alpha = (i + 1) / this.trail.length * 0.5;
            const size = (i + 1) / this.trail.length * this.size * 0.8;

            renderer.ctx.globalAlpha = alpha;
            renderer.drawCircle(
                this.trail[i].x, this.trail[i].y, size,
                this.color
            );
        }

        renderer.ctx.globalAlpha = 1.0;
    }

    /**
     * 渲染元素特效
     */
    renderElementEffect(renderer) {
        const time = this.lifeTime;

        switch (this.element) {
            case 'fire':
                // 火焰粒子效果
                const flameSize = this.size * (0.8 + Math.sin(time * 10) * 0.2);
                renderer.drawGlow(this.x, this.y, flameSize, '#ff6b35', 0.6);
                break;

            case 'water':
                // 水滴效果
                renderer.drawCircle(
                    this.x, this.y, this.size * 1.2,
                    null, 'rgba(116, 185, 255, 0.4)', 2
                );
                break;

            case 'earth':
                // 岩石碎片
                renderer.ctx.fillStyle = '#8d6e63';
                for (let i = 0; i < 4; i++) {
                    const angle = (i / 4) * Math.PI * 2 + time * 2;
                    const x = this.x + Math.cos(angle) * this.size * 0.7;
                    const y = this.y + Math.sin(angle) * this.size * 0.7;
                    renderer.drawCircle(x, y, 2, '#8d6e63');
                }
                break;

            case 'air':
                // 风旋效果
                renderer.ctx.strokeStyle = 'rgba(0, 206, 201, 0.6)';
                renderer.ctx.lineWidth = 2;
                for (let i = 0; i < 3; i++) {
                    const radius = this.size * (1 + i * 0.3);
                    const startAngle = time * 5 + i * Math.PI / 3;
                    renderer.ctx.beginPath();
                    renderer.ctx.arc(this.x, this.y, radius, startAngle, startAngle + Math.PI);
                    renderer.ctx.stroke();
                }
                break;
        }
    }
}

// 子弹管理器
export class ProjectileManager {
    constructor(gameEngine) {
        this.gameEngine = gameEngine;
        this.projectiles = [];

        console.log('🎯 子弹管理系统初始化完成');
    }

    /**
     * 添加子弹
     */
    addProjectile(projectileData) {
        const projectile = new Projectile(projectileData);
        this.projectiles.push(projectile);
        return projectile;
    }

    /**
     * 更新所有子弹
     */
    update(deltaTime) {
        // 更新子弹
        for (const projectile of this.projectiles) {
            projectile.update(deltaTime);
        }

        // 移除非活跃的子弹
        this.projectiles = this.projectiles.filter(p => p.isActive);
    }

    /**
     * 渲染所有子弹
     */
    render(renderer) {
        for (const projectile of this.projectiles) {
            projectile.render(renderer);
        }
    }

    /**
     * 清空所有子弹
     */
    clear() {
        this.projectiles = [];
        console.log('🧹 清空所有子弹');
    }

    /**
     * 获取子弹统计信息
     */
    getStatistics() {
        return {
            activeProjectiles: this.projectiles.length,
            projectilesByElement: this.getProjectilesByElement()
        };
    }

    /**
     * 按元素分组统计子弹
     */
    getProjectilesByElement() {
        const stats = {};
        for (const projectile of this.projectiles) {
            stats[projectile.element] = (stats[projectile.element] || 0) + 1;
        }
        return stats;
    }
}