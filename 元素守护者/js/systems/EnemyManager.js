/**
 * 敌人管理系统
 * 处理敌人的生成、移动、AI和进化机制
 */

import { Vector2 } from '../utils/Vector2.js';

// 敌人基础类
class Enemy {
    constructor(type, path) {
        this.type = type;
        this.path = path;
        this.pathIndex = 0;

        // 位置和移动
        this.x = path[0].x;
        this.y = path[0].y;
        this.speed = 50;
        this.direction = 0;

        // 属性
        this.maxHealth = 100;
        this.health = this.maxHealth;
        this.armor = 0;
        this.goldReward = 5;
        this.scoreValue = 10;

        // 状态
        this.isAlive = true;
        this.effects = new Map(); // 状态效果

        // 视觉
        this.size = 15;
        this.color = '#e74c3c';
        this.animationTime = 0;

        this.initializeStats();
        console.log(`👹 生成${type}敌人`);
    }

    /**
     * 初始化敌人属性
     */
    initializeStats() {
        const stats = this.getEnemyStats(this.type);
        this.maxHealth = stats.health;
        this.health = this.maxHealth;
        this.speed = stats.speed;
        this.armor = stats.armor;
        this.goldReward = stats.goldReward;
        this.scoreValue = stats.scoreValue;
        this.color = stats.color;
        this.size = stats.size;
    }

    /**
     * 获取敌人数据
     */
    getEnemyStats(type) {
        const enemyData = {
            basic: {
                health: 100,
                speed: 50,
                armor: 0,
                goldReward: 5,
                scoreValue: 10,
                color: '#e74c3c',
                size: 15
            },
            fast: {
                health: 60,
                speed: 80,
                armor: 0,
                goldReward: 8,
                scoreValue: 15,
                color: '#f39c12',
                size: 12
            },
            heavy: {
                health: 200,
                speed: 30,
                armor: 5,
                goldReward: 15,
                scoreValue: 25,
                color: '#8e44ad',
                size: 20
            },
            flying: {
                health: 80,
                speed: 60,
                armor: 0,
                goldReward: 12,
                scoreValue: 20,
                color: '#3498db',
                size: 14
            }
        };

        return enemyData[type] || enemyData.basic;
    }

    /**
     * 更新敌人状态
     */
    update(deltaTime) {
        if (!this.isAlive) return;

        this.animationTime += deltaTime;

        // 更新状态效果
        this.updateEffects(deltaTime);

        // 移动
        this.move(deltaTime);

        // 检查是否到达终点
        if (this.pathIndex >= this.path.length - 1) {
            this.reachEnd();
        }
    }

    /**
     * 移动逻辑
     */
    move(deltaTime) {
        if (this.pathIndex >= this.path.length - 1) return;

        const currentTarget = this.path[this.pathIndex + 1];
        const dx = currentTarget.x - this.x;
        const dy = currentTarget.y - this.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance < 5) {
            // 到达当前路径点，移动到下一个
            this.pathIndex++;
            return;
        }

        // 计算移动方向
        this.direction = Math.atan2(dy, dx);

        // 应用速度修正（考虑减速效果）
        let currentSpeed = this.speed;
        if (this.effects.has('slow')) {
            const slowEffect = this.effects.get('slow');
            currentSpeed *= slowEffect.slowFactor;
        }

        // 移动
        const moveDistance = currentSpeed * deltaTime;
        this.x += (dx / distance) * moveDistance;
        this.y += (dy / distance) * moveDistance;
    }

    /**
     * 更新状态效果
     */
    updateEffects(deltaTime) {
        for (const [effectType, effect] of this.effects) {
            effect.duration -= deltaTime;

            // 应用持续效果
            switch (effectType) {
                case 'burn':
                    this.takeDamage(effect.damage * deltaTime, 'fire', false);
                    break;
                case 'freeze':
                    // 冰冻效果在move方法中处理
                    break;
            }

            // 移除过期效果
            if (effect.duration <= 0) {
                this.effects.delete(effectType);
                console.log(`✨ ${effectType}效果结束`);
            }
        }
    }

    /**
     * 受到伤害
     */
    takeDamage(damage, element = 'physical', showEffect = true) {
        if (!this.isAlive) return;

        // 计算实际伤害（考虑护甲）
        const actualDamage = Math.max(1, damage - this.armor);
        this.health -= actualDamage;

        if (showEffect) {
            console.log(`💥 敌人受到${actualDamage}点${element}伤害`);
        }

        // 检查死亡
        if (this.health <= 0) {
            this.die(element);
        }
    }

    /**
     * 应用状态效果
     */
    applyEffect(effect) {
        switch (effect.type) {
            case 'burn':
                this.effects.set('burn', {
                    duration: effect.duration,
                    damage: effect.damage
                });
                break;

            case 'slow':
                this.effects.set('slow', {
                    duration: effect.duration,
                    slowFactor: effect.slowFactor
                });
                break;

            case 'stun':
                if (Math.random() < effect.chance) {
                    this.effects.set('stun', {
                        duration: effect.duration
                    });
                }
                break;

            case 'knockback':
                // 击退效果
                const knockbackDistance = effect.force;
                const angle = this.direction + Math.PI; // 反方向
                this.x += Math.cos(angle) * knockbackDistance;
                this.y += Math.sin(angle) * knockbackDistance;
                break;
        }

        console.log(`✨ 应用${effect.type}效果`);
    }

    /**
     * 敌人死亡
     */
    die(killedByElement = null) {
        this.isAlive = false;
        this.killedByElement = killedByElement;
        console.log(`💀 敌人死亡，被${killedByElement}元素击杀`);
    }

    /**
     * 到达终点
     */
    reachEnd() {
        this.isAlive = false;
        console.log('🏁 敌人到达终点');
    }

    /**
     * 渲染敌人
     */
    render(renderer) {
        if (!this.isAlive) return;

        // 绘制敌人主体
        renderer.drawGradientCircle(
            this.x, this.y, this.size,
            this.color, '#2d3436'
        );

        // 绘制血条
        this.renderHealthBar(renderer);

        // 绘制状态效果
        this.renderEffects(renderer);

        // 绘制方向指示
        this.renderDirection(renderer);
    }

    /**
     * 渲染血条
     */
    renderHealthBar(renderer) {
        const barWidth = this.size * 2;
        const barHeight = 4;
        const barY = this.y - this.size - 8;

        renderer.drawHealthBar(
            this.x, barY, barWidth, barHeight,
            this.health, this.maxHealth
        );
    }

    /**
     * 渲染状态效果
     */
    renderEffects(renderer) {
        let effectIndex = 0;

        for (const [effectType, effect] of this.effects) {
            const iconX = this.x - this.size + effectIndex * 12;
            const iconY = this.y + this.size + 8;

            let icon = '';
            let color = 'white';

            switch (effectType) {
                case 'burn':
                    icon = '🔥';
                    color = '#ff6b35';
                    break;
                case 'slow':
                    icon = '❄️';
                    color = '#74b9ff';
                    break;
                case 'stun':
                    icon = '💫';
                    color = '#ffd700';
                    break;
            }

            if (icon) {
                renderer.drawText(icon, iconX, iconY, '10px Arial', color);
            }

            effectIndex++;
        }
    }

    /**
     * 渲染方向指示
     */
    renderDirection(renderer) {
        const arrowLength = this.size * 0.6;
        const arrowX = this.x + Math.cos(this.direction) * arrowLength;
        const arrowY = this.y + Math.sin(this.direction) * arrowLength;

        renderer.drawLine(
            this.x, this.y, arrowX, arrowY,
            'rgba(255, 255, 255, 0.7)', 2
        );
    }
}

// 敌人管理器
export class EnemyManager {
    constructor(gameEngine) {
        this.gameEngine = gameEngine;
        this.enemies = [];

        console.log('👹 敌人管理系统初始化完成');
    }

    /**
     * 生成敌人
     */
    spawnEnemy(type) {
        const path = this.gameEngine.pathPoints;
        if (path.length === 0) return null;

        const enemy = new Enemy(type, path);
        this.enemies.push(enemy);
        return enemy;
    }

    /**
     * 更新所有敌人
     */
    update(deltaTime) {
        // 更新敌人
        for (const enemy of this.enemies) {
            enemy.update(deltaTime);
        }

        // 处理死亡的敌人
        const deadEnemies = this.enemies.filter(e => !e.isAlive);
        for (const enemy of deadEnemies) {
            if (enemy.killedByElement) {
                // 敌人被击杀
                this.gameEngine.gameState.killEnemy(enemy);
            } else {
                // 敌人到达终点
                this.gameEngine.gameState.loseLife(1);
            }
        }

        // 移除死亡的敌人
        this.enemies = this.enemies.filter(e => e.isAlive);
    }

    /**
     * 渲染所有敌人
     */
    render(renderer) {
        for (const enemy of this.enemies) {
            enemy.render(renderer);
        }
    }

    /**
     * 清空所有敌人
     */
    clear() {
        this.enemies = [];
        console.log('🧹 清空所有敌人');
    }

    /**
     * 获取敌人统计信息
     */
    getStatistics() {
        return {
            totalEnemies: this.enemies.length,
            enemiesByType: this.getEnemiesByType(),
            averageHealth: this.getAverageHealth()
        };
    }

    /**
     * 按类型分组统计敌人
     */
    getEnemiesByType() {
        const stats = {};
        for (const enemy of this.enemies) {
            stats[enemy.type] = (stats[enemy.type] || 0) + 1;
        }
        return stats;
    }

    /**
     * 获取平均血量
     */
    getAverageHealth() {
        if (this.enemies.length === 0) return 0;

        const totalHealth = this.enemies.reduce((sum, e) => sum + e.health, 0);
        return totalHealth / this.enemies.length;
    }
}