/**
 * 存档管理系统
 * 处理游戏数据的本地存储和读取
 */

export class SaveManager {
    constructor() {
        this.storageKey = 'elemental_guardians_save';
        this.settingsKey = 'elemental_guardians_settings';

        // 默认设置
        this.defaultSettings = {
            masterVolume: 1.0,
            sfxVolume: 0.7,
            musicVolume: 0.5,
            showFPS: false,
            showGrid: false,
            difficulty: 'normal',
            language: 'zh'
        };

        console.log('💾 存档管理系统初始化完成');
    }

    /**
     * 保存游戏数据
     */
    saveGame(gameState, towerData = null, waveData = null) {
        try {
            const saveData = {
                version: '1.0.0',
                timestamp: Date.now(),
                gameState: gameState.toJSON(),
                towers: towerData,
                wave: waveData
            };

            localStorage.setItem(this.storageKey, JSON.stringify(saveData));
            console.log('💾 游戏数据保存成功');
            return true;

        } catch (error) {
            console.error('❌ 游戏数据保存失败:', error);
            return false;
        }
    }

    /**
     * 加载游戏数据
     */
    loadGame() {
        try {
            const saveData = localStorage.getItem(this.storageKey);
            if (!saveData) {
                console.log('📂 没有找到存档数据');
                return null;
            }

            const data = JSON.parse(saveData);
            console.log('📂 游戏数据加载成功');
            return data;

        } catch (error) {
            console.error('❌ 游戏数据加载失败:', error);
            return null;
        }
    }

    /**
     * 删除存档
     */
    deleteSave() {
        try {
            localStorage.removeItem(this.storageKey);
            console.log('🗑️ 存档已删除');
            return true;
        } catch (error) {
            console.error('❌ 删除存档失败:', error);
            return false;
        }
    }

    /**
     * 检查是否有存档
     */
    hasSave() {
        return localStorage.getItem(this.storageKey) !== null;
    }

    /**
     * 保存设置
     */
    saveSettings(settings) {
        try {
            localStorage.setItem(this.settingsKey, JSON.stringify(settings));
            console.log('⚙️ 设置保存成功');
            return true;
        } catch (error) {
            console.error('❌ 设置保存失败:', error);
            return false;
        }
    }

    /**
     * 加载设置
     */
    loadSettings() {
        try {
            const settings = localStorage.getItem(this.settingsKey);
            if (!settings) {
                return { ...this.defaultSettings };
            }

            const loadedSettings = JSON.parse(settings);
            return { ...this.defaultSettings, ...loadedSettings };

        } catch (error) {
            console.error('❌ 设置加载失败:', error);
            return { ...this.defaultSettings };
        }
    }

    /**
     * 重置设置
     */
    resetSettings() {
        try {
            localStorage.removeItem(this.settingsKey);
            console.log('🔄 设置已重置');
            return true;
        } catch (error) {
            console.error('❌ 重置设置失败:', error);
            return false;
        }
    }

    /**
     * 获取存储使用情况
     */
    getStorageInfo() {
        try {
            const saveSize = localStorage.getItem(this.storageKey)?.length || 0;
            const settingsSize = localStorage.getItem(this.settingsKey)?.length || 0;

            return {
                saveSize: saveSize,
                settingsSize: settingsSize,
                totalSize: saveSize + settingsSize,
                hasSave: this.hasSave()
            };
        } catch (error) {
            console.error('❌ 获取存储信息失败:', error);
            return null;
        }
    }
}