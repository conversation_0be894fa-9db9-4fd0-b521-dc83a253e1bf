/**
 * 塔管理系统
 * 负责塔的创建、更新、升级和融合机制
 */

import { Vector2 } from '../utils/Vector2.js';

// 塔的基础类
class Tower {
    constructor(type, x, y, gridX, gridY) {
        this.type = type;
        this.x = x;
        this.y = y;
        this.gridX = gridX;
        this.gridY = gridY;

        // 基础属性
        this.level = 1;
        this.damage = 0;
        this.range = 0;
        this.attackSpeed = 0;
        this.cost = 0;
        this.element = '';

        // 状态
        this.lastAttackTime = 0;
        this.target = null;
        this.isActive = true;

        // 视觉效果
        this.rotation = 0;
        this.animationTime = 0;
        this.isAttacking = false;
        this.attackAnimationDuration = 0.3;

        // 融合相关
        this.isFused = false;
        this.fusedElements = [];
        this.fusionBonus = 1.0;

        this.initializeStats();
    }

    /**
     * 初始化塔的属性
     */
    initializeStats() {
        const stats = this.getTowerStats(this.type);
        this.damage = stats.damage;
        this.range = stats.range;
        this.attackSpeed = stats.attackSpeed;
        this.cost = stats.cost;
        this.element = stats.element;
        this.color = stats.color;
        this.icon = stats.icon;
    }

    /**
     * 获取塔的基础数据
     */
    getTowerStats(type) {
        const towerData = {
            fire: {
                damage: 25,
                range: 80,
                attackSpeed: 1.0,
                cost: 50,
                element: 'fire',
                color: '#ff6b35',
                icon: '🔥',
                description: '火焰塔：造成持续燃烧伤害'
            },
            water: {
                damage: 15,
                range: 90,
                attackSpeed: 0.8,
                cost: 60,
                element: 'water',
                color: '#74b9ff',
                icon: '💧',
                description: '冰霜塔：减缓敌人移动速度'
            },
            earth: {
                damage: 40,
                range: 70,
                attackSpeed: 0.6,
                cost: 80,
                element: 'earth',
                color: '#6c5ce7',
                icon: '🗿',
                description: '岩石塔：高伤害，可击晕敌人'
            },
            air: {
                damage: 20,
                range: 100,
                attackSpeed: 1.2,
                cost: 70,
                element: 'air',
                color: '#00cec9',
                icon: '💨',
                description: '风暴塔：攻击速度快，可击退敌人'
            }
        };

        return towerData[type] || towerData.fire;
    }

    /**
     * 更新塔的状态
     */
    update(deltaTime, enemies) {
        this.animationTime += deltaTime;

        // 更新攻击动画
        if (this.isAttacking) {
            if (this.animationTime - this.lastAttackTime > this.attackAnimationDuration) {
                this.isAttacking = false;
            }
        }

        // 寻找目标
        if (!this.target || !this.isTargetValid(this.target)) {
            this.target = this.findTarget(enemies);
        }

        // 攻击目标
        if (this.target && this.canAttack()) {
            this.attack(this.target);
        }

        // 更新朝向
        if (this.target) {
            const dx = this.target.x - this.x;
            const dy = this.target.y - this.y;
            this.rotation = Math.atan2(dy, dx);
        }
    }

    /**
     * 寻找攻击目标
     */
    findTarget(enemies) {
        let closestEnemy = null;
        let closestDistance = Infinity;

        for (const enemy of enemies) {
            if (!enemy.isAlive) continue;

            const distance = Vector2.distance(this, enemy);
            if (distance <= this.range && distance < closestDistance) {
                closestDistance = distance;
                closestEnemy = enemy;
            }
        }

        return closestEnemy;
    }

    /**
     * 检查目标是否有效
     */
    isTargetValid(target) {
        if (!target || !target.isAlive) return false;

        const distance = Vector2.distance(this, target);
        return distance <= this.range;
    }

    /**
     * 检查是否可以攻击
     */
    canAttack() {
        const currentTime = Date.now() / 1000;
        return currentTime - this.lastAttackTime >= (1 / this.attackSpeed);
    }

    /**
     * 攻击目标
     */
    attack(target) {
        const currentTime = Date.now() / 1000;
        this.lastAttackTime = currentTime;
        this.isAttacking = true;

        // 创建子弹
        return {
            type: 'projectile',
            element: this.element,
            damage: this.damage * this.fusionBonus,
            startX: this.x,
            startY: this.y,
            targetX: target.x,
            targetY: target.y,
            target: target,
            tower: this,
            speed: 300,
            effects: this.getAttackEffects()
        };
    }

    /**
     * 获取攻击效果
     */
    getAttackEffects() {
        const effects = [];

        switch (this.element) {
            case 'fire':
                effects.push({ type: 'burn', duration: 3, damage: 5 });
                break;
            case 'water':
                effects.push({ type: 'slow', duration: 2, slowFactor: 0.5 });
                break;
            case 'earth':
                effects.push({ type: 'stun', duration: 1, chance: 0.3 });
                break;
            case 'air':
                effects.push({ type: 'knockback', force: 50 });
                break;
        }

        // 融合效果
        if (this.isFused) {
            effects.push(...this.getFusionEffects());
        }

        return effects;
    }

    /**
     * 获取融合效果
     */
    getFusionEffects() {
        const effects = [];
        const elements = this.fusedElements.sort();

        // 双元素融合效果
        if (elements.length === 2) {
            const fusion = elements.join('-');

            switch (fusion) {
                case 'fire-water':
                    effects.push({ type: 'steam', damage: 10, aoe: 30 });
                    break;
                case 'fire-earth':
                    effects.push({ type: 'lava', damage: 15, duration: 4 });
                    break;
                case 'fire-air':
                    effects.push({ type: 'explosion', damage: 20, aoe: 40 });
                    break;
                case 'water-earth':
                    effects.push({ type: 'mud', slow: 0.3, duration: 5 });
                    break;
                case 'water-air':
                    effects.push({ type: 'ice', freeze: 2, damage: 12 });
                    break;
                case 'earth-air':
                    effects.push({ type: 'sandstorm', blind: 3, aoe: 50 });
                    break;
            }
        }

        return effects;
    }

    /**
     * 升级塔
     */
    upgrade() {
        if (this.level >= 5) return false; // 最大等级限制

        const upgradeCost = this.getUpgradeCost();
        this.level++;

        // 提升属性
        this.damage = Math.floor(this.damage * 1.3);
        this.range = Math.floor(this.range * 1.1);
        this.attackSpeed *= 1.2;

        console.log(`⬆️ ${this.element}塔升级到${this.level}级`);
        return upgradeCost;
    }

    /**
     * 获取升级费用
     */
    getUpgradeCost() {
        return Math.floor(this.cost * 0.7 * Math.pow(1.5, this.level - 1));
    }

    /**
     * 渲染塔
     */
    render(renderer) {
        // 保存画布状态
        renderer.save();

        // 移动到塔的位置
        renderer.translate(this.x, this.y);

        // 旋转到目标方向
        if (this.target) {
            renderer.rotate(this.rotation);
        }

        // 绘制塔的基座
        const baseRadius = 18;
        renderer.drawGradientCircle(0, 0, baseRadius, this.color, '#2d3436');

        // 绘制塔的边框
        renderer.drawCircle(0, 0, baseRadius, null, 'rgba(255, 255, 255, 0.3)', 2);

        // 绘制元素图标
        renderer.drawText(this.icon, 0, 0, '24px Arial', 'white');

        // 绘制等级指示器
        if (this.level > 1) {
            renderer.drawText(this.level.toString(), 12, -12, '12px Arial', '#ffd700');
        }

        // 绘制攻击动画
        if (this.isAttacking) {
            const attackProgress = (this.animationTime - this.lastAttackTime) / this.attackAnimationDuration;
            const glowRadius = baseRadius + attackProgress * 10;
            renderer.drawGlow(0, 0, glowRadius, this.color, 0.5 * (1 - attackProgress));
        }

        // 融合效果
        if (this.isFused) {
            this.renderFusionEffect(renderer, baseRadius);
        }

        // 恢复画布状态
        renderer.restore();
    }

    /**
     * 渲染融合效果
     */
    renderFusionEffect(renderer, baseRadius) {
        const time = this.animationTime;
        const pulseRadius = baseRadius + Math.sin(time * 3) * 5;

        // 绘制融合光环
        renderer.drawCircle(0, 0, pulseRadius, null, 'rgba(255, 215, 0, 0.6)', 3);

        // 绘制融合元素指示器
        const angleStep = (Math.PI * 2) / this.fusedElements.length;
        for (let i = 0; i < this.fusedElements.length; i++) {
            const angle = i * angleStep + time;
            const x = Math.cos(angle) * (baseRadius + 8);
            const y = Math.sin(angle) * (baseRadius + 8);

            const elementStats = this.getTowerStats(this.fusedElements[i]);
            renderer.drawText(elementStats.icon, x, y, '16px Arial', 'white');
        }
    }
}

// 塔管理器类
export class TowerManager {
    constructor(gameEngine) {
        this.gameEngine = gameEngine;
        this.towers = [];
        this.projectiles = [];

        // 融合系统
        this.fusionCombinations = new Map();
        this.initializeFusionCombinations();

        console.log('🏗️ 塔管理系统初始化完成');
    }

    /**
     * 初始化融合组合
     */
    initializeFusionCombinations() {
        // 双元素融合
        this.fusionCombinations.set('fire-water', {
            name: '蒸汽塔',
            icon: '☁️',
            color: '#95a5a6',
            bonusDamage: 1.5,
            specialEffect: 'steam'
        });

        this.fusionCombinations.set('fire-earth', {
            name: '熔岩塔',
            icon: '🌋',
            color: '#e17055',
            bonusDamage: 1.8,
            specialEffect: 'lava'
        });

        this.fusionCombinations.set('fire-air', {
            name: '爆炸塔',
            icon: '💥',
            color: '#fd79a8',
            bonusDamage: 2.0,
            specialEffect: 'explosion'
        });

        this.fusionCombinations.set('water-earth', {
            name: '泥沼塔',
            icon: '🟫',
            color: '#8d6e63',
            bonusDamage: 1.3,
            specialEffect: 'mud'
        });

        this.fusionCombinations.set('water-air', {
            name: '冰霜塔',
            icon: '❄️',
            color: '#81ecec',
            bonusDamage: 1.6,
            specialEffect: 'ice'
        });

        this.fusionCombinations.set('earth-air', {
            name: '沙暴塔',
            icon: '🌪️',
            color: '#fdcb6e',
            bonusDamage: 1.4,
            specialEffect: 'sandstorm'
        });

        // 三元素融合
        this.fusionCombinations.set('fire-water-earth', {
            name: '原始塔',
            icon: '🌍',
            color: '#00b894',
            bonusDamage: 2.5,
            specialEffect: 'primal'
        });

        this.fusionCombinations.set('fire-water-air', {
            name: '风暴塔',
            icon: '⛈️',
            color: '#6c5ce7',
            bonusDamage: 2.3,
            specialEffect: 'storm'
        });

        this.fusionCombinations.set('fire-earth-air', {
            name: '火山塔',
            icon: '🏔️',
            color: '#e84393',
            bonusDamage: 2.7,
            specialEffect: 'volcanic'
        });

        this.fusionCombinations.set('water-earth-air', {
            name: '自然塔',
            icon: '🌿',
            color: '#00cec9',
            bonusDamage: 2.2,
            specialEffect: 'nature'
        });

        // 四元素融合
        this.fusionCombinations.set('fire-water-earth-air', {
            name: '元素主宰塔',
            icon: '✨',
            color: '#ffd700',
            bonusDamage: 3.0,
            specialEffect: 'elemental_mastery'
        });
    }

    /**
     * 建造塔
     */
    buildTower(type, x, y, gridX, gridY) {
        const tower = new Tower(type, x, y, gridX, gridY);
        this.towers.push(tower);

        console.log(`🏗️ 在(${gridX}, ${gridY})建造了${type}塔`);
        return tower;
    }

    /**
     * 获取指定位置的塔
     */
    getTowerAt(gridX, gridY) {
        return this.towers.find(tower =>
            tower.gridX === gridX && tower.gridY === gridY
        );
    }

    /**
     * 升级塔
     */
    upgradeTower(tower) {
        const cost = tower.getUpgradeCost();
        if (this.gameEngine.gameState.spendGold(cost)) {
            tower.upgrade();
            return true;
        }
        return false;
    }

    /**
     * 出售塔
     */
    sellTower(tower) {
        const sellPrice = Math.floor(tower.cost * 0.6);
        this.gameEngine.gameState.addGold(sellPrice);

        const index = this.towers.indexOf(tower);
        if (index > -1) {
            this.towers.splice(index, 1);
            console.log(`💰 出售塔获得${sellPrice}金币`);
        }
    }

    /**
     * 尝试融合塔
     */
    attemptFusion(centerTower, adjacentTowers) {
        if (centerTower.isFused) {
            console.log('❌ 该塔已经融合过了');
            return false;
        }

        // 收集所有相邻塔的元素
        const elements = [centerTower.element];
        const towersToFuse = [centerTower];

        for (const tower of adjacentTowers) {
            if (tower && !tower.isFused && !elements.includes(tower.element)) {
                elements.push(tower.element);
                towersToFuse.push(tower);
            }
        }

        // 检查是否有有效的融合组合
        const fusionKey = elements.sort().join('-');
        const fusionData = this.fusionCombinations.get(fusionKey);

        if (!fusionData) {
            console.log('❌ 没有找到有效的融合组合');
            return false;
        }

        // 检查是否已解锁该融合
        if (!this.gameEngine.gameState.isFusionUnlocked(elements)) {
            console.log('❌ 该融合组合尚未解锁');
            return false;
        }

        // 执行融合
        this.performFusion(centerTower, towersToFuse, fusionData);
        return true;
    }

    /**
     * 执行融合
     */
    performFusion(centerTower, towersToFuse, fusionData) {
        // 移除其他塔
        for (let i = 1; i < towersToFuse.length; i++) {
            const tower = towersToFuse[i];
            const index = this.towers.indexOf(tower);
            if (index > -1) {
                this.towers.splice(index, 1);
            }
        }

        // 升级中心塔
        centerTower.isFused = true;
        centerTower.fusedElements = towersToFuse.map(t => t.element);
        centerTower.fusionBonus = fusionData.bonusDamage;
        centerTower.color = fusionData.color;
        centerTower.icon = fusionData.icon;

        // 提升属性
        centerTower.damage = Math.floor(centerTower.damage * fusionData.bonusDamage);
        centerTower.range = Math.floor(centerTower.range * 1.2);
        centerTower.attackSpeed *= 1.1;

        console.log(`✨ 融合成功！创建了${fusionData.name}`);

        // 创建融合特效
        this.createFusionEffect(centerTower.x, centerTower.y);
    }

    /**
     * 创建融合特效
     */
    createFusionEffect(x, y) {
        // 这里可以添加粒子效果
        console.log(`💫 在(${x}, ${y})创建融合特效`);
    }

    /**
     * 获取相邻的塔
     */
    getAdjacentTowers(tower) {
        const adjacent = [];
        const directions = [
            { x: -1, y: 0 }, { x: 1, y: 0 },
            { x: 0, y: -1 }, { x: 0, y: 1 },
            { x: -1, y: -1 }, { x: 1, y: -1 },
            { x: -1, y: 1 }, { x: 1, y: 1 }
        ];

        for (const dir of directions) {
            const adjacentTower = this.getTowerAt(
                tower.gridX + dir.x,
                tower.gridY + dir.y
            );
            adjacent.push(adjacentTower);
        }

        return adjacent;
    }

    /**
     * 更新所有塔
     */
    update(deltaTime) {
        const enemies = this.gameEngine.enemyManager.enemies;

        for (const tower of this.towers) {
            tower.update(deltaTime, enemies);

            // 处理塔的攻击
            if (tower.target && tower.canAttack()) {
                const projectile = tower.attack(tower.target);
                if (projectile) {
                    this.gameEngine.projectileManager.addProjectile(projectile);
                }
            }
        }
    }

    /**
     * 渲染所有塔
     */
    render(renderer) {
        for (const tower of this.towers) {
            tower.render(renderer);
        }
    }

    /**
     * 清空所有塔
     */
    clear() {
        this.towers = [];
        console.log('🧹 清空所有塔');
    }

    /**
     * 获取塔的统计信息
     */
    getStatistics() {
        return {
            totalTowers: this.towers.length,
            towersByElement: this.getTowersByElement(),
            fusedTowers: this.towers.filter(t => t.isFused).length,
            totalDamage: this.towers.reduce((sum, t) => sum + t.damage, 0),
            averageLevel: this.towers.reduce((sum, t) => sum + t.level, 0) / this.towers.length || 0
        };
    }

    /**
     * 按元素分组统计塔
     */
    getTowersByElement() {
        const stats = {};
        for (const tower of this.towers) {
            if (tower.isFused) {
                const key = tower.fusedElements.sort().join('-');
                stats[key] = (stats[key] || 0) + 1;
            } else {
                stats[tower.element] = (stats[tower.element] || 0) + 1;
            }
        }
        return stats;
    }
}