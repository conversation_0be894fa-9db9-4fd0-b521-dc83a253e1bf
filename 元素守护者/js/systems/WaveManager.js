/**
 * 波次管理系统
 * 控制敌人波次的生成和进度
 */

export class WaveManager {
    constructor(gameEngine) {
        this.gameEngine = gameEngine;

        // 波次状态
        this.currentWave = 0;
        this.totalWaves = 10;
        this.isWaveActive = false;
        this.waveStartTime = 0;
        this.timeBetweenWaves = 5; // 波次间隔时间（秒）
        this.nextWaveTimer = 0;

        // 当前波次配置
        this.currentWaveConfig = null;
        this.enemySpawnQueue = [];
        this.lastSpawnTime = 0;

        // 波次配置
        this.waveConfigs = this.generateWaveConfigs();

        console.log('🌊 波次管理系统初始化完成');
    }

    /**
     * 生成波次配置
     */
    generateWaveConfigs() {
        const configs = [];

        for (let wave = 1; wave <= this.totalWaves; wave++) {
            const config = {
                wave: wave,
                enemies: [],
                spawnInterval: Math.max(0.5, 2 - wave * 0.1), // 生成间隔逐渐减少
                totalEnemies: Math.floor(5 + wave * 2), // 敌人数量逐渐增加
                difficulty: 1 + (wave - 1) * 0.2 // 难度倍率
            };

            // 根据波次生成不同类型的敌人
            if (wave <= 3) {
                // 前3波：只有基础敌人
                for (let i = 0; i < config.totalEnemies; i++) {
                    config.enemies.push({ type: 'basic', delay: i * config.spawnInterval });
                }
            } else if (wave <= 6) {
                // 4-6波：基础敌人 + 快速敌人
                const basicCount = Math.floor(config.totalEnemies * 0.7);
                const fastCount = config.totalEnemies - basicCount;

                for (let i = 0; i < basicCount; i++) {
                    config.enemies.push({ type: 'basic', delay: i * config.spawnInterval });
                }
                for (let i = 0; i < fastCount; i++) {
                    config.enemies.push({
                        type: 'fast',
                        delay: (basicCount + i) * config.spawnInterval
                    });
                }
            } else if (wave <= 9) {
                // 7-9波：多种敌人混合
                const basicCount = Math.floor(config.totalEnemies * 0.4);
                const fastCount = Math.floor(config.totalEnemies * 0.3);
                const heavyCount = Math.floor(config.totalEnemies * 0.2);
                const flyingCount = config.totalEnemies - basicCount - fastCount - heavyCount;

                let enemyIndex = 0;

                // 添加各种类型的敌人
                for (let i = 0; i < basicCount; i++) {
                    config.enemies.push({ type: 'basic', delay: enemyIndex * config.spawnInterval });
                    enemyIndex++;
                }
                for (let i = 0; i < fastCount; i++) {
                    config.enemies.push({ type: 'fast', delay: enemyIndex * config.spawnInterval });
                    enemyIndex++;
                }
                for (let i = 0; i < heavyCount; i++) {
                    config.enemies.push({ type: 'heavy', delay: enemyIndex * config.spawnInterval });
                    enemyIndex++;
                }
                for (let i = 0; i < flyingCount; i++) {
                    config.enemies.push({ type: 'flying', delay: enemyIndex * config.spawnInterval });
                    enemyIndex++;
                }
            } else {
                // 第10波：Boss波次
                config.enemies.push({ type: 'heavy', delay: 0 });
                config.enemies.push({ type: 'heavy', delay: 2 });
                config.enemies.push({ type: 'heavy', delay: 4 });

                // 添加大量小兵
                for (let i = 0; i < 15; i++) {
                    config.enemies.push({
                        type: Math.random() < 0.5 ? 'basic' : 'fast',
                        delay: 6 + i * 0.3
                    });
                }
            }

            configs.push(config);
        }

        return configs;
    }

    /**
     * 开始下一波
     */
    startNextWave() {
        if (this.currentWave >= this.totalWaves) {
            console.log('🎉 所有波次已完成！');
            return false;
        }

        this.currentWave++;
        this.gameEngine.gameState.currentWave = this.currentWave;

        this.currentWaveConfig = this.waveConfigs[this.currentWave - 1];
        this.enemySpawnQueue = [...this.currentWaveConfig.enemies];
        this.isWaveActive = true;
        this.waveStartTime = Date.now() / 1000;
        this.lastSpawnTime = this.waveStartTime;

        console.log(`🌊 开始第${this.currentWave}波，共${this.currentWaveConfig.totalEnemies}个敌人`);

        // 播放波次开始音效
        if (this.gameEngine.audioManager) {
            this.gameEngine.audioManager.playSFX('waveStart');
        }

        return true;
    }

    /**
     * 更新波次状态
     */
    update(deltaTime) {
        const currentTime = Date.now() / 1000;

        if (this.isWaveActive) {
            // 生成敌人
            this.spawnEnemies(currentTime);

            // 检查波次是否完成
            if (this.enemySpawnQueue.length === 0 &&
                this.gameEngine.enemyManager.enemies.length === 0) {
                this.completeWave();
            }
        } else {
            // 波次间隔倒计时
            this.nextWaveTimer -= deltaTime;

            if (this.nextWaveTimer <= 0 && this.currentWave < this.totalWaves) {
                this.startNextWave();
            }
        }
    }

    /**
     * 生成敌人
     */
    spawnEnemies(currentTime) {
        if (this.enemySpawnQueue.length === 0) return;

        const nextEnemy = this.enemySpawnQueue[0];
        const spawnTime = this.waveStartTime + nextEnemy.delay;

        if (currentTime >= spawnTime) {
            // 生成敌人
            this.gameEngine.enemyManager.spawnEnemy(nextEnemy.type);
            this.enemySpawnQueue.shift();

            console.log(`👹 生成${nextEnemy.type}敌人 (剩余: ${this.enemySpawnQueue.length})`);
        }
    }

    /**
     * 完成当前波次
     */
    completeWave() {
        this.isWaveActive = false;
        this.nextWaveTimer = this.timeBetweenWaves;

        // 给予波次完成奖励
        const waveBonus = this.currentWave * 10;
        this.gameEngine.gameState.addGold(waveBonus);
        this.gameEngine.gameState.addScore(this.currentWave * 50);

        console.log(`✅ 第${this.currentWave}波完成！获得${waveBonus}金币奖励`);

        // 播放波次完成音效
        if (this.gameEngine.audioManager) {
            this.gameEngine.audioManager.playSFX('waveComplete');
        }

        // 检查是否所有波次都完成了
        if (this.currentWave >= this.totalWaves) {
            this.gameEngine.gameOver(true); // 胜利
        }
    }

    /**
     * 强制开始下一波
     */
    forceNextWave() {
        if (!this.isWaveActive && this.currentWave < this.totalWaves) {
            this.nextWaveTimer = 0;
            return true;
        }
        return false;
    }

    /**
     * 检查是否所有波次都完成
     */
    isAllWavesComplete() {
        return this.currentWave >= this.totalWaves && !this.isWaveActive;
    }

    /**
     * 获取当前波次进度
     */
    getWaveProgress() {
        if (!this.isWaveActive || !this.currentWaveConfig) {
            return 1.0;
        }

        const totalEnemies = this.currentWaveConfig.totalEnemies;
        const remainingEnemies = this.enemySpawnQueue.length +
                                this.gameEngine.enemyManager.enemies.length;

        return 1.0 - (remainingEnemies / totalEnemies);
    }

    /**
     * 获取下一波倒计时
     */
    getNextWaveCountdown() {
        return Math.max(0, this.nextWaveTimer);
    }

    /**
     * 重置波次管理器
     */
    reset() {
        this.currentWave = 0;
        this.isWaveActive = false;
        this.waveStartTime = 0;
        this.nextWaveTimer = 0;
        this.currentWaveConfig = null;
        this.enemySpawnQueue = [];
        this.lastSpawnTime = 0;

        console.log('🔄 波次管理器已重置');
    }

    /**
     * 获取波次统计信息
     */
    getStatistics() {
        return {
            currentWave: this.currentWave,
            totalWaves: this.totalWaves,
            isWaveActive: this.isWaveActive,
            waveProgress: this.getWaveProgress(),
            nextWaveCountdown: this.getNextWaveCountdown(),
            remainingEnemiesInQueue: this.enemySpawnQueue.length
        };
    }
}