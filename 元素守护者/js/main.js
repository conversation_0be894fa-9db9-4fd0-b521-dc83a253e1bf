/**
 * 元素守护者 - 主入口文件
 * 负责游戏初始化、资源加载和主要模块协调
 */

import { GameEngine } from './core/GameEngine.js';
import { Renderer } from './core/Renderer.js';
import { InputManager } from './core/InputManager.js';
import { AudioManager } from './core/AudioManager.js';
import { SaveManager } from './systems/SaveManager.js';
import { UIManager } from './systems/UIManager.js';

class ElementalGuardians {
    constructor() {
        // 游戏核心组件
        this.canvas = null;
        this.ctx = null;
        this.gameEngine = null;
        this.renderer = null;
        this.inputManager = null;
        this.audioManager = null;
        this.saveManager = null;
        this.uiManager = null;

        // 游戏状态
        this.isLoading = true;
        this.loadingProgress = 0;
        this.gameStarted = false;

        // 资源列表
        this.resources = {
            images: [],
            sounds: [],
            data: []
        };

        console.log('🎮 元素守护者游戏初始化开始...');
    }

    /**
     * 初始化游戏
     */
    async init() {
        try {
            // 初始化画布
            this.initCanvas();

            // 显示加载界面
            this.showLoadingScreen();

            // 加载游戏资源
            await this.loadResources();

            // 初始化核心系统
            this.initCoreSystems();

            // 初始化UI系统
            this.initUI();

            // 绑定事件监听器
            this.bindEvents();

            // 完成加载
            this.finishLoading();

            console.log('✅ 游戏初始化完成！');

        } catch (error) {
            console.error('❌ 游戏初始化失败:', error);
            this.showError('游戏初始化失败，请刷新页面重试');
        }
    }

    /**
     * 初始化画布
     */
    initCanvas() {
        this.canvas = document.getElementById('game-canvas');
        this.ctx = this.canvas.getContext('2d');

        // 设置画布尺寸
        this.resizeCanvas();

        // 监听窗口大小变化
        window.addEventListener('resize', () => this.resizeCanvas());

        console.log('🖼️ 画布初始化完成');
    }

    /**
     * 调整画布尺寸
     */
    resizeCanvas() {
        const container = document.getElementById('game-container');
        const rect = container.getBoundingClientRect();

        this.canvas.width = rect.width;
        this.canvas.height = rect.height;

        // 设置高DPI支持
        const dpr = window.devicePixelRatio || 1;
        const displayWidth = this.canvas.clientWidth;
        const displayHeight = this.canvas.clientHeight;

        if (this.canvas.width !== displayWidth * dpr || this.canvas.height !== displayHeight * dpr) {
            this.canvas.width = displayWidth * dpr;
            this.canvas.height = displayHeight * dpr;
            this.ctx.scale(dpr, dpr);
            this.canvas.style.width = displayWidth + 'px';
            this.canvas.style.height = displayHeight + 'px';
        }
    }

    /**
     * 显示加载界面
     */
    showLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        const progressBar = document.getElementById('loading-progress');
        const loadingText = document.getElementById('loading-text');

        loadingScreen.style.display = 'flex';

        // 模拟加载进度动画
        this.updateLoadingProgress(0, '正在初始化游戏引擎...');
    }

    /**
     * 更新加载进度
     */
    updateLoadingProgress(progress, text) {
        const progressBar = document.getElementById('loading-progress');
        const loadingText = document.getElementById('loading-text');

        this.loadingProgress = Math.max(this.loadingProgress, progress);
        progressBar.style.width = this.loadingProgress + '%';

        if (text) {
            loadingText.textContent = text;
        }
    }

    /**
     * 加载游戏资源
     */
    async loadResources() {
        const loadingSteps = [
            { progress: 20, text: '正在加载游戏引擎...', action: () => this.loadEngineModules() },
            { progress: 40, text: '正在加载游戏数据...', action: () => this.loadGameData() },
            { progress: 60, text: '正在加载音频资源...', action: () => this.loadAudioResources() },
            { progress: 80, text: '正在初始化游戏系统...', action: () => this.loadGameSystems() },
            { progress: 100, text: '加载完成！', action: () => Promise.resolve() }
        ];

        for (const step of loadingSteps) {
            this.updateLoadingProgress(step.progress, step.text);
            await step.action();
            await this.delay(300); // 添加延迟以显示加载过程
        }
    }

    /**
     * 加载引擎模块
     */
    async loadEngineModules() {
        // 这里可以预加载一些重要的模块
        await this.delay(200);
        console.log('🔧 引擎模块加载完成');
    }

    /**
     * 加载游戏数据
     */
    async loadGameData() {
        // 加载塔的配置数据、敌人数据、关卡数据等
        await this.delay(200);
        console.log('📊 游戏数据加载完成');
    }

    /**
     * 加载音频资源
     */
    async loadAudioResources() {
        // 预加载音效文件
        await this.delay(200);
        console.log('🔊 音频资源加载完成');
    }

    /**
     * 加载游戏系统
     */
    async loadGameSystems() {
        await this.delay(200);
        console.log('⚙️ 游戏系统加载完成');
    }

    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 初始化核心系统
     */
    initCoreSystems() {
        // 初始化游戏引擎
        this.gameEngine = new GameEngine(this.canvas, this.renderer, this.inputManager, this.audioManager);

        // 初始化渲染器
        this.renderer = new Renderer(this.ctx, this.canvas.width, this.canvas.height);

        // 初始化输入管理器
        this.inputManager = new InputManager(this.canvas);

        // 初始化音频管理器
        this.audioManager = new AudioManager();

        // 初始化存档管理器
        this.saveManager = new SaveManager();

        console.log('🎯 核心系统初始化完成');
    }

    /**
     * 初始化UI系统
     */
    initUI() {
        this.uiManager = new UIManager(this.gameEngine, this.saveManager, this.audioManager);
        console.log('🎨 UI系统初始化完成');
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 绑定输入事件到游戏引擎
        this.inputManager.onMouseClick = (x, y) => {
            if (this.gameEngine && this.gameStarted) {
                this.gameEngine.handleClick(x, y);
            }
        };

        this.inputManager.onMouseMove = (x, y) => {
            if (this.gameEngine && this.gameStarted) {
                this.gameEngine.handleMouseMove(x, y);
            }
        };

        this.inputManager.onTouchStart = (x, y) => {
            if (this.gameEngine && this.gameStarted) {
                this.gameEngine.handleTouchStart(x, y);
            }
        };

        // 绑定键盘事件
        document.addEventListener('keydown', (e) => {
            if (this.gameEngine && this.gameStarted) {
                this.gameEngine.handleKeyDown(e.key);
            }
        });

        // 绑定页面可见性变化事件（用于暂停游戏）
        document.addEventListener('visibilitychange', () => {
            if (document.hidden && this.gameEngine) {
                this.gameEngine.pause();
            }
        });

        console.log('🎮 事件监听器绑定完成');
    }

    /**
     * 完成加载
     */
    finishLoading() {
        setTimeout(() => {
            // 隐藏加载界面
            const loadingScreen = document.getElementById('loading-screen');
            loadingScreen.style.display = 'none';

            // 显示游戏界面
            const gameContainer = document.getElementById('game-container');
            gameContainer.style.display = 'block';

            // 启动游戏
            this.startGame();

            this.isLoading = false;
            this.gameStarted = true;

        }, 500);
    }

    /**
     * 启动游戏
     */
    startGame() {
        // 启动游戏循环
        this.gameEngine.start();

        // 启动渲染循环
        this.startRenderLoop();

        console.log('🚀 游戏启动成功！');
    }

    /**
     * 渲染循环
     */
    startRenderLoop() {
        const render = () => {
            if (!this.gameStarted) return;

            // 清空画布
            this.renderer.clear();

            // 渲染游戏内容
            if (this.gameEngine) {
                this.gameEngine.render(this.renderer);
            }

            // 继续下一帧
            requestAnimationFrame(render);
        };

        requestAnimationFrame(render);
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        const loadingText = document.getElementById('loading-text');
        if (loadingText) {
            loadingText.textContent = message;
            loadingText.style.color = '#ff6b6b';
        }

        console.error('💥 游戏错误:', message);
    }

    /**
     * 游戏暂停
     */
    pause() {
        if (this.gameEngine) {
            this.gameEngine.pause();
        }
    }

    /**
     * 游戏恢复
     */
    resume() {
        if (this.gameEngine) {
            this.gameEngine.resume();
        }
    }

    /**
     * 重启游戏
     */
    restart() {
        if (this.gameEngine) {
            this.gameEngine.restart();
        }
    }
}

// 游戏启动
document.addEventListener('DOMContentLoaded', () => {
    const game = new ElementalGuardians();
    game.init();

    // 将游戏实例暴露到全局，方便调试
    window.game = game;
});