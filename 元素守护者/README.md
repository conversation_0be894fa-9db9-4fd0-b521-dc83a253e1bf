# 元素守护者 (Elemental Guardians)

一款创新的塔防游戏，融合了元素系统、动态地形和时间循环机制。

## 🎮 游戏特色

### 🔥 元素融合系统
- **四种基础元素**：火、水、土、风
- **融合机制**：相邻的不同元素塔可以融合创造更强大的防御
- **特殊效果**：每种元素都有独特的攻击效果和地形影响

### 🌍 动态地形
- **地形变化**：元素攻击会改变地形属性
- **战略价值**：不同地形对敌人移动速度有影响
- **天气系统**：随机天气事件影响战斗

### ⏰ 时间循环机制
- **状态保存**：可以保存当前游戏状态
- **时间回溯**：返回之前的时间点重新布局
- **策略优化**：通过多次尝试找到最佳防御策略

### 🧠 敌人进化系统
- **智能AI**：敌人会根据玩家的防御策略调整路径
- **抗性进化**：频繁使用的元素攻击会让敌人产生抗性
- **多样化**：多种敌人类型，各有特色

## 🎯 游戏玩法

### 基础操作
1. **建造塔**：点击塔面板选择元素类型，然后点击空地建造
2. **升级塔**：点击已建造的塔，在升级面板中提升属性
3. **融合塔**：将不同元素的塔建造在相邻位置，点击融合按钮
4. **出售塔**：在升级面板中可以出售不需要的塔

### 元素特性
- **🔥 火元素**：高伤害，可燃烧地面，对冰系敌人效果显著
- **💧 水元素**：减速效果，可冰冻敌人，对火系敌人效果显著  
- **🌍 土元素**：高血量，可创造泥沼，对风系敌人效果显著
- **💨 风元素**：攻击速度快，可击退敌人，对土系敌人效果显著

### 融合组合
- **蒸汽塔** (火+水)：范围伤害，蒸汽效果
- **熔岩塔** (火+土)：超高伤害，熔岩地形
- **爆炸塔** (火+风)：爆炸伤害，击退效果
- **泥沼塔** (水+土)：强力减速，困住敌人
- **冰霜塔** (水+风)：冰冻效果，范围减速
- **沙暴塔** (土+风)：持续伤害，视野影响

## 🛠️ 技术特性

### 前端技术
- **HTML5 Canvas**：高性能2D渲染
- **ES6 模块**：现代JavaScript架构
- **CSS Grid/Flexbox**：响应式布局
- **Web Audio API**：音效系统

### 移动端优化
- **触摸控制**：完整的触摸事件支持
- **响应式设计**：适配各种屏幕尺寸
- **性能优化**：针对移动设备的渲染优化

### 数据持久化
- **本地存储**：使用localStorage保存游戏进度
- **状态管理**：完整的游戏状态序列化
- **设置保存**：用户偏好设置持久化

## 🚀 快速开始

### 运行游戏
1. 直接在浏览器中打开 `index.html`
2. 或者使用本地服务器：
   ```bash
   # 使用Python
   python -m http.server 8000
   
   # 使用Node.js
   npx serve .
   ```

### 游戏控制
- **鼠标/触摸**：选择和建造
- **键盘快捷键**：
  - `P` - 暂停/继续
  - `R` - 重新开始
  - `G` - 显示/隐藏网格
  - `ESC` - 取消当前操作

## 📁 项目结构

```
元素守护者/
├── index.html              # 主页面
├── styles/                 # 样式文件
│   ├── main.css           # 主样式
│   ├── ui.css             # UI组件样式
│   └── mobile.css         # 移动端适配
├── js/                    # JavaScript文件
│   ├── main.js            # 游戏入口
│   ├── core/              # 核心系统
│   │   ├── GameEngine.js  # 游戏引擎
│   │   ├── Renderer.js    # 渲染系统
│   │   ├── InputManager.js # 输入管理
│   │   └── AudioManager.js # 音频管理
│   ├── systems/           # 游戏系统
│   │   ├── TowerManager.js    # 塔管理
│   │   ├── EnemyManager.js    # 敌人管理
│   │   ├── ProjectileManager.js # 子弹管理
│   │   ├── WaveManager.js     # 波次管理
│   │   ├── TerrainManager.js  # 地形管理
│   │   ├── SaveManager.js     # 存档管理
│   │   └── UIManager.js       # UI管理
│   └── utils/             # 工具类
│       ├── Vector2.js     # 2D向量
│       └── GameState.js   # 游戏状态
└── README.md              # 说明文档
```

## 🎨 游戏截图

游戏界面包含：
- 游戏画布区域
- 塔建造面板
- 资源显示区
- 波次信息
- 设置面板

## 🔧 开发说明

### 代码架构
- **模块化设计**：每个系统独立管理
- **事件驱动**：松耦合的组件通信
- **状态管理**：集中式游戏状态管理

### 扩展性
- **插件系统**：易于添加新的塔类型和敌人
- **配置驱动**：游戏平衡参数可配置
- **国际化支持**：多语言界面支持

## 📝 更新日志

### v1.0.0 (当前版本)
- ✅ 基础塔防系统
- ✅ 四元素系统
- ✅ 融合机制
- ✅ 敌人AI系统
- ✅ 动态地形
- ✅ 移动端适配
- ✅ 本地存储

### 计划功能
- 🔄 更多融合组合
- 🔄 Boss敌人
- 🔄 成就系统
- 🔄 排行榜
- 🔄 多人模式

## 🤝 贡献

欢迎提交Issue和Pull Request来改进游戏！

## 📄 许可证

MIT License - 详见LICENSE文件

---

**享受游戏，成为真正的元素守护者！** 🎮✨
