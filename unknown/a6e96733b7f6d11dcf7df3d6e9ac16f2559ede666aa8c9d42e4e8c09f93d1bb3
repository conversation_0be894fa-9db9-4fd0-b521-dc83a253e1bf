/**
 * 二维向量类
 * 用于处理位置、速度、方向等二维数据
 */

export class Vector2 {
    constructor(x = 0, y = 0) {
        this.x = x;
        this.y = y;
    }

    /**
     * 设置向量值
     */
    set(x, y) {
        this.x = x;
        this.y = y;
        return this;
    }

    /**
     * 复制另一个向量
     */
    copy(vector) {
        this.x = vector.x;
        this.y = vector.y;
        return this;
    }

    /**
     * 克隆向量
     */
    clone() {
        return new Vector2(this.x, this.y);
    }

    /**
     * 向量加法
     */
    add(vector) {
        this.x += vector.x;
        this.y += vector.y;
        return this;
    }

    /**
     * 向量减法
     */
    subtract(vector) {
        this.x -= vector.x;
        this.y -= vector.y;
        return this;
    }

    /**
     * 向量乘法（标量）
     */
    multiply(scalar) {
        this.x *= scalar;
        this.y *= scalar;
        return this;
    }

    /**
     * 向量除法（标量）
     */
    divide(scalar) {
        if (scalar !== 0) {
            this.x /= scalar;
            this.y /= scalar;
        }
        return this;
    }

    /**
     * 计算向量长度
     */
    length() {
        return Math.sqrt(this.x * this.x + this.y * this.y);
    }

    /**
     * 计算向量长度的平方（避免开方运算）
     */
    lengthSquared() {
        return this.x * this.x + this.y * this.y;
    }

    /**
     * 归一化向量
     */
    normalize() {
        const length = this.length();
        if (length > 0) {
            this.divide(length);
        }
        return this;
    }

    /**
     * 获取归一化后的向量（不修改原向量）
     */
    normalized() {
        return this.clone().normalize();
    }

    /**
     * 计算向量角度（弧度）
     */
    angle() {
        return Math.atan2(this.y, this.x);
    }

    /**
     * 旋转向量
     */
    rotate(angle) {
        const cos = Math.cos(angle);
        const sin = Math.sin(angle);
        const x = this.x * cos - this.y * sin;
        const y = this.x * sin + this.y * cos;
        this.x = x;
        this.y = y;
        return this;
    }

    /**
     * 限制向量长度
     */
    limit(max) {
        const lengthSq = this.lengthSquared();
        if (lengthSq > max * max) {
            this.normalize().multiply(max);
        }
        return this;
    }

    /**
     * 线性插值
     */
    lerp(target, t) {
        this.x += (target.x - this.x) * t;
        this.y += (target.y - this.y) * t;
        return this;
    }

    /**
     * 检查向量是否为零向量
     */
    isZero() {
        return this.x === 0 && this.y === 0;
    }

    /**
     * 转换为字符串
     */
    toString() {
        return `Vector2(${this.x.toFixed(2)}, ${this.y.toFixed(2)})`;
    }

    // 静态方法

    /**
     * 计算两个向量的距离
     */
    static distance(v1, v2) {
        const dx = v2.x - v1.x;
        const dy = v2.y - v1.y;
        return Math.sqrt(dx * dx + dy * dy);
    }

    /**
     * 计算两个向量距离的平方
     */
    static distanceSquared(v1, v2) {
        const dx = v2.x - v1.x;
        const dy = v2.y - v1.y;
        return dx * dx + dy * dy;
    }

    /**
     * 向量点积
     */
    static dot(v1, v2) {
        return v1.x * v2.x + v1.y * v2.y;
    }

    /**
     * 向量叉积（2D中返回标量）
     */
    static cross(v1, v2) {
        return v1.x * v2.y - v1.y * v2.x;
    }

    /**
     * 向量加法（静态）
     */
    static add(v1, v2) {
        return new Vector2(v1.x + v2.x, v1.y + v2.y);
    }

    /**
     * 向量减法（静态）
     */
    static subtract(v1, v2) {
        return new Vector2(v1.x - v2.x, v1.y - v2.y);
    }

    /**
     * 向量乘法（静态）
     */
    static multiply(vector, scalar) {
        return new Vector2(vector.x * scalar, vector.y * scalar);
    }

    /**
     * 线性插值（静态）
     */
    static lerp(v1, v2, t) {
        return new Vector2(
            v1.x + (v2.x - v1.x) * t,
            v1.y + (v2.y - v1.y) * t
        );
    }

    /**
     * 从角度创建单位向量
     */
    static fromAngle(angle) {
        return new Vector2(Math.cos(angle), Math.sin(angle));
    }

    /**
     * 随机单位向量
     */
    static random() {
        const angle = Math.random() * Math.PI * 2;
        return Vector2.fromAngle(angle);
    }

    /**
     * 零向量
     */
    static zero() {
        return new Vector2(0, 0);
    }

    /**
     * 单位向量（右）
     */
    static right() {
        return new Vector2(1, 0);
    }

    /**
     * 单位向量（上）
     */
    static up() {
        return new Vector2(0, -1);
    }

    /**
     * 单位向量（左）
     */
    static left() {
        return new Vector2(-1, 0);
    }

    /**
     * 单位向量（下）
     */
    static down() {
        return new Vector2(0, 1);
    }
}