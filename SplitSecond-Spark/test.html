<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SplitSecond Spark - 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .test-section h2 {
            margin-top: 0;
            color: #4ecdc4;
        }
        
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .test-item:last-child {
            border-bottom: none;
        }
        
        .status {
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9em;
        }
        
        .status.pass {
            background: #27ae60;
            color: white;
        }
        
        .status.fail {
            background: #e74c3c;
            color: white;
        }
        
        .status.warning {
            background: #f39c12;
            color: white;
        }
        
        .game-link {
            text-align: center;
            margin: 30px 0;
        }
        
        .game-link a {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-size: 1.2em;
            font-weight: bold;
            transition: transform 0.3s;
        }
        
        .game-link a:hover {
            transform: scale(1.05);
        }
        
        .info {
            background: rgba(52, 152, 219, 0.2);
            border: 1px solid rgba(52, 152, 219, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .error-log {
            background: rgba(231, 76, 60, 0.2);
            border: 1px solid rgba(231, 76, 60, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 0.9em;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 SplitSecond Spark 测试页面</h1>
        
        <div class="info">
            <strong>📋 测试说明：</strong>
            此页面用于检测游戏的基本功能和兼容性。请确保所有测试项目都显示为"通过"状态，然后点击下方链接开始游戏。
        </div>
        
        <div class="test-section">
            <h2>🌐 浏览器兼容性检测</h2>
            <div id="browser-tests"></div>
        </div>
        
        <div class="test-section">
            <h2>📱 设备功能检测</h2>
            <div id="device-tests"></div>
        </div>
        
        <div class="test-section">
            <h2>🎨 Canvas支持检测</h2>
            <div id="canvas-tests"></div>
        </div>
        
        <div class="test-section">
            <h2>💾 存储功能检测</h2>
            <div id="storage-tests"></div>
        </div>
        
        <div class="test-section">
            <h2>🎯 游戏资源检测</h2>
            <div id="resource-tests"></div>
        </div>
        
        <div id="error-section" style="display: none;">
            <div class="test-section">
                <h2>❌ 错误日志</h2>
                <div id="error-log" class="error-log"></div>
            </div>
        </div>
        
        <div class="game-link">
            <a href="index.html" id="game-link">🚀 开始游戏</a>
        </div>
    </div>

    <script>
        // 错误收集
        const errors = [];
        
        window.addEventListener('error', (e) => {
            errors.push(`错误: ${e.message} (${e.filename}:${e.lineno})`);
            updateErrorLog();
        });
        
        function updateErrorLog() {
            const errorSection = document.getElementById('error-section');
            const errorLog = document.getElementById('error-log');
            
            if (errors.length > 0) {
                errorSection.style.display = 'block';
                errorLog.innerHTML = errors.map(error => `<div>${error}</div>`).join('');
            }
        }
        
        function createTestItem(name, status, message = '') {
            return `
                <div class="test-item">
                    <span>${name}</span>
                    <span class="status ${status}">${getStatusText(status)} ${message}</span>
                </div>
            `;
        }
        
        function getStatusText(status) {
            switch(status) {
                case 'pass': return '✅ 通过';
                case 'fail': return '❌ 失败';
                case 'warning': return '⚠️ 警告';
                default: return '❓ 未知';
            }
        }
        
        // 浏览器兼容性检测
        function testBrowserCompatibility() {
            const tests = [];
            
            // ES6支持
            try {
                eval('const test = () => {};');
                tests.push(createTestItem('ES6语法支持', 'pass'));
            } catch(e) {
                tests.push(createTestItem('ES6语法支持', 'fail', '需要现代浏览器'));
            }
            
            // Canvas支持
            const canvas = document.createElement('canvas');
            if (canvas.getContext && canvas.getContext('2d')) {
                tests.push(createTestItem('Canvas 2D支持', 'pass'));
            } else {
                tests.push(createTestItem('Canvas 2D支持', 'fail'));
            }
            
            // requestAnimationFrame支持
            if (window.requestAnimationFrame) {
                tests.push(createTestItem('动画帧支持', 'pass'));
            } else {
                tests.push(createTestItem('动画帧支持', 'fail'));
            }
            
            // 触摸事件支持
            if ('ontouchstart' in window) {
                tests.push(createTestItem('触摸事件支持', 'pass'));
            } else {
                tests.push(createTestItem('触摸事件支持', 'warning', '仅支持鼠标'));
            }
            
            document.getElementById('browser-tests').innerHTML = tests.join('');
        }
        
        // 设备功能检测
        function testDeviceFeatures() {
            const tests = [];
            
            // 屏幕尺寸
            const width = window.innerWidth;
            const height = window.innerHeight;
            if (width >= 800 && height >= 600) {
                tests.push(createTestItem('屏幕尺寸', 'pass', `${width}x${height}`));
            } else {
                tests.push(createTestItem('屏幕尺寸', 'warning', `${width}x${height} (较小)`));
            }
            
            // 设备类型检测
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            tests.push(createTestItem('设备类型', 'pass', isMobile ? '移动设备' : '桌面设备'));
            
            // 性能检测
            const start = performance.now();
            for(let i = 0; i < 100000; i++) {
                Math.random();
            }
            const end = performance.now();
            const time = end - start;
            
            if (time < 10) {
                tests.push(createTestItem('性能测试', 'pass', `${time.toFixed(2)}ms`));
            } else if (time < 50) {
                tests.push(createTestItem('性能测试', 'warning', `${time.toFixed(2)}ms`));
            } else {
                tests.push(createTestItem('性能测试', 'fail', `${time.toFixed(2)}ms (过慢)`));
            }
            
            document.getElementById('device-tests').innerHTML = tests.join('');
        }
        
        // Canvas功能检测
        function testCanvasFeatures() {
            const tests = [];
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            if (ctx) {
                // 基本绘制
                try {
                    ctx.fillRect(0, 0, 10, 10);
                    tests.push(createTestItem('基本绘制', 'pass'));
                } catch(e) {
                    tests.push(createTestItem('基本绘制', 'fail'));
                }
                
                // 渐变支持
                try {
                    const gradient = ctx.createLinearGradient(0, 0, 100, 0);
                    gradient.addColorStop(0, 'red');
                    gradient.addColorStop(1, 'blue');
                    tests.push(createTestItem('渐变支持', 'pass'));
                } catch(e) {
                    tests.push(createTestItem('渐变支持', 'fail'));
                }
                
                // 变换支持
                try {
                    ctx.save();
                    ctx.translate(10, 10);
                    ctx.rotate(0.5);
                    ctx.scale(2, 2);
                    ctx.restore();
                    tests.push(createTestItem('变换支持', 'pass'));
                } catch(e) {
                    tests.push(createTestItem('变换支持', 'fail'));
                }
            } else {
                tests.push(createTestItem('Canvas上下文', 'fail'));
            }
            
            document.getElementById('canvas-tests').innerHTML = tests.join('');
        }
        
        // 存储功能检测
        function testStorageFeatures() {
            const tests = [];
            
            // localStorage支持
            try {
                localStorage.setItem('test', 'value');
                const value = localStorage.getItem('test');
                localStorage.removeItem('test');
                
                if (value === 'value') {
                    tests.push(createTestItem('本地存储', 'pass'));
                } else {
                    tests.push(createTestItem('本地存储', 'fail'));
                }
            } catch(e) {
                tests.push(createTestItem('本地存储', 'fail', '存储被禁用'));
            }
            
            // JSON支持
            try {
                const obj = {test: 'value'};
                const json = JSON.stringify(obj);
                const parsed = JSON.parse(json);
                
                if (parsed.test === 'value') {
                    tests.push(createTestItem('JSON序列化', 'pass'));
                } else {
                    tests.push(createTestItem('JSON序列化', 'fail'));
                }
            } catch(e) {
                tests.push(createTestItem('JSON序列化', 'fail'));
            }
            
            document.getElementById('storage-tests').innerHTML = tests.join('');
        }
        
        // 游戏资源检测
        function testGameResources() {
            const tests = [];
            const resources = [
                'css/main.css',
                'css/game.css',
                'css/ui.css',
                'css/effects.css',
                'js/utils.js',
                'js/game.js',
                'manifest.json'
            ];
            
            let completed = 0;
            const total = resources.length;
            
            resources.forEach(resource => {
                fetch(resource)
                    .then(response => {
                        if (response.ok) {
                            tests.push(createTestItem(resource, 'pass'));
                        } else {
                            tests.push(createTestItem(resource, 'fail', `HTTP ${response.status}`));
                        }
                    })
                    .catch(error => {
                        tests.push(createTestItem(resource, 'fail', '加载失败'));
                    })
                    .finally(() => {
                        completed++;
                        if (completed === total) {
                            document.getElementById('resource-tests').innerHTML = tests.join('');
                        }
                    });
            });
        }
        
        // 运行所有测试
        function runAllTests() {
            testBrowserCompatibility();
            testDeviceFeatures();
            testCanvasFeatures();
            testStorageFeatures();
            testGameResources();
        }
        
        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', runAllTests);
        
        // 检查游戏链接状态
        document.getElementById('game-link').addEventListener('click', function(e) {
            const failedTests = document.querySelectorAll('.status.fail');
            if (failedTests.length > 0) {
                if (!confirm('检测到一些功能不支持，游戏可能无法正常运行。是否仍要继续？')) {
                    e.preventDefault();
                }
            }
        });
    </script>
</body>
</html>
