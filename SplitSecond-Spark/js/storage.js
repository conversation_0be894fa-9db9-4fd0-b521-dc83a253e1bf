/**
 * 游戏存储管理器 - SplitSecond Spark
 * 负责游戏数据的保存和加载，使用localStorage实现持久化存储
 */

class GameStorage {
    constructor() {
        this.storageKey = 'splitsecond-spark-save';
        this.settingsKey = 'splitsecond-spark-settings';
        this.defaultSettings = {
            musicVolume: 70,
            sfxVolume: 80,
            graphicsQuality: 'medium',
            language: 'zh-CN'
        };
        
        // 初始化设置
        this.initializeSettings();
        
        DebugUtils.log('游戏存储管理器初始化完成');
    }

    /**
     * 初始化游戏设置
     */
    initializeSettings() {
        const savedSettings = this.loadSettings();
        if (!savedSettings) {
            this.saveSettings(this.defaultSettings);
            DebugUtils.log('创建默认游戏设置');
        }
    }

    /**
     * 保存游戏数据
     * @param {object} gameData - 游戏数据对象
     * @returns {boolean} 保存是否成功
     */
    saveGame(gameData) {
        try {
            const saveData = {
                version: '1.0.0',
                timestamp: TimeUtils.now(),
                data: gameData
            };

            const serializedData = JSON.stringify(saveData);
            localStorage.setItem(this.storageKey, serializedData);
            
            DebugUtils.log('游戏数据保存成功');
            return true;
        } catch (error) {
            DebugUtils.log(`游戏数据保存失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 加载游戏数据
     * @returns {object|null} 游戏数据对象或null
     */
    loadGame() {
        try {
            const serializedData = localStorage.getItem(this.storageKey);
            if (!serializedData) {
                DebugUtils.log('未找到保存的游戏数据');
                return null;
            }

            const saveData = JSON.parse(serializedData);
            
            // 验证数据格式
            if (!saveData.version || !saveData.data) {
                DebugUtils.log('游戏数据格式无效', 'warn');
                return null;
            }

            DebugUtils.log('游戏数据加载成功');
            return saveData.data;
        } catch (error) {
            DebugUtils.log(`游戏数据加载失败: ${error.message}`, 'error');
            return null;
        }
    }

    /**
     * 检查是否存在保存的游戏数据
     * @returns {boolean} 是否存在保存数据
     */
    hasSavedGame() {
        return localStorage.getItem(this.storageKey) !== null;
    }

    /**
     * 删除保存的游戏数据
     * @returns {boolean} 删除是否成功
     */
    deleteSavedGame() {
        try {
            localStorage.removeItem(this.storageKey);
            DebugUtils.log('游戏数据删除成功');
            return true;
        } catch (error) {
            DebugUtils.log(`游戏数据删除失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 保存游戏设置
     * @param {object} settings - 设置对象
     * @returns {boolean} 保存是否成功
     */
    saveSettings(settings) {
        try {
            const settingsData = {
                ...this.defaultSettings,
                ...settings,
                timestamp: TimeUtils.now()
            };

            const serializedSettings = JSON.stringify(settingsData);
            localStorage.setItem(this.settingsKey, serializedSettings);
            
            DebugUtils.log('游戏设置保存成功');
            return true;
        } catch (error) {
            DebugUtils.log(`游戏设置保存失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 加载游戏设置
     * @returns {object} 设置对象
     */
    loadSettings() {
        try {
            const serializedSettings = localStorage.getItem(this.settingsKey);
            if (!serializedSettings) {
                return this.defaultSettings;
            }

            const settings = JSON.parse(serializedSettings);
            
            // 合并默认设置，确保所有设置项都存在
            const mergedSettings = {
                ...this.defaultSettings,
                ...settings
            };

            DebugUtils.log('游戏设置加载成功');
            return mergedSettings;
        } catch (error) {
            DebugUtils.log(`游戏设置加载失败: ${error.message}`, 'error');
            return this.defaultSettings;
        }
    }

    /**
     * 重置游戏设置为默认值
     * @returns {boolean} 重置是否成功
     */
    resetSettings() {
        return this.saveSettings(this.defaultSettings);
    }

    /**
     * 获取存储使用情况
     * @returns {object} 存储使用情况对象
     */
    getStorageInfo() {
        try {
            const gameDataSize = localStorage.getItem(this.storageKey)?.length || 0;
            const settingsSize = localStorage.getItem(this.settingsKey)?.length || 0;
            const totalSize = gameDataSize + settingsSize;

            // 估算localStorage总容量（通常为5MB）
            const estimatedCapacity = 5 * 1024 * 1024; // 5MB in bytes
            const usagePercentage = (totalSize / estimatedCapacity) * 100;

            return {
                gameDataSize,
                settingsSize,
                totalSize,
                usagePercentage: Math.round(usagePercentage * 100) / 100,
                hasGameData: this.hasSavedGame()
            };
        } catch (error) {
            DebugUtils.log(`获取存储信息失败: ${error.message}`, 'error');
            return {
                gameDataSize: 0,
                settingsSize: 0,
                totalSize: 0,
                usagePercentage: 0,
                hasGameData: false
            };
        }
    }

    /**
     * 导出游戏数据（用于备份）
     * @returns {string|null} 导出的JSON字符串
     */
    exportGameData() {
        try {
            const gameData = this.loadGame();
            const settings = this.loadSettings();
            
            if (!gameData) {
                DebugUtils.log('没有游戏数据可导出', 'warn');
                return null;
            }

            const exportData = {
                version: '1.0.0',
                exportTime: TimeUtils.now(),
                gameData,
                settings
            };

            const exportString = JSON.stringify(exportData, null, 2);
            DebugUtils.log('游戏数据导出成功');
            return exportString;
        } catch (error) {
            DebugUtils.log(`游戏数据导出失败: ${error.message}`, 'error');
            return null;
        }
    }

    /**
     * 导入游戏数据（用于恢复备份）
     * @param {string} importString - 导入的JSON字符串
     * @returns {boolean} 导入是否成功
     */
    importGameData(importString) {
        try {
            const importData = JSON.parse(importString);
            
            // 验证导入数据格式
            if (!importData.version || !importData.gameData) {
                DebugUtils.log('导入数据格式无效', 'error');
                return false;
            }

            // 保存游戏数据
            const gameSuccess = this.saveGame(importData.gameData);
            
            // 保存设置（如果存在）
            let settingsSuccess = true;
            if (importData.settings) {
                settingsSuccess = this.saveSettings(importData.settings);
            }

            if (gameSuccess && settingsSuccess) {
                DebugUtils.log('游戏数据导入成功');
                return true;
            } else {
                DebugUtils.log('游戏数据导入部分失败', 'warn');
                return false;
            }
        } catch (error) {
            DebugUtils.log(`游戏数据导入失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 清理所有存储数据
     * @returns {boolean} 清理是否成功
     */
    clearAllData() {
        try {
            localStorage.removeItem(this.storageKey);
            localStorage.removeItem(this.settingsKey);
            DebugUtils.log('所有存储数据清理完成');
            return true;
        } catch (error) {
            DebugUtils.log(`存储数据清理失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 创建游戏数据的快照
     * @param {object} gameData - 游戏数据
     * @returns {object} 数据快照
     */
    createSnapshot(gameData) {
        return {
            timestamp: TimeUtils.now(),
            data: JSON.parse(JSON.stringify(gameData)) // 深拷贝
        };
    }
}

// 创建全局存储管理器实例
window.gameStorage = new GameStorage();
