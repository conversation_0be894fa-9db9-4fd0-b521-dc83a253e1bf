/**
 * 游戏实体系统 - SplitSecond Spark
 * 定义游戏中的各种实体对象和基础类
 */

class Entity {
    constructor(x, y, options = {}) {
        this.id = options.id || this.generateId();
        this.x = x;
        this.y = y;
        this.width = options.width || 20;
        this.height = options.height || 20;
        this.radius = options.radius || Math.max(this.width, this.height) / 2;
        
        // 运动属性
        this.vx = 0;
        this.vy = 0;
        this.speed = options.speed || 100;
        this.maxSpeed = options.maxSpeed || 200;
        
        // 视觉属性
        this.color = options.color || '#ffffff';
        this.opacity = options.opacity || 1;
        this.rotation = options.rotation || 0;
        this.scale = options.scale || 1;
        
        // 状态属性
        this.active = true;
        this.visible = true;
        this.solid = options.solid !== undefined ? options.solid : true;
        
        // 类型和标签
        this.type = options.type || 'entity';
        this.tags = new Set(options.tags || []);
        
        // 生命周期
        this.created = TimeUtils.now();
        this.lastUpdate = this.created;
    }

    /**
     * 生成唯一ID
     * @returns {string} 唯一ID
     */
    generateId() {
        return 'entity_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
    }

    /**
     * 更新实体
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        if (!this.active) return;
        
        this.lastUpdate = TimeUtils.now();
        
        // 更新位置
        this.x += this.vx * deltaTime * 0.001;
        this.y += this.vy * deltaTime * 0.001;
        
        // 限制速度
        const currentSpeed = Math.sqrt(this.vx * this.vx + this.vy * this.vy);
        if (currentSpeed > this.maxSpeed) {
            this.vx = (this.vx / currentSpeed) * this.maxSpeed;
            this.vy = (this.vy / currentSpeed) * this.maxSpeed;
        }
    }

    /**
     * 渲染实体
     * @param {Renderer} renderer - 渲染器
     */
    render(renderer) {
        if (!this.visible) return;
        
        renderer.ctx.save();
        renderer.ctx.globalAlpha = this.opacity;
        renderer.ctx.translate(this.x, this.y);
        renderer.ctx.rotate(this.rotation);
        renderer.ctx.scale(this.scale, this.scale);
        
        this.draw(renderer);
        
        renderer.ctx.restore();
    }

    /**
     * 绘制实体（子类重写）
     * @param {Renderer} renderer - 渲染器
     */
    draw(renderer) {
        renderer.drawRect(-this.width/2, -this.height/2, this.width, this.height, this.color);
    }

    /**
     * 获取边界框
     * @returns {object} 边界框对象
     */
    getBounds() {
        return {
            left: this.x - this.width / 2,
            right: this.x + this.width / 2,
            top: this.y - this.height / 2,
            bottom: this.y + this.height / 2,
            centerX: this.x,
            centerY: this.y,
            width: this.width,
            height: this.height
        };
    }

    /**
     * 检查与另一个实体的碰撞
     * @param {Entity} other - 另一个实体
     * @returns {boolean} 是否碰撞
     */
    collidesWith(other) {
        if (!this.solid || !other.solid) return false;
        
        const distance = MathUtils.distance(this.x, this.y, other.x, other.y);
        return distance < (this.radius + other.radius);
    }

    /**
     * 检查矩形碰撞
     * @param {Entity} other - 另一个实体
     * @returns {boolean} 是否碰撞
     */
    rectCollidesWith(other) {
        if (!this.solid || !other.solid) return false;
        
        const bounds1 = this.getBounds();
        const bounds2 = other.getBounds();
        
        return !(bounds1.right < bounds2.left || 
                bounds1.left > bounds2.right || 
                bounds1.bottom < bounds2.top || 
                bounds1.top > bounds2.bottom);
    }

    /**
     * 设置位置
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     */
    setPosition(x, y) {
        this.x = x;
        this.y = y;
    }

    /**
     * 移动到目标位置
     * @param {number} targetX - 目标X坐标
     * @param {number} targetY - 目标Y坐标
     * @param {number} speed - 移动速度
     */
    moveTo(targetX, targetY, speed = this.speed) {
        const dx = targetX - this.x;
        const dy = targetY - this.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance > 0) {
            this.vx = (dx / distance) * speed;
            this.vy = (dy / distance) * speed;
        }
    }

    /**
     * 停止移动
     */
    stop() {
        this.vx = 0;
        this.vy = 0;
    }

    /**
     * 添加标签
     * @param {string} tag - 标签
     */
    addTag(tag) {
        this.tags.add(tag);
    }

    /**
     * 移除标签
     * @param {string} tag - 标签
     */
    removeTag(tag) {
        this.tags.delete(tag);
    }

    /**
     * 检查是否有标签
     * @param {string} tag - 标签
     * @returns {boolean} 是否有该标签
     */
    hasTag(tag) {
        return this.tags.has(tag);
    }

    /**
     * 销毁实体
     */
    destroy() {
        this.active = false;
        this.visible = false;
    }

    /**
     * 获取到另一个实体的距离
     * @param {Entity} other - 另一个实体
     * @returns {number} 距离
     */
    distanceTo(other) {
        return MathUtils.distance(this.x, this.y, other.x, other.y);
    }

    /**
     * 获取到另一个实体的角度
     * @param {Entity} other - 另一个实体
     * @returns {number} 角度（弧度）
     */
    angleTo(other) {
        return Math.atan2(other.y - this.y, other.x - this.x);
    }
}

class Obstacle extends Entity {
    constructor(x, y, options = {}) {
        super(x, y, {
            ...options,
            type: 'obstacle',
            solid: true
        });
        
        this.obstacleType = options.obstacleType || 'wall';
        this.destructible = options.destructible || false;
        this.health = options.health || 100;
        this.maxHealth = this.health;
    }

    /**
     * 绘制障碍物
     * @param {Renderer} renderer - 渲染器
     */
    draw(renderer) {
        const ctx = renderer.ctx;
        
        // 根据障碍物类型绘制不同形状
        switch (this.obstacleType) {
            case 'wall':
                this.drawWall(ctx);
                break;
            case 'crystal':
                this.drawCrystal(ctx);
                break;
            case 'ruins':
                this.drawRuins(ctx);
                break;
            default:
                renderer.drawRect(-this.width/2, -this.height/2, this.width, this.height, this.color);
        }
    }

    /**
     * 绘制墙壁
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    drawWall(ctx) {
        ctx.fillStyle = this.color;
        ctx.fillRect(-this.width/2, -this.height/2, this.width, this.height);
        
        // 添加边框
        ctx.strokeStyle = this.color.replace(/[^,]+(?=\))/, '0.8');
        ctx.lineWidth = 2;
        ctx.strokeRect(-this.width/2, -this.height/2, this.width, this.height);
    }

    /**
     * 绘制水晶
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    drawCrystal(ctx) {
        const sides = 6;
        const radius = this.radius;
        
        ctx.fillStyle = this.color;
        ctx.beginPath();
        
        for (let i = 0; i < sides; i++) {
            const angle = (i * 2 * Math.PI) / sides;
            const x = Math.cos(angle) * radius;
            const y = Math.sin(angle) * radius;
            
            if (i === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        }
        
        ctx.closePath();
        ctx.fill();
        
        // 添加发光效果
        const gradient = ctx.createRadialGradient(0, 0, 0, 0, 0, radius);
        gradient.addColorStop(0, this.color + '80');
        gradient.addColorStop(1, this.color + '00');
        
        ctx.fillStyle = gradient;
        ctx.fill();
    }

    /**
     * 绘制废墟
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    drawRuins(ctx) {
        ctx.fillStyle = this.color;
        
        // 绘制不规则形状
        ctx.beginPath();
        ctx.moveTo(-this.width/2, -this.height/2);
        ctx.lineTo(this.width/3, -this.height/2);
        ctx.lineTo(this.width/2, -this.height/4);
        ctx.lineTo(this.width/2, this.height/2);
        ctx.lineTo(-this.width/3, this.height/2);
        ctx.lineTo(-this.width/2, this.height/4);
        ctx.closePath();
        ctx.fill();
    }

    /**
     * 受到伤害
     * @param {number} damage - 伤害值
     * @returns {boolean} 是否被摧毁
     */
    takeDamage(damage) {
        if (!this.destructible) return false;
        
        this.health -= damage;
        
        if (this.health <= 0) {
            this.destroy();
            return true;
        }
        
        return false;
    }
}

class Portal extends Entity {
    constructor(x, y, options = {}) {
        super(x, y, {
            ...options,
            type: 'portal',
            solid: false
        });
        
        this.targetX = options.targetX || x;
        this.targetY = options.targetY || y;
        this.targetDimension = options.targetDimension || null;
        this.activated = false;
        this.cooldown = 1000; // 毫秒
        this.lastUsed = 0;
        
        this.animationPhase = 0;
    }

    /**
     * 更新传送门
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        super.update(deltaTime);
        
        this.animationPhase += deltaTime * 0.005;
        
        // 检查冷却时间
        if (this.activated && TimeUtils.now() - this.lastUsed > this.cooldown) {
            this.activated = false;
        }
    }

    /**
     * 绘制传送门
     * @param {Renderer} renderer - 渲染器
     */
    draw(renderer) {
        const ctx = renderer.ctx;
        const pulseSize = Math.sin(this.animationPhase) * 5 + this.radius;
        
        // 绘制外圈
        const gradient = ctx.createRadialGradient(0, 0, 0, 0, 0, pulseSize);
        gradient.addColorStop(0, this.color + '00');
        gradient.addColorStop(0.7, this.color + '80');
        gradient.addColorStop(1, this.color + '00');
        
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(0, 0, pulseSize, 0, Math.PI * 2);
        ctx.fill();
        
        // 绘制内圈
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(0, 0, this.radius * 0.6, 0, Math.PI * 2);
        ctx.fill();
    }

    /**
     * 使用传送门
     * @param {Entity} entity - 使用传送门的实体
     * @returns {boolean} 是否成功传送
     */
    use(entity) {
        if (this.activated) return false;
        
        this.activated = true;
        this.lastUsed = TimeUtils.now();
        
        // 传送实体
        entity.setPosition(this.targetX, this.targetY);
        
        // 如果有目标维度，切换维度
        if (this.targetDimension && window.timeDimension) {
            timeDimension.switchDimension(this.targetDimension);
        }
        
        return true;
    }
}

class Collectible extends Entity {
    constructor(x, y, options = {}) {
        super(x, y, {
            ...options,
            type: 'collectible',
            solid: false
        });
        
        this.value = options.value || 1;
        this.collectSound = options.collectSound || null;
        this.collected = false;
        
        this.bobPhase = Math.random() * Math.PI * 2;
        this.bobSpeed = options.bobSpeed || 0.003;
        this.bobHeight = options.bobHeight || 5;
        this.originalY = this.y;
    }

    /**
     * 更新收集品
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        if (this.collected) return;
        
        super.update(deltaTime);
        
        // 上下浮动效果
        this.bobPhase += deltaTime * this.bobSpeed;
        this.y = this.originalY + Math.sin(this.bobPhase) * this.bobHeight;
    }

    /**
     * 收集物品
     * @param {Entity} collector - 收集者
     * @returns {boolean} 是否成功收集
     */
    collect(collector) {
        if (this.collected) return false;
        
        this.collected = true;
        this.visible = false;
        this.active = false;
        
        // 播放收集音效
        if (this.collectSound) {
            // 播放音效逻辑
        }
        
        return true;
    }

    /**
     * 绘制收集品
     * @param {Renderer} renderer - 渲染器
     */
    draw(renderer) {
        if (this.collected) return;
        
        // 绘制发光效果
        renderer.drawGlow(0, 0, this.radius * 2, this.color, 0.6);
        
        // 绘制主体
        renderer.drawCircle(0, 0, this.radius, this.color);
        
        // 绘制闪烁效果
        const flickerIntensity = Math.sin(Date.now() * 0.01) * 0.3 + 0.7;
        renderer.drawGlow(0, 0, this.radius * 0.5, '#ffffff', flickerIntensity);
    }
}

// 导出实体类
window.Entity = Entity;
window.Obstacle = Obstacle;
window.Portal = Portal;
window.Collectible = Collectible;
