/**
 * 输入管理器 - SplitSecond Spark
 * 处理键盘、鼠标和触摸输入，支持PC和移动端
 */

class InputManager {
    constructor() {
        // 键盘状态
        this.keys = {};
        this.keysPressed = {};
        this.keysReleased = {};
        
        // 鼠标状态
        this.mouse = {
            x: 0,
            y: 0,
            buttons: {},
            buttonsPressed: {},
            buttonsReleased: {}
        };
        
        // 触摸状态
        this.touches = new Map();
        this.touchStarted = new Map();
        this.touchEnded = new Map();
        
        // 移动端虚拟按键状态
        this.virtualKeys = {};
        
        // 输入映射
        this.keyMappings = {
            // 移动控制
            'ArrowUp': 'up',
            'KeyW': 'up',
            'ArrowDown': 'down',
            'KeyS': 'down',
            'ArrowLeft': 'left',
            'KeyA': 'left',
            'ArrowRight': 'right',
            'KeyD': 'right',
            
            // 时间切换
            'Digit1': 'time-past',
            'Digit2': 'time-present',
            'Digit3': 'time-future',
            
            // 动作
            'Space': 'interact',
            'Enter': 'interact',
            'KeyE': 'interact',
            
            // 系统
            'Escape': 'pause',
            'KeyP': 'pause'
        };
        
        this.initializeEventListeners();
        DebugUtils.log('输入管理器初始化完成');
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        // 键盘事件
        document.addEventListener('keydown', this.handleKeyDown.bind(this));
        document.addEventListener('keyup', this.handleKeyUp.bind(this));
        
        // 鼠标事件
        document.addEventListener('mousedown', this.handleMouseDown.bind(this));
        document.addEventListener('mouseup', this.handleMouseUp.bind(this));
        document.addEventListener('mousemove', this.handleMouseMove.bind(this));
        
        // 触摸事件
        document.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: false });
        document.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false });
        document.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: false });
        
        // 移动端虚拟按键事件
        this.initializeMobileControls();

        // 检测设备类型
        this.isMobile = this.detectMobileDevice();

        // 防止默认的触摸行为（仅在游戏画布上）
        const canvas = document.getElementById('game-canvas');
        if (canvas) {
            canvas.addEventListener('touchstart', (e) => e.preventDefault(), { passive: false });
            canvas.addEventListener('touchmove', (e) => e.preventDefault(), { passive: false });
        }
    }

    /**
     * 检测移动设备
     * @returns {boolean} 是否为移动设备
     */
    detectMobileDevice() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               (navigator.maxTouchPoints && navigator.maxTouchPoints > 2) ||
               window.innerWidth <= 768;
    }

    /**
     * 初始化移动端控制器
     */
    initializeMobileControls() {
        // 方向按键
        const moveButtons = DOMUtils.$$('.move-btn');
        moveButtons.forEach(button => {
            const direction = button.dataset.direction;
            
            button.addEventListener('touchstart', (e) => {
                e.preventDefault();
                this.virtualKeys[direction] = true;
                DOMUtils.addClass(button, 'active');
            });
            
            button.addEventListener('touchend', (e) => {
                e.preventDefault();
                this.virtualKeys[direction] = false;
                DOMUtils.removeClass(button, 'active');
            });
        });
        
        // 动作按键
        const interactBtn = DOMUtils.$('#interact-btn');
        if (interactBtn) {
            interactBtn.addEventListener('touchstart', (e) => {
                e.preventDefault();
                this.virtualKeys['interact'] = true;
                DOMUtils.addClass(interactBtn, 'active');
            });
            
            interactBtn.addEventListener('touchend', (e) => {
                e.preventDefault();
                this.virtualKeys['interact'] = false;
                DOMUtils.removeClass(interactBtn, 'active');
            });
        }
        
        // 时空切换按键
        const timeSwitchBtn = DOMUtils.$('#time-switch-btn');
        if (timeSwitchBtn) {
            timeSwitchBtn.addEventListener('touchstart', (e) => {
                e.preventDefault();
                this.virtualKeys['time-switch'] = true;
                DOMUtils.addClass(timeSwitchBtn, 'active');
            });
            
            timeSwitchBtn.addEventListener('touchend', (e) => {
                e.preventDefault();
                this.virtualKeys['time-switch'] = false;
                DOMUtils.removeClass(timeSwitchBtn, 'active');
            });
        }
    }

    /**
     * 处理键盘按下事件
     * @param {KeyboardEvent} event - 键盘事件
     */
    handleKeyDown(event) {
        const key = event.code;
        
        if (!this.keys[key]) {
            this.keysPressed[key] = true;
        }
        
        this.keys[key] = true;
        
        // 阻止某些默认行为
        if (['Space', 'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(key)) {
            event.preventDefault();
        }
    }

    /**
     * 处理键盘释放事件
     * @param {KeyboardEvent} event - 键盘事件
     */
    handleKeyUp(event) {
        const key = event.code;
        this.keys[key] = false;
        this.keysReleased[key] = true;
    }

    /**
     * 处理鼠标按下事件
     * @param {MouseEvent} event - 鼠标事件
     */
    handleMouseDown(event) {
        const button = event.button;
        
        if (!this.mouse.buttons[button]) {
            this.mouse.buttonsPressed[button] = true;
        }
        
        this.mouse.buttons[button] = true;
        this.updateMousePosition(event);
    }

    /**
     * 处理鼠标释放事件
     * @param {MouseEvent} event - 鼠标事件
     */
    handleMouseUp(event) {
        const button = event.button;
        this.mouse.buttons[button] = false;
        this.mouse.buttonsReleased[button] = true;
        this.updateMousePosition(event);
    }

    /**
     * 处理鼠标移动事件
     * @param {MouseEvent} event - 鼠标事件
     */
    handleMouseMove(event) {
        this.updateMousePosition(event);
    }

    /**
     * 更新鼠标位置
     * @param {MouseEvent} event - 鼠标事件
     */
    updateMousePosition(event) {
        const canvas = DOMUtils.$('#game-canvas');
        if (canvas) {
            const rect = canvas.getBoundingClientRect();
            this.mouse.x = event.clientX - rect.left;
            this.mouse.y = event.clientY - rect.top;
        }
    }

    /**
     * 处理触摸开始事件
     * @param {TouchEvent} event - 触摸事件
     */
    handleTouchStart(event) {
        event.preventDefault();
        
        for (let touch of event.changedTouches) {
            const touchData = {
                id: touch.identifier,
                x: touch.clientX,
                y: touch.clientY,
                startX: touch.clientX,
                startY: touch.clientY,
                startTime: TimeUtils.now()
            };
            
            this.touches.set(touch.identifier, touchData);
            this.touchStarted.set(touch.identifier, touchData);
        }
    }

    /**
     * 处理触摸移动事件
     * @param {TouchEvent} event - 触摸事件
     */
    handleTouchMove(event) {
        event.preventDefault();
        
        for (let touch of event.changedTouches) {
            const touchData = this.touches.get(touch.identifier);
            if (touchData) {
                touchData.x = touch.clientX;
                touchData.y = touch.clientY;
            }
        }
    }

    /**
     * 处理触摸结束事件
     * @param {TouchEvent} event - 触摸事件
     */
    handleTouchEnd(event) {
        event.preventDefault();
        
        for (let touch of event.changedTouches) {
            const touchData = this.touches.get(touch.identifier);
            if (touchData) {
                touchData.endTime = TimeUtils.now();
                this.touchEnded.set(touch.identifier, touchData);
                this.touches.delete(touch.identifier);
            }
        }
    }

    /**
     * 检查按键是否被按住
     * @param {string} action - 动作名称
     * @returns {boolean} 是否被按住
     */
    isActionPressed(action) {
        // 检查键盘输入
        for (let [key, mappedAction] of Object.entries(this.keyMappings)) {
            if (mappedAction === action && this.keys[key]) {
                return true;
            }
        }
        
        // 检查虚拟按键
        return this.virtualKeys[action] || false;
    }

    /**
     * 检查按键是否刚被按下
     * @param {string} action - 动作名称
     * @returns {boolean} 是否刚被按下
     */
    isActionJustPressed(action) {
        // 检查键盘输入
        for (let [key, mappedAction] of Object.entries(this.keyMappings)) {
            if (mappedAction === action && this.keysPressed[key]) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 检查按键是否刚被释放
     * @param {string} action - 动作名称
     * @returns {boolean} 是否刚被释放
     */
    isActionJustReleased(action) {
        // 检查键盘输入
        for (let [key, mappedAction] of Object.entries(this.keyMappings)) {
            if (mappedAction === action && this.keysReleased[key]) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 获取移动输入向量
     * @returns {object} 移动向量 {x, y}
     */
    getMovementVector() {
        let x = 0;
        let y = 0;
        
        if (this.isActionPressed('left')) x -= 1;
        if (this.isActionPressed('right')) x += 1;
        if (this.isActionPressed('up')) y -= 1;
        if (this.isActionPressed('down')) y += 1;
        
        // 归一化对角线移动
        if (x !== 0 && y !== 0) {
            const length = Math.sqrt(x * x + y * y);
            x /= length;
            y /= length;
        }
        
        return { x, y };
    }

    /**
     * 更新输入状态（每帧调用）
     */
    update() {
        // 清除单帧输入状态
        this.keysPressed = {};
        this.keysReleased = {};
        this.mouse.buttonsPressed = {};
        this.mouse.buttonsReleased = {};
        this.touchStarted.clear();
        this.touchEnded.clear();
    }

    /**
     * 获取触摸手势信息
     * @param {number} touchId - 触摸ID
     * @returns {object|null} 手势信息
     */
    getTouchGesture(touchId) {
        const touchData = this.touchEnded.get(touchId);
        if (!touchData) return null;
        
        const deltaX = touchData.x - touchData.startX;
        const deltaY = touchData.y - touchData.startY;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
        const duration = touchData.endTime - touchData.startTime;
        
        // 判断手势类型
        if (distance < 10 && duration < 300) {
            return { type: 'tap', x: touchData.x, y: touchData.y };
        } else if (distance > 50) {
            const angle = Math.atan2(deltaY, deltaX);
            return {
                type: 'swipe',
                direction: this.getSwipeDirection(angle),
                distance,
                duration
            };
        }
        
        return null;
    }

    /**
     * 获取滑动方向
     * @param {number} angle - 角度（弧度）
     * @returns {string} 方向字符串
     */
    getSwipeDirection(angle) {
        const degrees = MathUtils.radToDeg(angle);
        
        if (degrees >= -45 && degrees < 45) return 'right';
        if (degrees >= 45 && degrees < 135) return 'down';
        if (degrees >= 135 || degrees < -135) return 'left';
        return 'up';
    }
}

// 创建全局输入管理器实例
window.inputManager = new InputManager();
