/**
 * 游戏主控制器 - SplitSecond Spark
 * 管理游戏的主要逻辑、状态和游戏循环
 */

class Game {
    constructor() {
        // 游戏状态
        this.state = 'loading'; // loading, menu, playing, paused, gameover
        this.isRunning = false;
        this.isPaused = false;
        
        // 游戏对象
        this.world = null;
        this.player = null;
        this.sparkSystem = null;
        
        // 时间管理
        this.lastTime = 0;
        this.deltaTime = 0;
        this.gameTime = 0;
        this.fps = 0;
        this.frameCount = 0;
        this.fpsUpdateTime = 0;
        
        // 游戏设置
        this.settings = {
            showFPS: false,
            showDebug: false,
            soundEnabled: true,
            musicEnabled: true,
            particleQuality: 'high' // low, medium, high
        };
        
        // 游戏统计
        this.stats = {
            playTime: 0,
            totalSparks: 0,
            bestScore: 0,
            dimensionSwitches: 0
        };
        
        this.initializeGame();
    }

    /**
     * 初始化游戏
     */
    async initializeGame() {
        try {
            DebugUtils.log('开始初始化游戏...');
            
            // 初始化渲染器
            if (!window.renderer) {
                throw new Error('渲染器未初始化');
            }
            
            // 初始化输入管理器
            if (!window.inputManager) {
                throw new Error('输入管理器未初始化');
            }
            
            // 初始化存储系统
            if (!window.storageManager) {
                throw new Error('存储管理器未初始化');
            }
            
            // 加载游戏设置
            this.loadSettings();
            
            // 创建游戏世界
            this.world = new GameWorld(1200, 800);
            window.gameWorld = this.world;
            
            // 创建玩家
            const spawnPosition = this.world.getRandomWalkablePosition(20) || { x: 100, y: 100 };
            this.player = new Player(spawnPosition.x, spawnPosition.y);
            this.world.addEntity(this.player);
            
            // 创建火花系统
            this.sparkSystem = new SparkSystem();
            
            // 初始化时间维度系统
            this.initializeTimeDimensions();
            
            // 设置事件监听器
            this.setupEventListeners();
            
            // 初始化UI
            this.initializeUI();
            
            this.state = 'menu';
            DebugUtils.log('游戏初始化完成');
            
        } catch (error) {
            DebugUtils.log(`游戏初始化失败: ${error.message}`, 'error');
            this.handleInitializationError(error);
        }
    }

    /**
     * 初始化时间维度
     */
    initializeTimeDimensions() {
        if (!window.timeDimension) return;
        
        // 为每个时间维度添加内容
        Object.keys(timeDimension.dimensions).forEach(dimensionId => {
            const dimension = timeDimension.dimensions[dimensionId];
            
            // 添加障碍物到世界
            dimension.obstacles.forEach(obstacleData => {
                const obstacle = new Obstacle(obstacleData.x, obstacleData.y, {
                    width: obstacleData.width,
                    height: obstacleData.height,
                    color: obstacleData.color,
                    obstacleType: obstacleData.type
                });
                
                // 只在当前维度显示
                if (dimensionId === timeDimension.currentDimension) {
                    this.world.addObstacle(obstacle);
                }
            });
            
            // 添加能量火花到世界
            dimension.sparks.forEach(sparkData => {
                if (!sparkData.collected) {
                    const spark = new EnergySpark(sparkData.x, sparkData.y, {
                        sparkType: sparkData.type,
                        color: sparkData.color,
                        value: sparkData.energy
                    });
                    
                    // 只在当前维度显示
                    if (dimensionId === timeDimension.currentDimension) {
                        this.world.addCollectible(spark);
                    }
                }
            });
        });
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 键盘事件
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        document.addEventListener('keyup', (e) => this.handleKeyUp(e));
        
        // 窗口事件
        window.addEventListener('blur', () => this.pauseGame());
        window.addEventListener('focus', () => this.resumeGame());
        window.addEventListener('resize', () => this.handleResize());
        
        // 游戏控制按钮
        const pauseBtn = DOMUtils.$('#pause-btn');
        if (pauseBtn) {
            pauseBtn.addEventListener('click', () => this.togglePause());
        }
        
        // 时间维度切换按钮
        const pastBtn = DOMUtils.$('#past-btn');
        const presentBtn = DOMUtils.$('#present-btn');
        const futureBtn = DOMUtils.$('#future-btn');
        
        if (pastBtn) pastBtn.addEventListener('click', () => this.switchTimeDimension('past'));
        if (presentBtn) presentBtn.addEventListener('click', () => this.switchTimeDimension('present'));
        if (futureBtn) futureBtn.addEventListener('click', () => this.switchTimeDimension('future'));
    }

    /**
     * 初始化UI
     */
    initializeUI() {
        this.updateUI();
        this.showMainMenu();
    }

    /**
     * 显示主菜单
     */
    showMainMenu() {
        const mainMenu = DOMUtils.$('#main-menu');
        const gameContainer = DOMUtils.$('#game-container');
        
        if (mainMenu) DOMUtils.show(mainMenu);
        if (gameContainer) DOMUtils.hide(gameContainer);
        
        // 设置菜单按钮事件
        const startBtn = DOMUtils.$('#start-game-btn');
        const continueBtn = DOMUtils.$('#continue-game-btn');
        const settingsBtn = DOMUtils.$('#settings-btn');
        
        if (startBtn) {
            startBtn.addEventListener('click', () => this.startNewGame());
        }
        
        if (continueBtn) {
            const hasSave = storageManager.hasSaveData();
            if (hasSave) {
                DOMUtils.show(continueBtn);
                continueBtn.addEventListener('click', () => this.continueGame());
            } else {
                DOMUtils.hide(continueBtn);
            }
        }
        
        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => this.showSettings());
        }
    }

    /**
     * 开始新游戏
     */
    startNewGame() {
        DebugUtils.log('开始新游戏');
        
        this.resetGame();
        this.state = 'playing';
        this.isRunning = true;
        
        this.hideMainMenu();
        this.showGameUI();
        this.startGameLoop();
    }

    /**
     * 继续游戏
     */
    continueGame() {
        DebugUtils.log('继续游戏');
        
        this.loadGame();
        this.state = 'playing';
        this.isRunning = true;
        
        this.hideMainMenu();
        this.showGameUI();
        this.startGameLoop();
    }

    /**
     * 重置游戏
     */
    resetGame() {
        // 重置玩家状态
        if (this.player) {
            const spawnPosition = this.world.getRandomWalkablePosition(20) || { x: 100, y: 100 };
            this.player.setPosition(spawnPosition.x, spawnPosition.y);
            this.player.health = this.player.maxHealth;
            this.player.energy = this.player.maxEnergy;
            this.player.sparksCollected = 0;
            this.player.score = 0;
        }
        
        // 重置时间维度
        if (window.timeDimension) {
            timeDimension.currentDimension = 'present';
        }
        
        // 重置统计
        this.stats.playTime = 0;
        this.stats.dimensionSwitches = 0;
        
        // 清空粒子
        if (window.particleSystem) {
            particleSystem.clear();
        }
        
        this.gameTime = 0;
    }

    /**
     * 隐藏主菜单
     */
    hideMainMenu() {
        const mainMenu = DOMUtils.$('#main-menu');
        if (mainMenu) DOMUtils.hide(mainMenu);
    }

    /**
     * 显示游戏UI
     */
    showGameUI() {
        const gameContainer = DOMUtils.$('#game-container');
        if (gameContainer) DOMUtils.show(gameContainer);
    }

    /**
     * 开始游戏循环
     */
    startGameLoop() {
        this.lastTime = performance.now();
        this.gameLoop();
    }

    /**
     * 游戏主循环
     */
    gameLoop() {
        if (!this.isRunning) return;
        
        const currentTime = performance.now();
        this.deltaTime = currentTime - this.lastTime;
        this.lastTime = currentTime;
        
        // 限制最大帧时间，防止游戏在长时间暂停后出现问题
        this.deltaTime = Math.min(this.deltaTime, 50);
        
        if (!this.isPaused) {
            this.update(this.deltaTime);
            this.render();
            
            this.gameTime += this.deltaTime;
            this.stats.playTime += this.deltaTime;
        }
        
        this.updateFPS();
        
        requestAnimationFrame(() => this.gameLoop());
    }

    /**
     * 更新游戏逻辑
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        // 更新输入
        if (window.inputManager) {
            inputManager.update(deltaTime);
        }
        
        // 更新世界
        if (this.world) {
            this.world.update(deltaTime);
        }
        
        // 更新时间维度系统
        if (window.timeDimension) {
            timeDimension.update(deltaTime);
        }
        
        // 更新火花系统
        if (this.sparkSystem) {
            this.sparkSystem.update(deltaTime);
        }
        
        // 更新粒子系统
        if (window.particleSystem) {
            particleSystem.update(deltaTime);
        }
        
        // 处理玩家交互
        this.handlePlayerInteractions();
        
        // 检查游戏结束条件
        this.checkGameEndConditions();
        
        // 自动保存
        this.handleAutoSave();
    }

    /**
     * 渲染游戏
     */
    render() {
        if (!window.renderer) return;
        
        // 清空画布
        renderer.clear();
        
        // 设置相机跟随玩家
        if (this.player) {
            renderer.setCamera(this.player.x, this.player.y);
        }
        
        // 渲染世界
        if (this.world) {
            this.world.render(renderer);
        }
        
        // 渲染粒子系统
        if (window.particleSystem) {
            particleSystem.render(renderer);
        }
        
        // 渲染UI覆盖层
        this.renderUI();
        
        // 渲染调试信息
        if (this.settings.showDebug) {
            this.renderDebugInfo();
        }
    }

    /**
     * 处理玩家交互
     */
    handlePlayerInteractions() {
        if (!this.player || !window.inputManager) return;
        
        // 检查交互按键
        if (inputManager.isActionPressed('interact')) {
            this.player.interact();
        }
        
        // 检查能力使用
        if (inputManager.isActionPressed('dash')) {
            this.player.useAbility('dash');
        }
        
        if (inputManager.isActionPressed('energyBurst')) {
            this.player.useAbility('energyBurst');
        }
    }

    /**
     * 切换时间维度
     * @param {string} dimension - 目标维度
     */
    switchTimeDimension(dimension) {
        if (!window.timeDimension) return;
        
        if (timeDimension.switchDimension(dimension)) {
            this.stats.dimensionSwitches++;
            
            // 重新加载当前维度的内容
            this.reloadDimensionContent();
            
            DebugUtils.log(`切换到时间维度: ${dimension}`);
        }
    }

    /**
     * 重新加载维度内容
     */
    reloadDimensionContent() {
        // 清空当前世界内容（保留玩家）
        this.world.obstacles = [];
        this.world.collectibles = [];
        
        // 重新加载当前维度的内容
        this.initializeTimeDimensions();
    }

    /**
     * 检查游戏结束条件
     */
    checkGameEndConditions() {
        if (!this.player) return;
        
        // 检查玩家死亡
        if (this.player.health <= 0) {
            this.gameOver();
        }
        
        // 检查胜利条件（收集足够的火花）
        if (this.player.sparksCollected >= 50) {
            this.gameWin();
        }
    }

    /**
     * 游戏结束
     */
    gameOver() {
        this.state = 'gameover';
        this.isRunning = false;
        
        DebugUtils.log('游戏结束');
        
        // 显示游戏结束界面
        this.showGameOverScreen();
    }

    /**
     * 游戏胜利
     */
    gameWin() {
        this.state = 'win';
        this.isRunning = false;
        
        // 更新最佳分数
        if (this.player.score > this.stats.bestScore) {
            this.stats.bestScore = this.player.score;
        }
        
        DebugUtils.log('游戏胜利！');
        
        // 显示胜利界面
        this.showWinScreen();
    }

    /**
     * 暂停游戏
     */
    pauseGame() {
        if (this.state === 'playing') {
            this.isPaused = true;
            this.showPauseMenu();
        }
    }

    /**
     * 恢复游戏
     */
    resumeGame() {
        if (this.isPaused) {
            this.isPaused = false;
            this.hidePauseMenu();
        }
    }

    /**
     * 切换暂停状态
     */
    togglePause() {
        if (this.isPaused) {
            this.resumeGame();
        } else {
            this.pauseGame();
        }
    }

    /**
     * 处理键盘按下事件
     * @param {KeyboardEvent} event - 键盘事件
     */
    handleKeyDown(event) {
        switch (event.code) {
            case 'Escape':
                this.togglePause();
                break;
            case 'F1':
                this.settings.showDebug = !this.settings.showDebug;
                break;
            case 'F2':
                this.settings.showFPS = !this.settings.showFPS;
                break;
        }
    }

    /**
     * 处理键盘释放事件
     * @param {KeyboardEvent} event - 键盘事件
     */
    handleKeyUp(event) {
        // 处理键盘释放事件
    }

    /**
     * 处理窗口大小改变
     */
    handleResize() {
        if (window.renderer) {
            renderer.handleResize();
        }
    }

    /**
     * 更新FPS计算
     */
    updateFPS() {
        this.frameCount++;
        const currentTime = performance.now();
        
        if (currentTime - this.fpsUpdateTime >= 1000) {
            this.fps = Math.round((this.frameCount * 1000) / (currentTime - this.fpsUpdateTime));
            this.frameCount = 0;
            this.fpsUpdateTime = currentTime;
        }
    }

    /**
     * 渲染UI
     */
    renderUI() {
        if (this.settings.showFPS) {
            this.renderFPS();
        }
    }

    /**
     * 渲染FPS显示
     */
    renderFPS() {
        const fpsElement = DOMUtils.$('#fps-display');
        if (fpsElement) {
            fpsElement.textContent = `FPS: ${this.fps}`;
            DOMUtils.show(fpsElement);
        }
    }

    /**
     * 渲染调试信息
     */
    renderDebugInfo() {
        const ctx = renderer.ctx;
        ctx.save();
        
        ctx.fillStyle = '#ffffff';
        ctx.font = '12px monospace';
        ctx.textAlign = 'left';
        
        const debugInfo = [
            `玩家位置: (${Math.round(this.player.x)}, ${Math.round(this.player.y)})`,
            `游戏时间: ${Math.round(this.gameTime / 1000)}s`,
            `实体数量: ${this.world.entities.size}`,
            `粒子数量: ${particleSystem.getParticleCount()}`,
            `当前维度: ${timeDimension.currentDimension}`
        ];
        
        debugInfo.forEach((info, index) => {
            ctx.fillText(info, 10, 30 + index * 15);
        });
        
        ctx.restore();
    }

    /**
     * 更新UI显示
     */
    updateUI() {
        if (!this.player) return;
        
        // 更新生命值
        const healthBar = DOMUtils.$('#health-bar');
        if (healthBar) {
            const healthPercent = (this.player.health / this.player.maxHealth) * 100;
            healthBar.style.width = `${healthPercent}%`;
        }
        
        // 更新能量值
        const energyBar = DOMUtils.$('#energy-bar');
        if (energyBar) {
            const energyPercent = (this.player.energy / this.player.maxEnergy) * 100;
            energyBar.style.width = `${energyPercent}%`;
        }
        
        // 更新分数
        const scoreElement = DOMUtils.$('#score');
        if (scoreElement) {
            scoreElement.textContent = this.player.score;
        }
        
        // 更新火花数量
        const sparkCountElement = DOMUtils.$('#spark-count');
        if (sparkCountElement) {
            sparkCountElement.textContent = this.player.sparksCollected;
        }
    }

    /**
     * 自动保存处理
     */
    handleAutoSave() {
        // 每30秒自动保存一次
        if (this.gameTime % 30000 < this.deltaTime) {
            this.saveGame();
        }
    }

    /**
     * 保存游戏
     */
    saveGame() {
        if (!window.storageManager) return;
        
        const gameData = {
            player: this.player.getState(),
            world: this.world.getState(),
            timeDimension: timeDimension.getSaveData(),
            stats: this.stats,
            gameTime: this.gameTime,
            timestamp: Date.now()
        };
        
        storageManager.saveGame(gameData);
        DebugUtils.log('游戏已保存');
    }

    /**
     * 加载游戏
     */
    loadGame() {
        if (!window.storageManager) return;
        
        const gameData = storageManager.loadGame();
        if (!gameData) return;
        
        // 恢复玩家状态
        if (gameData.player && this.player) {
            this.player.setState(gameData.player);
        }
        
        // 恢复时间维度状态
        if (gameData.timeDimension && window.timeDimension) {
            timeDimension.loadSaveData(gameData.timeDimension);
        }
        
        // 恢复统计数据
        if (gameData.stats) {
            Object.assign(this.stats, gameData.stats);
        }
        
        // 恢复游戏时间
        if (gameData.gameTime) {
            this.gameTime = gameData.gameTime;
        }
        
        DebugUtils.log('游戏已加载');
    }

    /**
     * 加载设置
     */
    loadSettings() {
        if (!window.storageManager) return;
        
        const settings = storageManager.loadSettings();
        if (settings) {
            Object.assign(this.settings, settings);
        }
    }

    /**
     * 保存设置
     */
    saveSettings() {
        if (!window.storageManager) return;
        
        storageManager.saveSettings(this.settings);
    }

    /**
     * 显示暂停菜单
     */
    showPauseMenu() {
        const pauseMenu = DOMUtils.$('#pause-menu');
        if (pauseMenu) DOMUtils.show(pauseMenu);
    }

    /**
     * 隐藏暂停菜单
     */
    hidePauseMenu() {
        const pauseMenu = DOMUtils.$('#pause-menu');
        if (pauseMenu) DOMUtils.hide(pauseMenu);
    }

    /**
     * 显示游戏结束界面
     */
    showGameOverScreen() {
        // 实现游戏结束界面显示逻辑
        DebugUtils.log('显示游戏结束界面');
    }

    /**
     * 显示胜利界面
     */
    showWinScreen() {
        // 实现胜利界面显示逻辑
        DebugUtils.log('显示胜利界面');
    }

    /**
     * 显示设置界面
     */
    showSettings() {
        // 实现设置界面显示逻辑
        DebugUtils.log('显示设置界面');
    }

    /**
     * 处理初始化错误
     * @param {Error} error - 错误对象
     */
    handleInitializationError(error) {
        const errorMessage = `游戏初始化失败: ${error.message}`;
        
        // 显示错误信息给用户
        const errorElement = document.createElement('div');
        errorElement.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #ff4444;
            color: white;
            padding: 20px;
            border-radius: 10px;
            font-family: Arial, sans-serif;
            z-index: 10000;
        `;
        errorElement.textContent = errorMessage;
        document.body.appendChild(errorElement);
    }
}

// 创建全局游戏实例
window.game = null;

// 页面加载完成后初始化游戏
document.addEventListener('DOMContentLoaded', () => {
    window.game = new Game();
});
