/**
 * 能量火花系统 - SplitSecond Spark
 * 管理游戏中的能量火花生成、收集和特效
 */

class EnergySpark extends Collectible {
    constructor(x, y, options = {}) {
        super(x, y, {
            ...options,
            type: 'spark',
            radius: options.radius || 12,
            value: options.value || 10
        });
        
        // 火花类型
        this.sparkType = options.sparkType || 'time';
        this.energyType = this.getEnergyTypeBySparkType(this.sparkType);
        
        // 视觉属性
        this.baseColor = options.color || this.getColorByType(this.sparkType);
        this.color = this.baseColor;
        this.intensity = 1;
        this.pulseSpeed = options.pulseSpeed || 0.008;
        
        // 能量属性
        this.energyValue = options.energyValue || 20;
        this.specialEffect = options.specialEffect || null;
        
        // 动画属性
        this.rotationSpeed = MathUtils.random(-0.05, 0.05);
        this.orbitRadius = MathUtils.random(5, 15);
        this.orbitSpeed = MathUtils.random(0.002, 0.005);
        this.orbitPhase = Math.random() * Math.PI * 2;
        
        // 粒子效果
        this.particleTimer = 0;
        this.particleInterval = 200; // 毫秒
        
        // 磁性吸引
        this.magneticRange = 60;
        this.attractionForce = 0.3;
        this.isBeingAttracted = false;
        
        DebugUtils.log(`创建能量火花: ${this.sparkType} at (${x}, ${y})`);
    }

    /**
     * 根据火花类型获取能量类型
     * @param {string} sparkType - 火花类型
     * @returns {string} 能量类型
     */
    getEnergyTypeBySparkType(sparkType) {
        const typeMap = {
            'time': 'temporal',
            'space': 'spatial',
            'quantum': 'quantum',
            'void': 'void',
            'memory': 'psychic',
            'echo': 'resonance'
        };
        
        return typeMap[sparkType] || 'temporal';
    }

    /**
     * 根据类型获取颜色
     * @param {string} sparkType - 火花类型
     * @returns {string} 颜色值
     */
    getColorByType(sparkType) {
        const colorMap = {
            'time': '#00d4ff',
            'space': '#9b59b6',
            'quantum': '#e74c3c',
            'void': '#2c3e50',
            'memory': '#f39c12',
            'echo': '#1abc9c'
        };
        
        return colorMap[sparkType] || '#00d4ff';
    }

    /**
     * 更新能量火花
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        if (this.collected) return;
        
        super.update(deltaTime);
        
        this.updateAnimation(deltaTime);
        this.updateMagneticAttraction(deltaTime);
        this.updateParticleEffects(deltaTime);
        this.updateVisualEffects(deltaTime);
    }

    /**
     * 更新动画
     * @param {number} deltaTime - 时间增量
     */
    updateAnimation(deltaTime) {
        // 旋转动画
        this.rotation += this.rotationSpeed * deltaTime;
        
        // 轨道运动
        this.orbitPhase += this.orbitSpeed * deltaTime;
        const orbitX = Math.cos(this.orbitPhase) * this.orbitRadius * 0.1;
        const orbitY = Math.sin(this.orbitPhase) * this.orbitRadius * 0.1;
        
        this.x = this.startX + orbitX;
        this.y = this.startY + orbitY;
    }

    /**
     * 更新磁性吸引
     * @param {number} deltaTime - 时间增量
     */
    updateMagneticAttraction(deltaTime) {
        if (!window.gameWorld || !window.gameWorld.entities) return;
        
        // 寻找玩家
        let player = null;
        gameWorld.entities.forEach(entity => {
            if (entity.type === 'player') {
                player = entity;
            }
        });
        
        if (!player) return;
        
        const distance = this.distanceTo(player);
        
        if (distance < this.magneticRange) {
            this.isBeingAttracted = true;
            
            // 计算吸引力
            const attractionStrength = (this.magneticRange - distance) / this.magneticRange;
            const force = this.attractionForce * attractionStrength;
            
            // 向玩家移动
            const angle = this.angleTo(player);
            this.vx += Math.cos(angle) * force * deltaTime;
            this.vy += Math.sin(angle) * force * deltaTime;
            
            // 限制速度
            const maxAttractSpeed = 100;
            const currentSpeed = Math.sqrt(this.vx * this.vx + this.vy * this.vy);
            if (currentSpeed > maxAttractSpeed) {
                this.vx = (this.vx / currentSpeed) * maxAttractSpeed;
                this.vy = (this.vy / currentSpeed) * maxAttractSpeed;
            }
        } else {
            this.isBeingAttracted = false;
            // 逐渐减速
            this.vx *= 0.95;
            this.vy *= 0.95;
        }
    }

    /**
     * 更新粒子效果
     * @param {number} deltaTime - 时间增量
     */
    updateParticleEffects(deltaTime) {
        this.particleTimer += deltaTime;
        
        if (this.particleTimer >= this.particleInterval) {
            this.particleTimer = 0;
            this.createAmbientParticles();
        }
    }

    /**
     * 创建环境粒子
     */
    createAmbientParticles() {
        if (!window.particleSystem) return;
        
        const particleCount = MathUtils.randomInt(1, 3);
        
        for (let i = 0; i < particleCount; i++) {
            const angle = Math.random() * Math.PI * 2;
            const distance = MathUtils.random(5, 15);
            const particleX = this.x + Math.cos(angle) * distance;
            const particleY = this.y + Math.sin(angle) * distance;
            
            particleSystem.createParticle(particleX, particleY, {
                vx: Math.cos(angle) * MathUtils.random(0.5, 2),
                vy: Math.sin(angle) * MathUtils.random(0.5, 2),
                size: MathUtils.random(1, 3),
                color: this.baseColor,
                opacity: MathUtils.random(0.3, 0.8),
                life: MathUtils.random(500, 1000),
                type: 'spark',
                ay: -0.1
            });
        }
    }

    /**
     * 更新视觉效果
     * @param {number} deltaTime - 时间增量
     */
    updateVisualEffects(deltaTime) {
        // 脉冲效果
        this.bobPhase += deltaTime * this.pulseSpeed;
        this.intensity = 0.7 + Math.sin(this.bobPhase) * 0.3;
        
        // 根据吸引状态调整颜色
        if (this.isBeingAttracted) {
            const attractionIntensity = Math.sin(Date.now() * 0.02) * 0.2 + 0.8;
            this.color = ColorUtils.adjustBrightness(this.baseColor, attractionIntensity);
        } else {
            this.color = this.baseColor;
        }
    }

    /**
     * 收集火花
     * @param {Entity} collector - 收集者
     * @returns {boolean} 是否成功收集
     */
    collect(collector) {
        if (this.collected) return false;
        
        const success = super.collect(collector);
        
        if (success) {
            // 应用特殊效果
            this.applySpecialEffect(collector);
            
            // 创建收集特效
            this.createCollectionEffect();
            
            // 播放收集音效
            this.playCollectionSound();
            
            DebugUtils.log(`收集能量火花: ${this.sparkType}, 能量值: ${this.energyValue}`);
        }
        
        return success;
    }

    /**
     * 应用特殊效果
     * @param {Entity} collector - 收集者
     */
    applySpecialEffect(collector) {
        switch (this.sparkType) {
            case 'time':
                // 时间火花：恢复能量
                if (collector.energy !== undefined) {
                    collector.energy = Math.min(collector.energy + this.energyValue, collector.maxEnergy);
                }
                break;
                
            case 'space':
                // 空间火花：提升移动速度
                collector.addStatusEffect('speed_boost', {
                    duration: 5000,
                    type: 'movement',
                    speedMultiplier: 1.3
                });
                break;
                
            case 'quantum':
                // 量子火花：解锁能力
                if (collector.abilities && collector.abilities.dash) {
                    collector.abilities.dash.unlocked = true;
                }
                break;
                
            case 'void':
                // 虚空火花：护盾效果
                collector.addStatusEffect('shield', {
                    duration: 10000,
                    type: 'protection',
                    absorption: 0.5
                });
                break;
                
            case 'memory':
                // 记忆火花：解锁时间维度
                if (window.timeDimension) {
                    timeDimension.unlockDimension('past');
                }
                break;
                
            case 'echo':
                // 回响火花：解锁未来维度
                if (window.timeDimension) {
                    timeDimension.unlockDimension('future');
                }
                break;
        }
    }

    /**
     * 创建收集特效
     */
    createCollectionEffect() {
        if (!window.particleSystem) return;
        
        // 爆发式粒子效果
        const particleCount = 20;
        
        for (let i = 0; i < particleCount; i++) {
            const angle = (i / particleCount) * Math.PI * 2;
            const speed = MathUtils.random(3, 8);
            
            particleSystem.createParticle(this.x, this.y, {
                vx: Math.cos(angle) * speed,
                vy: Math.sin(angle) * speed,
                size: MathUtils.random(2, 6),
                color: this.baseColor,
                life: MathUtils.random(800, 1200),
                type: 'explosion',
                rotationSpeed: MathUtils.random(-0.2, 0.2)
            });
        }
        
        // 中心闪光效果
        particleSystem.createParticle(this.x, this.y, {
            vx: 0,
            vy: 0,
            size: this.radius * 3,
            color: '#ffffff',
            life: 300,
            type: 'spark',
            ay: 0
        });
    }

    /**
     * 播放收集音效
     */
    playCollectionSound() {
        // 这里可以添加音效播放逻辑
        DebugUtils.log(`播放收集音效: ${this.sparkType}`);
    }

    /**
     * 绘制能量火花
     * @param {Renderer} renderer - 渲染器
     */
    draw(renderer) {
        if (this.collected) return;
        
        const ctx = renderer.ctx;
        const glowRadius = this.radius * (1 + this.intensity * 0.5);
        
        // 绘制外层发光
        renderer.drawGlow(0, 0, glowRadius * 3, this.color, this.intensity * 0.4);
        
        // 绘制中层发光
        renderer.drawGlow(0, 0, glowRadius * 1.5, this.color, this.intensity * 0.7);
        
        // 绘制主体
        const gradient = ctx.createRadialGradient(0, 0, 0, 0, 0, this.radius);
        gradient.addColorStop(0, '#ffffff');
        gradient.addColorStop(0.3, this.color);
        gradient.addColorStop(1, this.color + '80');
        
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(0, 0, this.radius, 0, Math.PI * 2);
        ctx.fill();
        
        // 绘制类型标识
        this.drawTypeIndicator(ctx);
        
        // 绘制能量环
        this.drawEnergyRing(ctx);
    }

    /**
     * 绘制类型标识
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    drawTypeIndicator(ctx) {
        const symbolSize = this.radius * 0.6;
        
        ctx.fillStyle = '#ffffff';
        ctx.font = `${symbolSize}px Arial`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        
        const symbols = {
            'time': '⧖',
            'space': '◊',
            'quantum': '⚛',
            'void': '●',
            'memory': '◐',
            'echo': '◑'
        };
        
        const symbol = symbols[this.sparkType] || '◯';
        ctx.fillText(symbol, 0, 0);
    }

    /**
     * 绘制能量环
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    drawEnergyRing(ctx) {
        const ringRadius = this.radius + 8;
        const ringWidth = 2;
        const segments = 12;
        const activeSegments = Math.floor(segments * this.intensity);
        
        for (let i = 0; i < segments; i++) {
            const angle = (i / segments) * Math.PI * 2 - Math.PI / 2;
            const startAngle = angle - (Math.PI / segments) * 0.4;
            const endAngle = angle + (Math.PI / segments) * 0.4;
            
            ctx.strokeStyle = i < activeSegments ? this.color : this.color + '30';
            ctx.lineWidth = ringWidth;
            ctx.beginPath();
            ctx.arc(0, 0, ringRadius, startAngle, endAngle);
            ctx.stroke();
        }
    }
}

class SparkSystem {
    constructor() {
        this.sparks = [];
        this.spawnTimer = 0;
        this.spawnInterval = 5000; // 5秒生成一个新火花
        this.maxSparks = 15;
        
        // 生成权重
        this.spawnWeights = {
            'time': 0.4,
            'space': 0.3,
            'quantum': 0.1,
            'void': 0.1,
            'memory': 0.05,
            'echo': 0.05
        };
        
        DebugUtils.log('能量火花系统初始化完成');
    }

    /**
     * 更新火花系统
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        // 更新现有火花
        this.sparks.forEach(spark => {
            spark.update(deltaTime);
        });
        
        // 清理已收集的火花
        this.sparks = this.sparks.filter(spark => !spark.collected);
        
        // 生成新火花
        this.spawnTimer += deltaTime;
        if (this.spawnTimer >= this.spawnInterval && this.sparks.length < this.maxSparks) {
            this.spawnRandomSpark();
            this.spawnTimer = 0;
        }
    }

    /**
     * 生成随机火花
     */
    spawnRandomSpark() {
        if (!window.gameWorld) return;
        
        const position = gameWorld.getRandomWalkablePosition(15);
        if (!position) return;
        
        const sparkType = this.getRandomSparkType();
        const spark = new EnergySpark(position.x, position.y, {
            sparkType: sparkType,
            value: this.getValueByType(sparkType),
            energyValue: this.getEnergyValueByType(sparkType)
        });
        
        this.sparks.push(spark);
        gameWorld.addCollectible(spark);
        
        DebugUtils.log(`生成新火花: ${sparkType} at (${position.x}, ${position.y})`);
    }

    /**
     * 获取随机火花类型
     * @returns {string} 火花类型
     */
    getRandomSparkType() {
        const random = Math.random();
        let cumulative = 0;
        
        for (let type in this.spawnWeights) {
            cumulative += this.spawnWeights[type];
            if (random <= cumulative) {
                return type;
            }
        }
        
        return 'time'; // 默认类型
    }

    /**
     * 根据类型获取分值
     * @param {string} sparkType - 火花类型
     * @returns {number} 分值
     */
    getValueByType(sparkType) {
        const valueMap = {
            'time': 10,
            'space': 15,
            'quantum': 25,
            'void': 30,
            'memory': 50,
            'echo': 50
        };
        
        return valueMap[sparkType] || 10;
    }

    /**
     * 根据类型获取能量值
     * @param {string} sparkType - 火花类型
     * @returns {number} 能量值
     */
    getEnergyValueByType(sparkType) {
        const energyMap = {
            'time': 20,
            'space': 15,
            'quantum': 30,
            'void': 25,
            'memory': 40,
            'echo': 40
        };
        
        return energyMap[sparkType] || 20;
    }

    /**
     * 获取火花数量
     * @returns {number} 火花数量
     */
    getSparkCount() {
        return this.sparks.length;
    }

    /**
     * 清空所有火花
     */
    clear() {
        this.sparks = [];
    }
}

// 导出类
window.EnergySpark = EnergySpark;
window.SparkSystem = SparkSystem;
