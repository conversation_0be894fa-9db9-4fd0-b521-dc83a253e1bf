/**
 * 游戏世界系统 - SplitSecond Spark
 * 管理游戏世界的地图、碰撞检测和空间查询
 */

class GameWorld {
    constructor(width = 1200, height = 800) {
        this.width = width;
        this.height = height;
        
        // 实体管理
        this.entities = new Map();
        this.obstacles = [];
        this.portals = [];
        this.collectibles = [];
        
        // 空间分割系统（用于优化碰撞检测）
        this.gridSize = 64;
        this.grid = new Map();
        
        // 世界边界
        this.bounds = {
            left: 0,
            right: width,
            top: 0,
            bottom: height
        };
        
        // 背景瓦片
        this.backgroundTiles = [];
        
        this.initializeWorld();
        DebugUtils.log('游戏世界初始化完成');
    }

    /**
     * 初始化世界
     */
    initializeWorld() {
        this.generateBackgroundTiles();
        this.createWorldBoundaries();
    }

    /**
     * 生成背景瓦片
     */
    generateBackgroundTiles() {
        const tileSize = 100;
        const tilesX = Math.ceil(this.width / tileSize);
        const tilesY = Math.ceil(this.height / tileSize);
        
        for (let x = 0; x < tilesX; x++) {
            for (let y = 0; y < tilesY; y++) {
                const tile = {
                    x: x * tileSize,
                    y: y * tileSize,
                    width: tileSize,
                    height: tileSize,
                    type: 'background',
                    color: this.getBackgroundTileColor(x, y)
                };
                
                this.backgroundTiles.push(tile);
            }
        }
    }

    /**
     * 获取背景瓦片颜色
     * @param {number} x - 瓦片X坐标
     * @param {number} y - 瓦片Y坐标
     * @returns {string} 颜色值
     */
    getBackgroundTileColor(x, y) {
        // 创建棋盘格效果
        const isEven = (x + y) % 2 === 0;
        return isEven ? '#1a1a2e20' : '#16213e20';
    }

    /**
     * 创建世界边界
     */
    createWorldBoundaries() {
        const borderThickness = 20;
        
        // 上边界
        this.addObstacle(new Obstacle(this.width / 2, -borderThickness / 2, {
            width: this.width,
            height: borderThickness,
            color: '#333333',
            obstacleType: 'wall'
        }));
        
        // 下边界
        this.addObstacle(new Obstacle(this.width / 2, this.height + borderThickness / 2, {
            width: this.width,
            height: borderThickness,
            color: '#333333',
            obstacleType: 'wall'
        }));
        
        // 左边界
        this.addObstacle(new Obstacle(-borderThickness / 2, this.height / 2, {
            width: borderThickness,
            height: this.height,
            color: '#333333',
            obstacleType: 'wall'
        }));
        
        // 右边界
        this.addObstacle(new Obstacle(this.width + borderThickness / 2, this.height / 2, {
            width: borderThickness,
            height: this.height,
            color: '#333333',
            obstacleType: 'wall'
        }));
    }

    /**
     * 添加实体到世界
     * @param {Entity} entity - 实体对象
     */
    addEntity(entity) {
        this.entities.set(entity.id, entity);
        this.updateEntityGrid(entity);
        
        // 根据实体类型添加到相应列表
        if (entity.type === 'obstacle') {
            this.obstacles.push(entity);
        } else if (entity.type === 'portal') {
            this.portals.push(entity);
        } else if (entity.type === 'collectible') {
            this.collectibles.push(entity);
        }
    }

    /**
     * 添加障碍物
     * @param {Obstacle} obstacle - 障碍物对象
     */
    addObstacle(obstacle) {
        this.addEntity(obstacle);
    }

    /**
     * 添加传送门
     * @param {Portal} portal - 传送门对象
     */
    addPortal(portal) {
        this.addEntity(portal);
    }

    /**
     * 添加收集品
     * @param {Collectible} collectible - 收集品对象
     */
    addCollectible(collectible) {
        this.addEntity(collectible);
    }

    /**
     * 移除实体
     * @param {string} entityId - 实体ID
     */
    removeEntity(entityId) {
        const entity = this.entities.get(entityId);
        if (!entity) return;
        
        this.entities.delete(entityId);
        this.removeFromGrid(entity);
        
        // 从相应列表中移除
        if (entity.type === 'obstacle') {
            const index = this.obstacles.indexOf(entity);
            if (index > -1) this.obstacles.splice(index, 1);
        } else if (entity.type === 'portal') {
            const index = this.portals.indexOf(entity);
            if (index > -1) this.portals.splice(index, 1);
        } else if (entity.type === 'collectible') {
            const index = this.collectibles.indexOf(entity);
            if (index > -1) this.collectibles.splice(index, 1);
        }
    }

    /**
     * 获取实体
     * @param {string} entityId - 实体ID
     * @returns {Entity|null} 实体对象
     */
    getEntity(entityId) {
        return this.entities.get(entityId) || null;
    }

    /**
     * 更新实体在网格中的位置
     * @param {Entity} entity - 实体对象
     */
    updateEntityGrid(entity) {
        // 移除旧位置
        this.removeFromGrid(entity);
        
        // 添加到新位置
        const gridX = Math.floor(entity.x / this.gridSize);
        const gridY = Math.floor(entity.y / this.gridSize);
        const gridKey = `${gridX},${gridY}`;
        
        if (!this.grid.has(gridKey)) {
            this.grid.set(gridKey, new Set());
        }
        
        this.grid.get(gridKey).add(entity);
        entity.gridKey = gridKey;
    }

    /**
     * 从网格中移除实体
     * @param {Entity} entity - 实体对象
     */
    removeFromGrid(entity) {
        if (entity.gridKey) {
            const gridCell = this.grid.get(entity.gridKey);
            if (gridCell) {
                gridCell.delete(entity);
                if (gridCell.size === 0) {
                    this.grid.delete(entity.gridKey);
                }
            }
            entity.gridKey = null;
        }
    }

    /**
     * 获取指定区域内的实体
     * @param {number} x - 中心X坐标
     * @param {number} y - 中心Y坐标
     * @param {number} radius - 搜索半径
     * @returns {Array} 实体数组
     */
    getEntitiesInRadius(x, y, radius) {
        const entities = [];
        const gridRadius = Math.ceil(radius / this.gridSize);
        const centerGridX = Math.floor(x / this.gridSize);
        const centerGridY = Math.floor(y / this.gridSize);
        
        for (let gx = centerGridX - gridRadius; gx <= centerGridX + gridRadius; gx++) {
            for (let gy = centerGridY - gridRadius; gy <= centerGridY + gridRadius; gy++) {
                const gridKey = `${gx},${gy}`;
                const gridCell = this.grid.get(gridKey);
                
                if (gridCell) {
                    gridCell.forEach(entity => {
                        const distance = MathUtils.distance(x, y, entity.x, entity.y);
                        if (distance <= radius) {
                            entities.push(entity);
                        }
                    });
                }
            }
        }
        
        return entities;
    }

    /**
     * 检查位置是否可通行
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {number} radius - 检查半径
     * @param {Entity} excludeEntity - 排除的实体
     * @returns {boolean} 是否可通行
     */
    isPositionWalkable(x, y, radius = 10, excludeEntity = null) {
        // 检查世界边界
        if (x - radius < this.bounds.left || x + radius > this.bounds.right ||
            y - radius < this.bounds.top || y + radius > this.bounds.bottom) {
            return false;
        }
        
        // 检查与障碍物的碰撞
        const nearbyEntities = this.getEntitiesInRadius(x, y, radius + 50);
        
        for (let entity of nearbyEntities) {
            if (entity === excludeEntity || !entity.solid) continue;
            
            const distance = MathUtils.distance(x, y, entity.x, entity.y);
            if (distance < radius + entity.radius) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 获取从起点到终点的路径
     * @param {number} startX - 起点X坐标
     * @param {number} startY - 起点Y坐标
     * @param {number} endX - 终点X坐标
     * @param {number} endY - 终点Y坐标
     * @param {number} radius - 路径宽度
     * @returns {Array} 路径点数组
     */
    findPath(startX, startY, endX, endY, radius = 10) {
        // 简单的直线路径检查
        const steps = 10;
        const dx = (endX - startX) / steps;
        const dy = (endY - startY) / steps;
        
        for (let i = 1; i <= steps; i++) {
            const checkX = startX + dx * i;
            const checkY = startY + dy * i;
            
            if (!this.isPositionWalkable(checkX, checkY, radius)) {
                // 如果直线路径被阻挡，返回空路径（可以在这里实现更复杂的寻路算法）
                return [];
            }
        }
        
        // 直线路径可通行
        return [{ x: endX, y: endY }];
    }

    /**
     * 处理实体移动和碰撞
     * @param {Entity} entity - 移动的实体
     * @param {number} newX - 新X坐标
     * @param {number} newY - 新Y坐标
     * @returns {object} 移动结果
     */
    moveEntity(entity, newX, newY) {
        const oldX = entity.x;
        const oldY = entity.y;
        
        // 检查X轴移动
        entity.x = newX;
        if (!this.isPositionWalkable(entity.x, entity.y, entity.radius, entity)) {
            entity.x = oldX; // 恢复X坐标
        }
        
        // 检查Y轴移动
        entity.y = newY;
        if (!this.isPositionWalkable(entity.x, entity.y, entity.radius, entity)) {
            entity.y = oldY; // 恢复Y坐标
        }
        
        // 更新网格位置
        this.updateEntityGrid(entity);
        
        return {
            moved: entity.x !== oldX || entity.y !== oldY,
            finalX: entity.x,
            finalY: entity.y
        };
    }

    /**
     * 检查实体间的碰撞
     * @param {Entity} entity - 检查碰撞的实体
     * @returns {Array} 碰撞的实体数组
     */
    checkCollisions(entity) {
        const collisions = [];
        const nearbyEntities = this.getEntitiesInRadius(entity.x, entity.y, entity.radius + 50);
        
        for (let other of nearbyEntities) {
            if (other === entity) continue;
            
            if (entity.collidesWith(other)) {
                collisions.push(other);
            }
        }
        
        return collisions;
    }

    /**
     * 更新世界
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        // 更新所有实体
        this.entities.forEach(entity => {
            if (entity.active) {
                entity.update(deltaTime);
                
                // 更新网格位置（如果实体移动了）
                if (entity.vx !== 0 || entity.vy !== 0) {
                    this.updateEntityGrid(entity);
                }
            }
        });
        
        // 清理已销毁的实体
        this.cleanupDestroyedEntities();
    }

    /**
     * 清理已销毁的实体
     */
    cleanupDestroyedEntities() {
        const toRemove = [];
        
        this.entities.forEach((entity, id) => {
            if (!entity.active) {
                toRemove.push(id);
            }
        });
        
        toRemove.forEach(id => {
            this.removeEntity(id);
        });
    }

    /**
     * 渲染世界
     * @param {Renderer} renderer - 渲染器
     */
    render(renderer) {
        // 渲染背景瓦片
        this.backgroundTiles.forEach(tile => {
            renderer.drawRect(tile.x, tile.y, tile.width, tile.height, tile.color);
        });
        
        // 渲染所有实体
        this.entities.forEach(entity => {
            if (entity.visible) {
                entity.render(renderer);
            }
        });
    }

    /**
     * 获取世界状态数据
     * @returns {object} 世界状态
     */
    getState() {
        return {
            width: this.width,
            height: this.height,
            entityCount: this.entities.size,
            obstacleCount: this.obstacles.length,
            portalCount: this.portals.length,
            collectibleCount: this.collectibles.length
        };
    }

    /**
     * 清空世界
     */
    clear() {
        this.entities.clear();
        this.obstacles = [];
        this.portals = [];
        this.collectibles = [];
        this.grid.clear();
    }

    /**
     * 获取随机可通行位置
     * @param {number} radius - 实体半径
     * @param {number} maxAttempts - 最大尝试次数
     * @returns {object|null} 位置坐标或null
     */
    getRandomWalkablePosition(radius = 10, maxAttempts = 50) {
        for (let i = 0; i < maxAttempts; i++) {
            const x = MathUtils.random(radius, this.width - radius);
            const y = MathUtils.random(radius, this.height - radius);
            
            if (this.isPositionWalkable(x, y, radius)) {
                return { x, y };
            }
        }
        
        return null;
    }
}

// 导出游戏世界类
window.GameWorld = GameWorld;
