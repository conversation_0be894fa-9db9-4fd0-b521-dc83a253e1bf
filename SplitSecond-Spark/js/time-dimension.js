/**
 * 时间维度系统 - SplitSecond Spark
 * 管理游戏中的时间穿梭机制和不同时间线的状态
 */

class TimeDimension {
    constructor() {
        // 时间维度定义
        this.dimensions = {
            past: {
                name: '过去',
                id: 'past',
                theme: {
                    primary: '#8b4513',
                    secondary: '#a0522d',
                    accent: '#daa520'
                },
                worldState: null,
                sparks: [],
                obstacles: [],
                discovered: false
            },
            present: {
                name: '现在',
                id: 'present',
                theme: {
                    primary: '#00d4ff',
                    secondary: '#4ecdc4',
                    accent: '#ffffff'
                },
                worldState: null,
                sparks: [],
                obstacles: [],
                discovered: true // 默认解锁
            },
            future: {
                name: '未来',
                id: 'future',
                theme: {
                    primary: '#9b59b6',
                    secondary: '#e74c3c',
                    accent: '#f39c12'
                },
                worldState: null,
                sparks: [],
                obstacles: [],
                discovered: false
            }
        };
        
        this.currentDimension = 'present';
        this.transitionInProgress = false;
        this.transitionDuration = 1000; // 毫秒
        this.transitionStartTime = 0;
        
        // 时间穿梭冷却
        this.switchCooldown = 500; // 毫秒
        this.lastSwitchTime = 0;
        
        // 时间线关联规则
        this.dimensionRules = {
            // 过去的改变如何影响现在和未来
            past: {
                affects: ['present', 'future'],
                influence: 0.7 // 影响强度
            },
            // 现在的改变如何影响未来
            present: {
                affects: ['future'],
                influence: 0.5
            },
            // 未来不影响其他时间线
            future: {
                affects: [],
                influence: 0
            }
        };
        
        this.initializeDimensions();
        DebugUtils.log('时间维度系统初始化完成');
    }

    /**
     * 初始化时间维度
     */
    initializeDimensions() {
        // 为每个时间维度生成独特的世界状态
        Object.keys(this.dimensions).forEach(dimensionId => {
            this.generateDimensionWorld(dimensionId);
        });
    }

    /**
     * 生成时间维度的世界状态
     * @param {string} dimensionId - 时间维度ID
     */
    generateDimensionWorld(dimensionId) {
        const dimension = this.dimensions[dimensionId];
        const baseWorldSize = { width: 1200, height: 800 };
        
        // 生成障碍物
        dimension.obstacles = this.generateObstacles(dimensionId, baseWorldSize);
        
        // 生成能量火花
        dimension.sparks = this.generateSparks(dimensionId, baseWorldSize);
        
        // 设置世界状态
        dimension.worldState = {
            size: baseWorldSize,
            theme: dimension.theme,
            environmentEffects: this.getEnvironmentEffects(dimensionId)
        };
    }

    /**
     * 生成障碍物
     * @param {string} dimensionId - 时间维度ID
     * @param {object} worldSize - 世界大小
     * @returns {Array} 障碍物数组
     */
    generateObstacles(dimensionId, worldSize) {
        const obstacles = [];
        const obstacleCount = MathUtils.randomInt(8, 15);
        
        for (let i = 0; i < obstacleCount; i++) {
            const obstacle = {
                id: `${dimensionId}_obstacle_${i}`,
                x: MathUtils.randomInt(50, worldSize.width - 100),
                y: MathUtils.randomInt(50, worldSize.height - 100),
                width: MathUtils.randomInt(30, 80),
                height: MathUtils.randomInt(30, 80),
                color: this.getObstacleColor(dimensionId),
                type: this.getObstacleType(dimensionId),
                solid: true
            };
            
            obstacles.push(obstacle);
        }
        
        return obstacles;
    }

    /**
     * 生成能量火花
     * @param {string} dimensionId - 时间维度ID
     * @param {object} worldSize - 世界大小
     * @returns {Array} 能量火花数组
     */
    generateSparks(dimensionId, worldSize) {
        const sparks = [];
        const sparkCount = MathUtils.randomInt(5, 10);
        
        for (let i = 0; i < sparkCount; i++) {
            const spark = {
                id: `${dimensionId}_spark_${i}`,
                x: MathUtils.randomInt(100, worldSize.width - 100),
                y: MathUtils.randomInt(100, worldSize.height - 100),
                radius: MathUtils.randomInt(8, 15),
                color: this.getSparkColor(dimensionId),
                type: this.getSparkType(dimensionId),
                energy: MathUtils.randomInt(10, 50),
                collected: false,
                pulsePhase: Math.random() * Math.PI * 2
            };
            
            sparks.push(spark);
        }
        
        return sparks;
    }

    /**
     * 获取障碍物颜色
     * @param {string} dimensionId - 时间维度ID
     * @returns {string} 颜色值
     */
    getObstacleColor(dimensionId) {
        const theme = this.dimensions[dimensionId].theme;
        
        switch (dimensionId) {
            case 'past':
                return theme.secondary + '80';
            case 'present':
                return theme.primary + '60';
            case 'future':
                return theme.secondary + '90';
            default:
                return '#666666';
        }
    }

    /**
     * 获取障碍物类型
     * @param {string} dimensionId - 时间维度ID
     * @returns {string} 障碍物类型
     */
    getObstacleType(dimensionId) {
        const types = {
            past: ['ruins', 'stones', 'debris'],
            present: ['walls', 'barriers', 'structures'],
            future: ['crystals', 'energy_fields', 'tech_barriers']
        };
        
        const typeList = types[dimensionId] || types.present;
        return typeList[Math.floor(Math.random() * typeList.length)];
    }

    /**
     * 获取能量火花颜色
     * @param {string} dimensionId - 时间维度ID
     * @returns {string} 颜色值
     */
    getSparkColor(dimensionId) {
        const colors = {
            past: ['#daa520', '#ffd700', '#ffb347'],
            present: ['#00d4ff', '#4ecdc4', '#87ceeb'],
            future: ['#9b59b6', '#e74c3c', '#f39c12']
        };
        
        const colorList = colors[dimensionId] || colors.present;
        return colorList[Math.floor(Math.random() * colorList.length)];
    }

    /**
     * 获取能量火花类型
     * @param {string} dimensionId - 时间维度ID
     * @returns {string} 火花类型
     */
    getSparkType(dimensionId) {
        const types = {
            past: ['memory', 'echo', 'remnant'],
            present: ['energy', 'power', 'essence'],
            future: ['quantum', 'plasma', 'void']
        };
        
        const typeList = types[dimensionId] || types.present;
        return typeList[Math.floor(Math.random() * typeList.length)];
    }

    /**
     * 获取环境效果
     * @param {string} dimensionId - 时间维度ID
     * @returns {object} 环境效果对象
     */
    getEnvironmentEffects(dimensionId) {
        const effects = {
            past: {
                particles: 'dust',
                lighting: 'warm',
                atmosphere: 'nostalgic'
            },
            present: {
                particles: 'sparkles',
                lighting: 'bright',
                atmosphere: 'energetic'
            },
            future: {
                particles: 'plasma',
                lighting: 'neon',
                atmosphere: 'mysterious'
            }
        };
        
        return effects[dimensionId] || effects.present;
    }

    /**
     * 切换时间维度
     * @param {string} targetDimension - 目标时间维度
     * @returns {boolean} 是否成功切换
     */
    switchDimension(targetDimension) {
        const currentTime = TimeUtils.now();
        
        // 检查冷却时间
        if (currentTime - this.lastSwitchTime < this.switchCooldown) {
            DebugUtils.log('时间切换冷却中', 'warn');
            return false;
        }
        
        // 检查目标维度是否存在
        if (!this.dimensions[targetDimension]) {
            DebugUtils.log(`未知的时间维度: ${targetDimension}`, 'error');
            return false;
        }
        
        // 检查目标维度是否已解锁
        if (!this.dimensions[targetDimension].discovered) {
            DebugUtils.log(`时间维度未解锁: ${targetDimension}`, 'warn');
            return false;
        }
        
        // 如果已经在目标维度，不需要切换
        if (this.currentDimension === targetDimension) {
            return false;
        }
        
        // 开始切换
        this.startTransition(targetDimension);
        return true;
    }

    /**
     * 开始时间维度切换
     * @param {string} targetDimension - 目标时间维度
     */
    startTransition(targetDimension) {
        this.transitionInProgress = true;
        this.transitionStartTime = TimeUtils.now();
        this.lastSwitchTime = this.transitionStartTime;
        
        const previousDimension = this.currentDimension;
        this.currentDimension = targetDimension;
        
        // 触发切换事件
        this.onDimensionSwitch(previousDimension, targetDimension);
        
        DebugUtils.log(`时间维度切换: ${previousDimension} -> ${targetDimension}`);
        
        // 设置切换完成定时器
        setTimeout(() => {
            this.completeTransition();
        }, this.transitionDuration);
    }

    /**
     * 完成时间维度切换
     */
    completeTransition() {
        this.transitionInProgress = false;
        DebugUtils.log(`时间维度切换完成: ${this.currentDimension}`);
    }

    /**
     * 时间维度切换事件处理
     * @param {string} from - 源时间维度
     * @param {string} to - 目标时间维度
     */
    onDimensionSwitch(from, to) {
        // 更新UI
        this.updateDimensionUI(to);
        
        // 创建切换特效
        if (window.particleSystem) {
            particleSystem.createTimeTransitionEffect(
                window.innerWidth / 2,
                window.innerHeight / 2
            );
        }
        
        // 播放切换音效
        this.playSwitchSound(to);
    }

    /**
     * 更新时间维度UI
     * @param {string} dimensionId - 时间维度ID
     */
    updateDimensionUI(dimensionId) {
        // 更新时间按钮状态
        const timeButtons = DOMUtils.$$('.time-btn');
        timeButtons.forEach(btn => {
            DOMUtils.removeClass(btn, 'active');
        });
        
        const activeButton = DOMUtils.$(`#${dimensionId}-btn`);
        if (activeButton) {
            DOMUtils.addClass(activeButton, 'active');
        }
    }

    /**
     * 播放切换音效
     * @param {string} dimensionId - 时间维度ID
     */
    playSwitchSound(dimensionId) {
        // 这里可以添加音效播放逻辑
        DebugUtils.log(`播放时间切换音效: ${dimensionId}`);
    }

    /**
     * 解锁时间维度
     * @param {string} dimensionId - 时间维度ID
     */
    unlockDimension(dimensionId) {
        if (this.dimensions[dimensionId]) {
            this.dimensions[dimensionId].discovered = true;
            DebugUtils.log(`时间维度已解锁: ${dimensionId}`);
        }
    }

    /**
     * 获取当前时间维度
     * @returns {object} 当前时间维度对象
     */
    getCurrentDimension() {
        return this.dimensions[this.currentDimension];
    }

    /**
     * 获取时间维度数据
     * @param {string} dimensionId - 时间维度ID
     * @returns {object} 时间维度对象
     */
    getDimension(dimensionId) {
        return this.dimensions[dimensionId];
    }

    /**
     * 检查是否在切换中
     * @returns {boolean} 是否在切换中
     */
    isTransitioning() {
        return this.transitionInProgress;
    }

    /**
     * 获取切换进度
     * @returns {number} 切换进度 (0-1)
     */
    getTransitionProgress() {
        if (!this.transitionInProgress) return 0;
        
        const elapsed = TimeUtils.now() - this.transitionStartTime;
        return MathUtils.clamp(elapsed / this.transitionDuration, 0, 1);
    }

    /**
     * 更新时间维度系统
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        // 更新当前维度的动态元素
        const currentDim = this.getCurrentDimension();
        
        // 更新能量火花的脉冲效果
        currentDim.sparks.forEach(spark => {
            if (!spark.collected) {
                spark.pulsePhase += deltaTime * 0.005;
            }
        });
    }

    /**
     * 获取保存数据
     * @returns {object} 保存数据
     */
    getSaveData() {
        const saveData = {
            currentDimension: this.currentDimension,
            dimensions: {}
        };
        
        // 保存每个维度的状态
        Object.keys(this.dimensions).forEach(dimensionId => {
            const dimension = this.dimensions[dimensionId];
            saveData.dimensions[dimensionId] = {
                discovered: dimension.discovered,
                sparks: dimension.sparks.map(spark => ({
                    id: spark.id,
                    collected: spark.collected
                }))
            };
        });
        
        return saveData;
    }

    /**
     * 加载保存数据
     * @param {object} saveData - 保存数据
     */
    loadSaveData(saveData) {
        if (saveData.currentDimension) {
            this.currentDimension = saveData.currentDimension;
        }
        
        if (saveData.dimensions) {
            Object.keys(saveData.dimensions).forEach(dimensionId => {
                const savedDimension = saveData.dimensions[dimensionId];
                const dimension = this.dimensions[dimensionId];
                
                if (dimension && savedDimension) {
                    dimension.discovered = savedDimension.discovered;
                    
                    // 恢复火花收集状态
                    if (savedDimension.sparks) {
                        savedDimension.sparks.forEach(savedSpark => {
                            const spark = dimension.sparks.find(s => s.id === savedSpark.id);
                            if (spark) {
                                spark.collected = savedSpark.collected;
                            }
                        });
                    }
                }
            });
        }
        
        DebugUtils.log('时间维度数据加载完成');
    }
}

// 创建全局时间维度系统实例
window.timeDimension = new TimeDimension();
