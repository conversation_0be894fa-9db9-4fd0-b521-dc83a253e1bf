/**
 * 粒子系统 - SplitSecond Spark
 * 处理游戏中的各种粒子特效
 */

class Particle {
    constructor(x, y, options = {}) {
        this.x = x;
        this.y = y;
        this.startX = x;
        this.startY = y;
        
        // 运动属性
        this.vx = options.vx || MathUtils.random(-2, 2);
        this.vy = options.vy || MathUtils.random(-2, 2);
        this.ax = options.ax || 0;
        this.ay = options.ay || 0.1; // 重力
        
        // 视觉属性
        this.size = options.size || MathUtils.random(2, 6);
        this.startSize = this.size;
        this.color = options.color || '#ffffff';
        this.opacity = options.opacity || 1;
        this.startOpacity = this.opacity;
        
        // 生命周期
        this.life = options.life || 1000; // 毫秒
        this.maxLife = this.life;
        this.age = 0;
        
        // 特效属性
        this.rotation = options.rotation || 0;
        this.rotationSpeed = options.rotationSpeed || MathUtils.random(-0.1, 0.1);
        this.scaleSpeed = options.scaleSpeed || -0.001;
        this.fadeSpeed = options.fadeSpeed || -0.002;
        
        // 类型
        this.type = options.type || 'default';
        
        this.isDead = false;
    }

    /**
     * 更新粒子状态
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        if (this.isDead) return;
        
        this.age += deltaTime;
        
        // 更新位置
        this.vx += this.ax * deltaTime * 0.001;
        this.vy += this.ay * deltaTime * 0.001;
        this.x += this.vx * deltaTime * 0.1;
        this.y += this.vy * deltaTime * 0.1;
        
        // 更新旋转
        this.rotation += this.rotationSpeed * deltaTime * 0.1;
        
        // 更新大小和透明度
        this.size += this.scaleSpeed * deltaTime;
        this.opacity += this.fadeSpeed * deltaTime;
        
        // 生命周期处理
        const lifeRatio = this.age / this.maxLife;
        
        if (this.type === 'spark') {
            // 火花粒子特殊效果
            this.size = this.startSize * (1 - lifeRatio * 0.5);
            this.opacity = this.startOpacity * (1 - lifeRatio);
        } else if (this.type === 'explosion') {
            // 爆炸粒子特殊效果
            this.size = this.startSize * (1 + lifeRatio * 2);
            this.opacity = this.startOpacity * (1 - lifeRatio * lifeRatio);
        } else if (this.type === 'trail') {
            // 拖尾粒子特殊效果
            this.opacity = this.startOpacity * (1 - lifeRatio);
        }
        
        // 检查是否死亡
        if (this.age >= this.maxLife || this.opacity <= 0 || this.size <= 0) {
            this.isDead = true;
        }
    }

    /**
     * 渲染粒子
     * @param {Renderer} renderer - 渲染器
     */
    render(renderer) {
        if (this.isDead || this.opacity <= 0) return;
        
        const ctx = renderer.ctx;
        
        ctx.save();
        ctx.globalAlpha = MathUtils.clamp(this.opacity, 0, 1);
        ctx.translate(this.x, this.y);
        ctx.rotate(this.rotation);
        
        if (this.type === 'spark') {
            this.renderSpark(ctx);
        } else if (this.type === 'explosion') {
            this.renderExplosion(ctx);
        } else if (this.type === 'trail') {
            this.renderTrail(ctx);
        } else {
            this.renderDefault(ctx);
        }
        
        ctx.restore();
    }

    /**
     * 渲染默认粒子
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    renderDefault(ctx) {
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(0, 0, this.size, 0, Math.PI * 2);
        ctx.fill();
    }

    /**
     * 渲染火花粒子
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    renderSpark(ctx) {
        // 创建径向渐变
        const gradient = ctx.createRadialGradient(0, 0, 0, 0, 0, this.size);
        gradient.addColorStop(0, this.color);
        gradient.addColorStop(0.7, this.color + '80');
        gradient.addColorStop(1, this.color + '00');
        
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(0, 0, this.size, 0, Math.PI * 2);
        ctx.fill();
        
        // 添加闪烁效果
        if (Math.random() < 0.3) {
            ctx.fillStyle = '#ffffff';
            ctx.beginPath();
            ctx.arc(0, 0, this.size * 0.3, 0, Math.PI * 2);
            ctx.fill();
        }
    }

    /**
     * 渲染爆炸粒子
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    renderExplosion(ctx) {
        const spikes = 8;
        const outerRadius = this.size;
        const innerRadius = this.size * 0.5;
        
        ctx.fillStyle = this.color;
        ctx.beginPath();
        
        for (let i = 0; i < spikes * 2; i++) {
            const radius = i % 2 === 0 ? outerRadius : innerRadius;
            const angle = (i * Math.PI) / spikes;
            const x = Math.cos(angle) * radius;
            const y = Math.sin(angle) * radius;
            
            if (i === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        }
        
        ctx.closePath();
        ctx.fill();
    }

    /**
     * 渲染拖尾粒子
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    renderTrail(ctx) {
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.ellipse(0, 0, this.size, this.size * 0.3, 0, 0, Math.PI * 2);
        ctx.fill();
    }
}

class ParticleSystem {
    constructor() {
        this.particles = [];
        this.emitters = [];
        
        DebugUtils.log('粒子系统初始化完成');
    }

    /**
     * 创建粒子
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {object} options - 粒子选项
     * @returns {Particle} 创建的粒子
     */
    createParticle(x, y, options = {}) {
        const particle = new Particle(x, y, options);
        this.particles.push(particle);
        return particle;
    }

    /**
     * 创建火花收集特效
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {string} color - 颜色
     */
    createSparkCollectEffect(x, y, color = '#ffff00') {
        const particleCount = 15;
        
        for (let i = 0; i < particleCount; i++) {
            const angle = (i / particleCount) * Math.PI * 2;
            const speed = MathUtils.random(2, 5);
            
            this.createParticle(x, y, {
                vx: Math.cos(angle) * speed,
                vy: Math.sin(angle) * speed,
                size: MathUtils.random(3, 8),
                color: color,
                life: MathUtils.random(800, 1200),
                type: 'spark',
                ay: -0.2 // 向上飘
            });
        }
    }

    /**
     * 创建时间切换特效
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     */
    createTimeTransitionEffect(x, y) {
        const particleCount = 30;
        const colors = ['#00d4ff', '#4ecdc4', '#ffffff'];
        
        for (let i = 0; i < particleCount; i++) {
            const angle = MathUtils.random(0, Math.PI * 2);
            const speed = MathUtils.random(1, 4);
            const color = colors[Math.floor(Math.random() * colors.length)];
            
            this.createParticle(x, y, {
                vx: Math.cos(angle) * speed,
                vy: Math.sin(angle) * speed,
                size: MathUtils.random(2, 6),
                color: color,
                life: MathUtils.random(1000, 1500),
                type: 'explosion',
                rotationSpeed: MathUtils.random(-0.2, 0.2)
            });
        }
    }

    /**
     * 创建玩家移动拖尾
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {string} color - 颜色
     */
    createPlayerTrail(x, y, color = '#ffffff') {
        if (Math.random() < 0.3) { // 不是每帧都创建
            this.createParticle(x + MathUtils.random(-5, 5), y + MathUtils.random(-5, 5), {
                vx: MathUtils.random(-0.5, 0.5),
                vy: MathUtils.random(-0.5, 0.5),
                size: MathUtils.random(2, 4),
                color: color + '80',
                life: 500,
                type: 'trail',
                ay: 0
            });
        }
    }

    /**
     * 创建环境粒子
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {number} width - 宽度
     * @param {number} height - 高度
     */
    createAmbientParticles(x, y, width, height) {
        if (Math.random() < 0.02) { // 低频率创建
            const particleX = x + Math.random() * width;
            const particleY = y + Math.random() * height;
            
            this.createParticle(particleX, particleY, {
                vx: MathUtils.random(-0.2, 0.2),
                vy: MathUtils.random(-0.5, -0.1),
                size: MathUtils.random(1, 3),
                color: '#ffffff',
                opacity: MathUtils.random(0.1, 0.3),
                life: MathUtils.random(3000, 5000),
                ay: -0.05
            });
        }
    }

    /**
     * 更新粒子系统
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        // 更新所有粒子
        for (let i = this.particles.length - 1; i >= 0; i--) {
            const particle = this.particles[i];
            particle.update(deltaTime);
            
            // 移除死亡的粒子
            if (particle.isDead) {
                this.particles.splice(i, 1);
            }
        }
    }

    /**
     * 渲染粒子系统
     * @param {Renderer} renderer - 渲染器
     */
    render(renderer) {
        this.particles.forEach(particle => {
            particle.render(renderer);
        });
    }

    /**
     * 清除所有粒子
     */
    clear() {
        this.particles = [];
    }

    /**
     * 获取粒子数量
     * @returns {number} 粒子数量
     */
    getParticleCount() {
        return this.particles.length;
    }
}

// 创建全局粒子系统实例
window.particleSystem = new ParticleSystem();
