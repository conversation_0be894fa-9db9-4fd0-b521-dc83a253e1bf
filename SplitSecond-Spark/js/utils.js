/**
 * 工具函数库 - SplitSecond Spark
 * 提供游戏中常用的工具函数和辅助方法
 */

// 数学工具函数
const MathUtils = {
    /**
     * 将角度转换为弧度
     * @param {number} degrees - 角度值
     * @returns {number} 弧度值
     */
    degToRad(degrees) {
        return degrees * Math.PI / 180;
    },

    /**
     * 将弧度转换为角度
     * @param {number} radians - 弧度值
     * @returns {number} 角度值
     */
    radToDeg(radians) {
        return radians * 180 / Math.PI;
    },

    /**
     * 计算两点之间的距离
     * @param {number} x1 - 第一个点的x坐标
     * @param {number} y1 - 第一个点的y坐标
     * @param {number} x2 - 第二个点的x坐标
     * @param {number} y2 - 第二个点的y坐标
     * @returns {number} 距离值
     */
    distance(x1, y1, x2, y2) {
        const dx = x2 - x1;
        const dy = y2 - y1;
        return Math.sqrt(dx * dx + dy * dy);
    },

    /**
     * 线性插值
     * @param {number} start - 起始值
     * @param {number} end - 结束值
     * @param {number} t - 插值参数 (0-1)
     * @returns {number} 插值结果
     */
    lerp(start, end, t) {
        return start + (end - start) * t;
    },

    /**
     * 将值限制在指定范围内
     * @param {number} value - 要限制的值
     * @param {number} min - 最小值
     * @param {number} max - 最大值
     * @returns {number} 限制后的值
     */
    clamp(value, min, max) {
        return Math.min(Math.max(value, min), max);
    },

    /**
     * 生成指定范围内的随机数
     * @param {number} min - 最小值
     * @param {number} max - 最大值
     * @returns {number} 随机数
     */
    random(min, max) {
        return Math.random() * (max - min) + min;
    },

    /**
     * 生成指定范围内的随机整数
     * @param {number} min - 最小值
     * @param {number} max - 最大值
     * @returns {number} 随机整数
     */
    randomInt(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }
};

// 颜色工具函数
const ColorUtils = {
    /**
     * 将HSL颜色转换为RGB
     * @param {number} h - 色相 (0-360)
     * @param {number} s - 饱和度 (0-100)
     * @param {number} l - 亮度 (0-100)
     * @returns {object} RGB颜色对象
     */
    hslToRgb(h, s, l) {
        h /= 360;
        s /= 100;
        l /= 100;

        const c = (1 - Math.abs(2 * l - 1)) * s;
        const x = c * (1 - Math.abs((h * 6) % 2 - 1));
        const m = l - c / 2;

        let r, g, b;

        if (0 <= h && h < 1/6) {
            r = c; g = x; b = 0;
        } else if (1/6 <= h && h < 2/6) {
            r = x; g = c; b = 0;
        } else if (2/6 <= h && h < 3/6) {
            r = 0; g = c; b = x;
        } else if (3/6 <= h && h < 4/6) {
            r = 0; g = x; b = c;
        } else if (4/6 <= h && h < 5/6) {
            r = x; g = 0; b = c;
        } else {
            r = c; g = 0; b = x;
        }

        return {
            r: Math.round((r + m) * 255),
            g: Math.round((g + m) * 255),
            b: Math.round((b + m) * 255)
        };
    },

    /**
     * 创建RGBA颜色字符串
     * @param {number} r - 红色分量 (0-255)
     * @param {number} g - 绿色分量 (0-255)
     * @param {number} b - 蓝色分量 (0-255)
     * @param {number} a - 透明度 (0-1)
     * @returns {string} RGBA颜色字符串
     */
    rgba(r, g, b, a = 1) {
        return `rgba(${r}, ${g}, ${b}, ${a})`;
    },

    /**
     * 创建HSL颜色字符串
     * @param {number} h - 色相 (0-360)
     * @param {number} s - 饱和度 (0-100)
     * @param {number} l - 亮度 (0-100)
     * @returns {string} HSL颜色字符串
     */
    hsl(h, s, l) {
        return `hsl(${h}, ${s}%, ${l}%)`;
    }
};

// 时间工具函数
const TimeUtils = {
    /**
     * 获取当前时间戳
     * @returns {number} 时间戳
     */
    now() {
        return Date.now();
    },

    /**
     * 格式化时间显示
     * @param {number} seconds - 秒数
     * @returns {string} 格式化的时间字符串
     */
    formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
};

// DOM工具函数
const DOMUtils = {
    /**
     * 获取元素
     * @param {string} selector - CSS选择器
     * @returns {Element|null} DOM元素
     */
    $(selector) {
        return document.querySelector(selector);
    },

    /**
     * 获取所有匹配的元素
     * @param {string} selector - CSS选择器
     * @returns {NodeList} DOM元素列表
     */
    $$(selector) {
        return document.querySelectorAll(selector);
    },

    /**
     * 添加CSS类
     * @param {Element} element - DOM元素
     * @param {string} className - CSS类名
     */
    addClass(element, className) {
        if (element) {
            element.classList.add(className);
        }
    },

    /**
     * 移除CSS类
     * @param {Element} element - DOM元素
     * @param {string} className - CSS类名
     */
    removeClass(element, className) {
        if (element) {
            element.classList.remove(className);
        }
    },

    /**
     * 切换CSS类
     * @param {Element} element - DOM元素
     * @param {string} className - CSS类名
     */
    toggleClass(element, className) {
        if (element) {
            element.classList.toggle(className);
        }
    },

    /**
     * 检查是否包含CSS类
     * @param {Element} element - DOM元素
     * @param {string} className - CSS类名
     * @returns {boolean} 是否包含该类
     */
    hasClass(element, className) {
        return element ? element.classList.contains(className) : false;
    },

    /**
     * 设置元素样式
     * @param {Element} element - DOM元素
     * @param {object} styles - 样式对象
     */
    setStyles(element, styles) {
        if (element) {
            Object.assign(element.style, styles);
        }
    }
};

// 动画工具函数
const AnimationUtils = {
    /**
     * 缓动函数 - 缓入
     * @param {number} t - 时间参数 (0-1)
     * @returns {number} 缓动值
     */
    easeIn(t) {
        return t * t;
    },

    /**
     * 缓动函数 - 缓出
     * @param {number} t - 时间参数 (0-1)
     * @returns {number} 缓动值
     */
    easeOut(t) {
        return 1 - (1 - t) * (1 - t);
    },

    /**
     * 缓动函数 - 缓入缓出
     * @param {number} t - 时间参数 (0-1)
     * @returns {number} 缓动值
     */
    easeInOut(t) {
        return t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2;
    },

    /**
     * 弹性缓动函数
     * @param {number} t - 时间参数 (0-1)
     * @returns {number} 缓动值
     */
    easeElastic(t) {
        const c4 = (2 * Math.PI) / 3;
        return t === 0 ? 0 : t === 1 ? 1 : -Math.pow(2, 10 * t - 10) * Math.sin((t * 10 - 10.75) * c4);
    }
};

// 设备检测工具
const DeviceUtils = {
    /**
     * 检测是否为移动设备
     * @returns {boolean} 是否为移动设备
     */
    isMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    },

    /**
     * 检测是否为触摸设备
     * @returns {boolean} 是否为触摸设备
     */
    isTouchDevice() {
        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    },

    /**
     * 获取屏幕尺寸
     * @returns {object} 屏幕尺寸对象
     */
    getScreenSize() {
        return {
            width: window.innerWidth,
            height: window.innerHeight
        };
    }
};

// 调试工具
const DebugUtils = {
    /**
     * 控制台日志输出
     * @param {string} message - 日志消息
     * @param {string} type - 日志类型 (log, warn, error)
     */
    log(message, type = 'log') {
        if (window.DEBUG_MODE) {
            console[type](`[SplitSecond Spark] ${message}`);
        }
    },

    /**
     * 性能计时开始
     * @param {string} label - 计时标签
     */
    timeStart(label) {
        if (window.DEBUG_MODE) {
            console.time(`[SplitSecond Spark] ${label}`);
        }
    },

    /**
     * 性能计时结束
     * @param {string} label - 计时标签
     */
    timeEnd(label) {
        if (window.DEBUG_MODE) {
            console.timeEnd(`[SplitSecond Spark] ${label}`);
        }
    }
};

// 导出工具函数
window.MathUtils = MathUtils;
window.ColorUtils = ColorUtils;
window.TimeUtils = TimeUtils;
window.DOMUtils = DOMUtils;
window.AnimationUtils = AnimationUtils;
window.DeviceUtils = DeviceUtils;
window.DebugUtils = DebugUtils;
