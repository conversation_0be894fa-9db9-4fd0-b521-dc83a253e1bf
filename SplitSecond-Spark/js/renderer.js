/**
 * 渲染器 - SplitSecond Spark
 * 负责游戏的图形渲染，包括背景、实体、特效等
 */

class Renderer {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.width = 0;
        this.height = 0;
        
        // 相机系统
        this.camera = {
            x: 0,
            y: 0,
            zoom: 1,
            targetX: 0,
            targetY: 0,
            smoothing: 0.1
        };
        
        // 渲染层级
        this.layers = {
            background: [],
            entities: [],
            effects: [],
            ui: []
        };
        
        // 时间维度颜色主题
        this.timeThemes = {
            past: {
                primary: '#8b4513',
                secondary: '#a0522d',
                accent: '#daa520',
                filter: 'sepia(0.3) hue-rotate(30deg)'
            },
            present: {
                primary: '#00d4ff',
                secondary: '#4ecdc4',
                accent: '#ffffff',
                filter: 'none'
            },
            future: {
                primary: '#9b59b6',
                secondary: '#e74c3c',
                accent: '#f39c12',
                filter: 'hue-rotate(270deg) saturate(1.2)'
            }
        };
        
        this.currentTimeTheme = 'present';
        this.resize();
        this.setupCanvas();
        
        DebugUtils.log('渲染器初始化完成');
    }

    /**
     * 设置画布
     */
    setupCanvas() {
        // 设置高DPI支持
        const dpr = window.devicePixelRatio || 1;
        const rect = this.canvas.getBoundingClientRect();
        
        this.canvas.width = rect.width * dpr;
        this.canvas.height = rect.height * dpr;
        this.canvas.style.width = rect.width + 'px';
        this.canvas.style.height = rect.height + 'px';
        
        this.ctx.scale(dpr, dpr);
        
        // 设置渲染质量
        this.ctx.imageSmoothingEnabled = true;
        this.ctx.imageSmoothingQuality = 'high';
    }

    /**
     * 调整画布大小
     */
    resize() {
        const rect = this.canvas.getBoundingClientRect();
        this.width = rect.width;
        this.height = rect.height;
        this.setupCanvas();
    }

    /**
     * 清空画布
     */
    clear() {
        this.ctx.clearRect(0, 0, this.width, this.height);
    }

    /**
     * 更新相机位置
     * @param {number} targetX - 目标X坐标
     * @param {number} targetY - 目标Y坐标
     */
    updateCamera(targetX, targetY) {
        this.camera.targetX = targetX;
        this.camera.targetY = targetY;
        
        // 平滑跟随
        this.camera.x += (this.camera.targetX - this.camera.x) * this.camera.smoothing;
        this.camera.y += (this.camera.targetY - this.camera.y) * this.camera.smoothing;
    }

    /**
     * 设置相机变换
     */
    setCameraTransform() {
        this.ctx.save();
        this.ctx.translate(
            this.width / 2 - this.camera.x * this.camera.zoom,
            this.height / 2 - this.camera.y * this.camera.zoom
        );
        this.ctx.scale(this.camera.zoom, this.camera.zoom);
    }

    /**
     * 重置相机变换
     */
    resetCameraTransform() {
        this.ctx.restore();
    }

    /**
     * 世界坐标转屏幕坐标
     * @param {number} worldX - 世界X坐标
     * @param {number} worldY - 世界Y坐标
     * @returns {object} 屏幕坐标
     */
    worldToScreen(worldX, worldY) {
        return {
            x: (worldX - this.camera.x) * this.camera.zoom + this.width / 2,
            y: (worldY - this.camera.y) * this.camera.zoom + this.height / 2
        };
    }

    /**
     * 屏幕坐标转世界坐标
     * @param {number} screenX - 屏幕X坐标
     * @param {number} screenY - 屏幕Y坐标
     * @returns {object} 世界坐标
     */
    screenToWorld(screenX, screenY) {
        return {
            x: (screenX - this.width / 2) / this.camera.zoom + this.camera.x,
            y: (screenY - this.height / 2) / this.camera.zoom + this.camera.y
        };
    }

    /**
     * 设置时间维度主题
     * @param {string} timeDimension - 时间维度
     */
    setTimeDimension(timeDimension) {
        this.currentTimeTheme = timeDimension;
        
        // 应用滤镜效果到画布
        const theme = this.timeThemes[timeDimension];
        if (theme.filter !== 'none') {
            this.canvas.style.filter = theme.filter;
        } else {
            this.canvas.style.filter = '';
        }
    }

    /**
     * 获取当前时间主题
     * @returns {object} 时间主题对象
     */
    getCurrentTheme() {
        return this.timeThemes[this.currentTimeTheme];
    }

    /**
     * 绘制背景
     */
    drawBackground() {
        const theme = this.getCurrentTheme();
        
        // 创建渐变背景
        const gradient = this.ctx.createLinearGradient(0, 0, this.width, this.height);
        gradient.addColorStop(0, theme.primary + '20');
        gradient.addColorStop(0.5, theme.secondary + '10');
        gradient.addColorStop(1, '#0a0a1a');
        
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.width, this.height);
        
        // 绘制星空背景
        this.drawStars();
    }

    /**
     * 绘制星空
     */
    drawStars() {
        const theme = this.getCurrentTheme();
        const starCount = 100;
        
        this.ctx.save();
        
        for (let i = 0; i < starCount; i++) {
            const x = (i * 37) % this.width;
            const y = (i * 73) % this.height;
            const size = Math.sin(i * 0.1) * 0.5 + 1;
            const opacity = Math.sin(Date.now() * 0.001 + i * 0.1) * 0.3 + 0.7;
            
            this.ctx.fillStyle = theme.accent + Math.floor(opacity * 255).toString(16).padStart(2, '0');
            this.ctx.beginPath();
            this.ctx.arc(x, y, size, 0, Math.PI * 2);
            this.ctx.fill();
        }
        
        this.ctx.restore();
    }

    /**
     * 绘制矩形
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {number} width - 宽度
     * @param {number} height - 高度
     * @param {string} color - 颜色
     */
    drawRect(x, y, width, height, color) {
        this.ctx.fillStyle = color;
        this.ctx.fillRect(x, y, width, height);
    }

    /**
     * 绘制圆形
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {number} radius - 半径
     * @param {string} color - 颜色
     */
    drawCircle(x, y, radius, color) {
        this.ctx.fillStyle = color;
        this.ctx.beginPath();
        this.ctx.arc(x, y, radius, 0, Math.PI * 2);
        this.ctx.fill();
    }

    /**
     * 绘制文本
     * @param {string} text - 文本内容
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {string} color - 颜色
     * @param {string} font - 字体
     */
    drawText(text, x, y, color = '#ffffff', font = '16px Arial') {
        this.ctx.fillStyle = color;
        this.ctx.font = font;
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText(text, x, y);
    }

    /**
     * 绘制发光效果
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {number} radius - 半径
     * @param {string} color - 颜色
     * @param {number} intensity - 强度
     */
    drawGlow(x, y, radius, color, intensity = 1) {
        const gradient = this.ctx.createRadialGradient(x, y, 0, x, y, radius);
        gradient.addColorStop(0, color + Math.floor(255 * intensity).toString(16).padStart(2, '0'));
        gradient.addColorStop(1, color + '00');
        
        this.ctx.fillStyle = gradient;
        this.ctx.beginPath();
        this.ctx.arc(x, y, radius, 0, Math.PI * 2);
        this.ctx.fill();
    }

    /**
     * 主渲染函数
     * @param {object} gameState - 游戏状态
     */
    render(gameState) {
        this.clear();
        this.drawBackground();
        
        // 设置相机变换
        this.setCameraTransform();
        
        // 渲染游戏世界
        if (gameState.world) {
            this.renderWorld(gameState.world);
        }
        
        // 渲染玩家
        if (gameState.player) {
            this.renderPlayer(gameState.player);
        }
        
        // 渲染能量火花
        if (gameState.sparks) {
            this.renderSparks(gameState.sparks);
        }
        
        // 重置相机变换
        this.resetCameraTransform();
        
        // 渲染UI元素
        this.renderUI(gameState);
    }

    /**
     * 渲染游戏世界
     * @param {object} world - 世界对象
     */
    renderWorld(world) {
        // 渲染地图瓦片
        if (world.tiles) {
            world.tiles.forEach(tile => {
                this.drawRect(tile.x, tile.y, tile.width, tile.height, tile.color);
            });
        }
        
        // 渲染障碍物
        if (world.obstacles) {
            world.obstacles.forEach(obstacle => {
                this.drawRect(obstacle.x, obstacle.y, obstacle.width, obstacle.height, obstacle.color);
            });
        }
    }

    /**
     * 渲染玩家
     * @param {object} player - 玩家对象
     */
    renderPlayer(player) {
        const theme = this.getCurrentTheme();
        
        // 绘制玩家发光效果
        this.drawGlow(player.x, player.y, 30, theme.primary, 0.5);
        
        // 绘制玩家主体
        this.drawCircle(player.x, player.y, player.radius, theme.accent);
        
        // 绘制玩家边框
        this.ctx.strokeStyle = theme.primary;
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        this.ctx.arc(player.x, player.y, player.radius, 0, Math.PI * 2);
        this.ctx.stroke();
    }

    /**
     * 渲染能量火花
     * @param {Array} sparks - 能量火花数组
     */
    renderSparks(sparks) {
        sparks.forEach(spark => {
            // 绘制火花发光效果
            this.drawGlow(spark.x, spark.y, 20, spark.color, 0.8);
            
            // 绘制火花主体
            this.drawCircle(spark.x, spark.y, spark.radius, spark.color);
            
            // 绘制火花闪烁效果
            const flickerIntensity = Math.sin(Date.now() * 0.01 + spark.id) * 0.3 + 0.7;
            this.drawGlow(spark.x, spark.y, 10, '#ffffff', flickerIntensity);
        });
    }

    /**
     * 渲染UI元素
     * @param {object} gameState - 游戏状态
     */
    renderUI(gameState) {
        // UI元素通过CSS处理，这里可以添加额外的画布UI
    }
}

// 导出渲染器类
window.Renderer = Renderer;
