/**
 * 玩家系统 - SplitSecond Spark
 * 管理玩家角色的移动、状态和交互
 */

class Player extends Entity {
    constructor(x, y, options = {}) {
        super(x, y, {
            ...options,
            type: 'player',
            radius: 15,
            speed: 150,
            maxSpeed: 200,
            color: '#ffffff'
        });
        
        // 玩家属性
        this.health = 100;
        this.maxHealth = 100;
        this.energy = 100;
        this.maxEnergy = 100;
        
        // 收集统计
        this.sparksCollected = 0;
        this.totalSparks = 0;
        this.score = 0;
        
        // 移动状态
        this.isMoving = false;
        this.movementDirection = { x: 0, y: 0 };
        this.acceleration = 800;
        this.friction = 0.85;
        
        // 交互系统
        this.interactionRadius = 40;
        this.nearbyInteractables = [];
        
        // 视觉效果
        this.trailParticles = [];
        this.glowIntensity = 1;
        this.pulsePhase = 0;
        
        // 能力系统
        this.abilities = {
            timeShift: {
                unlocked: true,
                cooldown: 1000,
                lastUsed: 0
            },
            dash: {
                unlocked: false,
                cooldown: 2000,
                lastUsed: 0,
                distance: 100,
                duration: 200
            },
            energyBurst: {
                unlocked: false,
                cooldown: 5000,
                lastUsed: 0,
                radius: 80
            }
        };
        
        // 状态效果
        this.statusEffects = new Map();
        
        DebugUtils.log('玩家初始化完成');
    }

    /**
     * 更新玩家
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        super.update(deltaTime);
        
        this.updateMovement(deltaTime);
        this.updateStatusEffects(deltaTime);
        this.updateVisualEffects(deltaTime);
        this.updateInteractions();
        
        // 能量自然恢复
        this.regenerateEnergy(deltaTime);
    }

    /**
     * 更新移动
     * @param {number} deltaTime - 时间增量
     */
    updateMovement(deltaTime) {
        if (!window.inputManager) return;
        
        const movement = inputManager.getMovementVector();
        this.movementDirection = movement;
        this.isMoving = movement.x !== 0 || movement.y !== 0;
        
        if (this.isMoving) {
            // 应用加速度
            const targetVx = movement.x * this.speed;
            const targetVy = movement.y * this.speed;
            
            this.vx += (targetVx - this.vx) * this.acceleration * deltaTime * 0.001;
            this.vy += (targetVy - this.vy) * this.acceleration * deltaTime * 0.001;
            
            // 创建移动拖尾
            if (window.particleSystem) {
                const theme = window.renderer?.getCurrentTheme();
                const color = theme ? theme.accent : '#ffffff';
                particleSystem.createPlayerTrail(this.x, this.y, color);
            }
        } else {
            // 应用摩擦力
            this.vx *= this.friction;
            this.vy *= this.friction;
            
            // 停止微小移动
            if (Math.abs(this.vx) < 1) this.vx = 0;
            if (Math.abs(this.vy) < 1) this.vy = 0;
        }
        
        // 处理移动和碰撞
        if (window.gameWorld && (this.vx !== 0 || this.vy !== 0)) {
            const newX = this.x + this.vx * deltaTime * 0.001;
            const newY = this.y + this.vy * deltaTime * 0.001;
            
            const moveResult = gameWorld.moveEntity(this, newX, newY);
            
            // 如果碰撞，减少速度
            if (!moveResult.moved) {
                this.vx *= 0.5;
                this.vy *= 0.5;
            }
        }
    }

    /**
     * 更新状态效果
     * @param {number} deltaTime - 时间增量
     */
    updateStatusEffects(deltaTime) {
        const toRemove = [];
        
        this.statusEffects.forEach((effect, name) => {
            effect.duration -= deltaTime;
            
            if (effect.duration <= 0) {
                toRemove.push(name);
                this.removeStatusEffect(name);
            } else {
                this.applyStatusEffect(effect, deltaTime);
            }
        });
        
        toRemove.forEach(name => {
            this.statusEffects.delete(name);
        });
    }

    /**
     * 更新视觉效果
     * @param {number} deltaTime - 时间增量
     */
    updateVisualEffects(deltaTime) {
        this.pulsePhase += deltaTime * 0.005;
        
        // 根据移动状态调整发光强度
        if (this.isMoving) {
            this.glowIntensity = Math.min(this.glowIntensity + deltaTime * 0.003, 1.5);
        } else {
            this.glowIntensity = Math.max(this.glowIntensity - deltaTime * 0.002, 0.8);
        }
    }

    /**
     * 更新交互检测
     */
    updateInteractions() {
        if (!window.gameWorld) return;
        
        this.nearbyInteractables = [];
        
        // 检查附近的可交互对象
        const nearbyEntities = gameWorld.getEntitiesInRadius(
            this.x, this.y, this.interactionRadius
        );
        
        nearbyEntities.forEach(entity => {
            if (this.canInteractWith(entity)) {
                this.nearbyInteractables.push(entity);
            }
        });
    }

    /**
     * 检查是否可以与实体交互
     * @param {Entity} entity - 实体对象
     * @returns {boolean} 是否可交互
     */
    canInteractWith(entity) {
        if (entity === this) return false;
        
        return entity.type === 'collectible' || 
               entity.type === 'portal' || 
               entity.hasTag('interactable');
    }

    /**
     * 执行交互
     */
    interact() {
        if (this.nearbyInteractables.length === 0) return;
        
        // 与最近的可交互对象交互
        let closest = null;
        let closestDistance = Infinity;
        
        this.nearbyInteractables.forEach(entity => {
            const distance = this.distanceTo(entity);
            if (distance < closestDistance) {
                closest = entity;
                closestDistance = distance;
            }
        });
        
        if (closest) {
            this.interactWith(closest);
        }
    }

    /**
     * 与特定实体交互
     * @param {Entity} entity - 实体对象
     */
    interactWith(entity) {
        if (entity.type === 'collectible') {
            this.collectItem(entity);
        } else if (entity.type === 'portal') {
            this.usePortal(entity);
        }
    }

    /**
     * 收集物品
     * @param {Collectible} collectible - 收集品
     */
    collectItem(collectible) {
        if (collectible.collect(this)) {
            this.sparksCollected++;
            this.score += collectible.value;
            
            // 创建收集特效
            if (window.particleSystem) {
                particleSystem.createSparkCollectEffect(
                    collectible.x, collectible.y, collectible.color
                );
            }
            
            // 更新UI
            this.updateUI();
            
            DebugUtils.log(`收集物品: ${collectible.id}, 总分: ${this.score}`);
        }
    }

    /**
     * 使用传送门
     * @param {Portal} portal - 传送门
     */
    usePortal(portal) {
        if (portal.use(this)) {
            // 创建传送特效
            if (window.particleSystem) {
                particleSystem.createTimeTransitionEffect(this.x, this.y);
            }
            
            DebugUtils.log(`使用传送门: ${portal.id}`);
        }
    }

    /**
     * 使用能力
     * @param {string} abilityName - 能力名称
     * @returns {boolean} 是否成功使用
     */
    useAbility(abilityName) {
        const ability = this.abilities[abilityName];
        if (!ability || !ability.unlocked) return false;
        
        const currentTime = TimeUtils.now();
        if (currentTime - ability.lastUsed < ability.cooldown) return false;
        
        ability.lastUsed = currentTime;
        
        switch (abilityName) {
            case 'dash':
                return this.performDash();
            case 'energyBurst':
                return this.performEnergyBurst();
            default:
                return false;
        }
    }

    /**
     * 执行冲刺
     * @returns {boolean} 是否成功
     */
    performDash() {
        if (this.energy < 20) return false;
        
        this.energy -= 20;
        
        // 向移动方向冲刺
        const direction = this.movementDirection;
        if (direction.x === 0 && direction.y === 0) {
            // 如果没有移动方向，向前冲刺
            direction.x = 1;
        }
        
        const dashSpeed = this.abilities.dash.distance;
        this.vx = direction.x * dashSpeed;
        this.vy = direction.y * dashSpeed;
        
        // 添加冲刺状态效果
        this.addStatusEffect('dash', {
            duration: this.abilities.dash.duration,
            type: 'movement'
        });
        
        return true;
    }

    /**
     * 执行能量爆发
     * @returns {boolean} 是否成功
     */
    performEnergyBurst() {
        if (this.energy < 50) return false;
        
        this.energy -= 50;
        
        // 创建能量爆发特效
        if (window.particleSystem) {
            for (let i = 0; i < 20; i++) {
                const angle = (i / 20) * Math.PI * 2;
                const distance = this.abilities.energyBurst.radius;
                
                particleSystem.createParticle(this.x, this.y, {
                    vx: Math.cos(angle) * 5,
                    vy: Math.sin(angle) * 5,
                    size: MathUtils.random(5, 10),
                    color: '#00d4ff',
                    life: 1000,
                    type: 'explosion'
                });
            }
        }
        
        return true;
    }

    /**
     * 添加状态效果
     * @param {string} name - 效果名称
     * @param {object} effect - 效果对象
     */
    addStatusEffect(name, effect) {
        this.statusEffects.set(name, effect);
    }

    /**
     * 移除状态效果
     * @param {string} name - 效果名称
     */
    removeStatusEffect(name) {
        this.statusEffects.delete(name);
    }

    /**
     * 应用状态效果
     * @param {object} effect - 效果对象
     * @param {number} deltaTime - 时间增量
     */
    applyStatusEffect(effect, deltaTime) {
        switch (effect.type) {
            case 'movement':
                // 移动相关效果
                break;
            case 'damage':
                // 伤害效果
                this.takeDamage(effect.damage * deltaTime * 0.001);
                break;
            case 'heal':
                // 治疗效果
                this.heal(effect.healing * deltaTime * 0.001);
                break;
        }
    }

    /**
     * 能量自然恢复
     * @param {number} deltaTime - 时间增量
     */
    regenerateEnergy(deltaTime) {
        if (this.energy < this.maxEnergy) {
            this.energy = Math.min(this.energy + 10 * deltaTime * 0.001, this.maxEnergy);
        }
    }

    /**
     * 受到伤害
     * @param {number} damage - 伤害值
     */
    takeDamage(damage) {
        this.health = Math.max(this.health - damage, 0);
        
        if (this.health <= 0) {
            this.onDeath();
        }
    }

    /**
     * 治疗
     * @param {number} healing - 治疗值
     */
    heal(healing) {
        this.health = Math.min(this.health + healing, this.maxHealth);
    }

    /**
     * 死亡处理
     */
    onDeath() {
        DebugUtils.log('玩家死亡');
        // 触发死亡事件
    }

    /**
     * 更新UI
     */
    updateUI() {
        const sparkCountElement = DOMUtils.$('#spark-count');
        if (sparkCountElement) {
            sparkCountElement.textContent = this.sparksCollected;
        }
    }

    /**
     * 绘制玩家
     * @param {Renderer} renderer - 渲染器
     */
    draw(renderer) {
        const theme = renderer.getCurrentTheme();
        const pulseSize = Math.sin(this.pulsePhase) * 3 + this.radius;
        
        // 绘制外层发光效果
        renderer.drawGlow(0, 0, pulseSize * 2, theme.primary, this.glowIntensity * 0.6);
        
        // 绘制主体
        renderer.drawCircle(0, 0, this.radius, theme.accent);
        
        // 绘制内层发光
        renderer.drawGlow(0, 0, this.radius * 0.7, '#ffffff', this.glowIntensity);
        
        // 绘制边框
        const ctx = renderer.ctx;
        ctx.strokeStyle = theme.primary;
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.arc(0, 0, this.radius, 0, Math.PI * 2);
        ctx.stroke();
        
        // 绘制移动方向指示器
        if (this.isMoving) {
            const indicatorLength = this.radius + 10;
            const angle = Math.atan2(this.movementDirection.y, this.movementDirection.x);
            
            ctx.strokeStyle = theme.accent;
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(
                Math.cos(angle) * indicatorLength,
                Math.sin(angle) * indicatorLength
            );
            ctx.stroke();
        }
    }

    /**
     * 获取玩家状态数据
     * @returns {object} 状态数据
     */
    getState() {
        return {
            x: this.x,
            y: this.y,
            health: this.health,
            energy: this.energy,
            sparksCollected: this.sparksCollected,
            score: this.score,
            abilities: this.abilities
        };
    }

    /**
     * 设置玩家状态
     * @param {object} state - 状态数据
     */
    setState(state) {
        this.x = state.x || this.x;
        this.y = state.y || this.y;
        this.health = state.health || this.health;
        this.energy = state.energy || this.energy;
        this.sparksCollected = state.sparksCollected || this.sparksCollected;
        this.score = state.score || this.score;
        
        if (state.abilities) {
            Object.assign(this.abilities, state.abilities);
        }
        
        this.updateUI();
    }
}

// 导出玩家类
window.Player = Player;
