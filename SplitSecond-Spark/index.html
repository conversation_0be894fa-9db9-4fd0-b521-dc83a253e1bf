<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SplitSecond Spark - 时空火花</title>
    <meta name="description" content="一款创新的时空探索解谜游戏">
    <meta name="keywords" content="游戏,时空,探索,解谜,web游戏">
    
    <!-- PWA 支持 -->
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#1a1a2e">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <link rel="apple-touch-icon" href="assets/icon-192.png">
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/game.css">
    <link rel="stylesheet" href="css/ui.css">
    <link rel="stylesheet" href="css/effects.css">
</head>
<body>
    <!-- 加载屏幕 -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="game-logo">
                <h1>SplitSecond Spark</h1>
                <p>时空火花</p>
            </div>
            <div class="loading-bar">
                <div class="loading-progress"></div>
            </div>
            <div class="loading-text">正在加载游戏资源...</div>
        </div>
    </div>

    <!-- 主菜单 -->
    <div id="main-menu" class="main-menu hidden">
        <div class="menu-background"></div>
        <div class="menu-content">
            <div class="game-title">
                <h1>SplitSecond Spark</h1>
                <p class="subtitle">时空火花探索之旅</p>
            </div>
            <div class="menu-buttons">
                <button id="new-game-btn" class="menu-btn primary">开始新游戏</button>
                <button id="continue-game-btn" class="menu-btn secondary">继续游戏</button>
                <button id="settings-btn" class="menu-btn secondary">设置</button>
                <button id="help-btn" class="menu-btn secondary">帮助</button>
            </div>
        </div>
    </div>

    <!-- 游戏主界面 -->
    <div id="game-container" class="game-container hidden">
        <!-- 游戏画布 -->
        <canvas id="game-canvas" class="game-canvas"></canvas>
        
        <!-- 游戏UI -->
        <div class="game-ui">
            <!-- 顶部状态栏 -->
            <div class="top-bar">
                <div class="time-dimension-indicator">
                    <span class="time-label">时间维度:</span>
                    <div class="time-buttons">
                        <button id="past-btn" class="time-btn past">过去</button>
                        <button id="present-btn" class="time-btn present active">现在</button>
                        <button id="future-btn" class="time-btn future">未来</button>
                    </div>
                </div>
                <div class="spark-counter">
                    <span class="spark-icon">⚡</span>
                    <span id="spark-count">0</span>
                </div>
                <button id="pause-btn" class="pause-btn">⏸️</button>
            </div>

            <!-- 移动端控制器 -->
            <div class="mobile-controls">
                <div class="movement-pad">
                    <div class="movement-center"></div>
                    <button class="move-btn up" data-direction="up">↑</button>
                    <button class="move-btn down" data-direction="down">↓</button>
                    <button class="move-btn left" data-direction="left">←</button>
                    <button class="move-btn right" data-direction="right">→</button>
                </div>
                <div class="action-buttons">
                    <button id="interact-btn" class="action-btn">交互</button>
                    <button id="time-switch-btn" class="action-btn">时空切换</button>
                </div>
            </div>
        </div>

        <!-- 暂停菜单 -->
        <div id="pause-menu" class="pause-menu hidden">
            <div class="pause-content">
                <h2>游戏暂停</h2>
                <div class="pause-buttons">
                    <button id="resume-btn" class="menu-btn primary">继续游戏</button>
                    <button id="save-game-btn" class="menu-btn secondary">保存游戏</button>
                    <button id="main-menu-btn" class="menu-btn secondary">返回主菜单</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 设置界面 -->
    <div id="settings-menu" class="settings-menu hidden">
        <div class="settings-content">
            <h2>游戏设置</h2>
            <div class="settings-options">
                <div class="setting-item">
                    <label for="music-volume">音乐音量:</label>
                    <input type="range" id="music-volume" min="0" max="100" value="70">
                    <span class="volume-value">70%</span>
                </div>
                <div class="setting-item">
                    <label for="sfx-volume">音效音量:</label>
                    <input type="range" id="sfx-volume" min="0" max="100" value="80">
                    <span class="volume-value">80%</span>
                </div>
                <div class="setting-item">
                    <label for="graphics-quality">画质设置:</label>
                    <select id="graphics-quality">
                        <option value="low">低</option>
                        <option value="medium" selected>中</option>
                        <option value="high">高</option>
                    </select>
                </div>
            </div>
            <div class="settings-buttons">
                <button id="settings-back-btn" class="menu-btn primary">返回</button>
                <button id="reset-settings-btn" class="menu-btn secondary">重置设置</button>
            </div>
        </div>
    </div>

    <!-- 帮助界面 -->
    <div id="help-menu" class="help-menu hidden">
        <div class="help-content">
            <h2>游戏帮助</h2>
            <div class="help-sections">
                <div class="help-section">
                    <h3>游戏目标</h3>
                    <p>收集散布在不同时间维度的能量火花，解开时空谜题，探索神秘的时空世界。</p>
                </div>
                <div class="help-section">
                    <h3>操作方式</h3>
                    <ul>
                        <li><strong>移动:</strong> 使用WASD键或方向键移动角色</li>
                        <li><strong>时间切换:</strong> 点击顶部的时间按钮或按数字键1、2、3</li>
                        <li><strong>交互:</strong> 按空格键与物体交互</li>
                        <li><strong>暂停:</strong> 按ESC键暂停游戏</li>
                    </ul>
                </div>
                <div class="help-section">
                    <h3>游戏机制</h3>
                    <ul>
                        <li><strong>时间维度:</strong> 在过去、现在、未来三个时间线之间切换</li>
                        <li><strong>能量火花:</strong> 不同颜色的火花有不同效果</li>
                        <li><strong>时空谜题:</strong> 某些谜题需要在不同时间线协调解决</li>
                    </ul>
                </div>
            </div>
            <button id="help-back-btn" class="menu-btn primary">返回</button>
        </div>
    </div>

    <!-- 游戏脚本 -->
    <script src="js/utils.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/input.js"></script>
    <script src="js/renderer.js"></script>
    <script src="js/particles.js"></script>
    <script src="js/entities.js"></script>
    <script src="js/world.js"></script>
    <script src="js/time-dimension.js"></script>
    <script src="js/player.js"></script>
    <script src="js/spark-system.js"></script>
    <script src="js/game.js"></script>
    <script src="js/init.js"></script>
</body>
</html>
