#!/bin/bash

# SplitSecond Spark 游戏提交脚本
# 用于将游戏代码提交到Git仓库

echo "🎮 SplitSecond Spark - 游戏提交脚本"
echo "=================================="

# 检查是否在正确的目录
if [ ! -f "index.html" ]; then
    echo "❌ 错误：请在SplitSecond-Spark目录中运行此脚本"
    exit 1
fi

# 检查Git是否已初始化
if [ ! -d ".git" ]; then
    echo "📦 初始化Git仓库..."
    git init
    
    # 创建.gitignore文件
    cat > .gitignore << EOF
# 临时文件
*.tmp
*.log
.DS_Store
Thumbs.db

# 编辑器文件
.vscode/
.idea/
*.swp
*.swo

# 依赖文件（如果有的话）
node_modules/
npm-debug.log*

# 构建输出（如果有的话）
dist/
build/

# 测试覆盖率
coverage/

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
EOF
    
    echo "✅ Git仓库初始化完成"
fi

# 添加所有文件到暂存区
echo "📁 添加文件到暂存区..."
git add .

# 检查是否有文件被添加
if git diff --cached --quiet; then
    echo "ℹ️  没有新的更改需要提交"
    exit 0
fi

# 显示将要提交的文件
echo "📋 将要提交的文件："
git diff --cached --name-status

# 创建提交信息
COMMIT_MESSAGE="🎮 实现SplitSecond Spark时空探索游戏

✨ 新功能:
- 完整的时空维度切换系统（过去、现在、未来）
- 能量火花收集机制，支持6种不同类型的火花
- 响应式设计，完美支持PC和移动设备
- 本地存储系统，支持游戏进度保存和加载
- 丰富的粒子特效和动画系统
- PWA支持，可安装为独立应用

🎨 界面设计:
- 现代化的渐变UI设计
- 流畅的动画过渡效果
- 移动端虚拟控制器
- 时间维度主题化视觉效果

🔧 技术实现:
- 模块化的JavaScript架构
- Canvas 2D渲染引擎
- 统一的输入管理系统
- 空间分割的碰撞检测
- 实体组件系统

📱 平台支持:
- 桌面端：Windows、macOS、Linux
- 移动端：iOS、Android
- 浏览器：Chrome、Firefox、Safari、Edge

🎯 游戏特色:
- 创新的时间维度穿梭玩法
- 磁性收集系统提升游戏体验
- 多样化的玩家能力系统
- 沉浸式的视觉和音效设计

📚 文档完善:
- 详细的README说明文档
- 完整的代码注释（中文）
- 功能测试页面
- 部署和使用指南"

# 执行提交
echo "💾 提交更改..."
git commit -m "$COMMIT_MESSAGE"

if [ $? -eq 0 ]; then
    echo "✅ 提交成功！"
    
    # 显示提交信息
    echo ""
    echo "📊 提交统计："
    git show --stat HEAD
    
    echo ""
    echo "🎉 SplitSecond Spark 游戏开发完成！"
    echo ""
    echo "🚀 下一步操作："
    echo "1. 运行测试页面: 打开 test.html"
    echo "2. 开始游戏: 打开 index.html"
    echo "3. 部署到服务器: 上传所有文件到Web服务器"
    echo "4. 推送到远程仓库: git remote add origin <仓库地址> && git push -u origin main"
    echo ""
    echo "📖 更多信息请查看 README.md 文件"
    
else
    echo "❌ 提交失败！"
    exit 1
fi
