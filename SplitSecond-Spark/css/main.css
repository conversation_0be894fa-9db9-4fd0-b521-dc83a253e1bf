/* 主样式文件 - SplitSecond Spark */

/* 全局重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    font-family: 'Arial', 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: #ffffff;
    overflow: hidden;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* 通用工具类 */
.hidden {
    display: none !important;
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

.fade-out {
    animation: fadeOut 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

/* 加载屏幕样式 */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-content {
    text-align: center;
    max-width: 400px;
    padding: 20px;
}

.game-logo h1 {
    font-size: 3rem;
    font-weight: bold;
    background: linear-gradient(45deg, #00d4ff, #ff6b6b, #4ecdc4);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
    margin-bottom: 10px;
}

.game-logo p {
    font-size: 1.2rem;
    color: #a0a0a0;
    margin-bottom: 40px;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.loading-bar {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 20px;
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, #00d4ff, #4ecdc4);
    border-radius: 3px;
    width: 0%;
    transition: width 0.3s ease;
    animation: progressGlow 2s ease-in-out infinite;
}

@keyframes progressGlow {
    0%, 100% { box-shadow: 0 0 5px rgba(0, 212, 255, 0.5); }
    50% { box-shadow: 0 0 20px rgba(0, 212, 255, 0.8); }
}

.loading-text {
    font-size: 1rem;
    color: #a0a0a0;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
}

/* 主菜单样式 */
.main-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 900;
}

.menu-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    opacity: 0.95;
}

.menu-content {
    position: relative;
    text-align: center;
    max-width: 500px;
    padding: 40px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.game-title h1 {
    font-size: 3.5rem;
    font-weight: bold;
    background: linear-gradient(45deg, #00d4ff, #ff6b6b, #4ecdc4);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
    margin-bottom: 10px;
}

.subtitle {
    font-size: 1.3rem;
    color: #a0a0a0;
    margin-bottom: 40px;
}

.menu-buttons {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.menu-btn {
    padding: 15px 30px;
    font-size: 1.1rem;
    font-weight: 600;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.menu-btn.primary {
    background: linear-gradient(45deg, #00d4ff, #4ecdc4);
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
}

.menu-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
}

.menu-btn.secondary {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.menu-btn.secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.menu-btn:active {
    transform: translateY(0);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .game-logo h1, .game-title h1 {
        font-size: 2.5rem;
    }
    
    .menu-content {
        max-width: 90%;
        padding: 30px 20px;
    }
    
    .menu-btn {
        padding: 12px 25px;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .game-logo h1, .game-title h1 {
        font-size: 2rem;
    }
    
    .subtitle {
        font-size: 1.1rem;
    }
    
    .menu-btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
}
