/* 特效样式 - SplitSecond Spark */

/* 粒子效果基础样式 */
.particle {
    position: absolute;
    pointer-events: none;
    border-radius: 50%;
    animation: particleFloat 3s ease-out forwards;
}

@keyframes particleFloat {
    0% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
    100% {
        opacity: 0;
        transform: scale(0.3) translateY(-100px);
    }
}

/* 能量火花特效 */
.spark-particle {
    width: 4px;
    height: 4px;
    background: radial-gradient(circle, #ffff00 0%, #ff6b6b 50%, transparent 100%);
    box-shadow: 0 0 10px #ffff00;
    animation: sparkParticle 2s ease-out forwards;
}

@keyframes sparkParticle {
    0% {
        opacity: 1;
        transform: scale(1) rotate(0deg);
        box-shadow: 0 0 10px #ffff00;
    }
    50% {
        opacity: 0.8;
        transform: scale(1.5) rotate(180deg);
        box-shadow: 0 0 20px #ffff00, 0 0 30px #ff6b6b;
    }
    100% {
        opacity: 0;
        transform: scale(0.2) rotate(360deg);
        box-shadow: 0 0 5px #ffff00;
    }
}

/* 时间切换特效 */
.time-transition-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 150;
    background: radial-gradient(circle at center, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
    opacity: 0;
    animation: timeTransition 1s ease-in-out;
}

@keyframes timeTransition {
    0% {
        opacity: 0;
        transform: scale(0.8);
    }
    50% {
        opacity: 1;
        transform: scale(1.2);
    }
    100% {
        opacity: 0;
        transform: scale(1);
    }
}

/* 时间涟漪效果 */
.time-ripple {
    position: absolute;
    border: 2px solid rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    pointer-events: none;
    animation: rippleExpand 1.5s ease-out forwards;
}

@keyframes rippleExpand {
    0% {
        width: 0;
        height: 0;
        opacity: 1;
        border-width: 3px;
    }
    100% {
        width: 200px;
        height: 200px;
        opacity: 0;
        border-width: 1px;
    }
}

/* 收集效果 */
.collect-effect {
    position: absolute;
    pointer-events: none;
    font-size: 1.5rem;
    font-weight: bold;
    color: #ffff00;
    text-shadow: 0 0 10px #ffff00;
    animation: collectFloat 2s ease-out forwards;
}

@keyframes collectFloat {
    0% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    50% {
        opacity: 1;
        transform: translateY(-30px) scale(1.2);
    }
    100% {
        opacity: 0;
        transform: translateY(-60px) scale(0.8);
    }
}

/* 脉冲效果 */
.pulse-effect {
    animation: pulseGlow 2s ease-in-out infinite;
}

@keyframes pulseGlow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 0 20px rgba(255, 255, 255, 0.6), 0 0 30px rgba(255, 255, 255, 0.4);
        transform: scale(1.05);
    }
}

/* 闪烁效果 */
.blink-effect {
    animation: blink 1s ease-in-out infinite;
}

@keyframes blink {
    0%, 50%, 100% { opacity: 1; }
    25%, 75% { opacity: 0.3; }
}

/* 旋转效果 */
.rotate-effect {
    animation: rotate 4s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 浮动效果 */
.float-effect {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

/* 震动效果 */
.shake-effect {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
    20%, 40%, 60%, 80% { transform: translateX(2px); }
}

/* 缩放进入效果 */
.scale-in {
    animation: scaleIn 0.3s ease-out;
}

@keyframes scaleIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* 滑入效果 */
.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    0% {
        opacity: 0;
        transform: translateX(-100px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    0% {
        opacity: 0;
        transform: translateX(100px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 光晕效果 */
.glow-effect {
    position: relative;
}

.glow-effect::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #00d4ff, #4ecdc4, #ff6b6b, #00d4ff);
    border-radius: inherit;
    z-index: -1;
    filter: blur(10px);
    opacity: 0.7;
    animation: glowRotate 3s linear infinite;
}

@keyframes glowRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 星星闪烁背景效果 */
.stars-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.star {
    position: absolute;
    width: 2px;
    height: 2px;
    background: #ffffff;
    border-radius: 50%;
    animation: starTwinkle 3s ease-in-out infinite;
}

@keyframes starTwinkle {
    0%, 100% { 
        opacity: 0.3;
        transform: scale(1);
    }
    50% { 
        opacity: 1;
        transform: scale(1.5);
    }
}

/* 能量波纹效果 */
.energy-wave {
    position: absolute;
    border: 1px solid rgba(0, 212, 255, 0.6);
    border-radius: 50%;
    pointer-events: none;
    animation: energyWave 2s ease-out infinite;
}

@keyframes energyWave {
    0% {
        width: 10px;
        height: 10px;
        opacity: 1;
    }
    100% {
        width: 100px;
        height: 100px;
        opacity: 0;
    }
}

/* 传送门效果 */
.portal-effect {
    position: relative;
    overflow: hidden;
}

.portal-effect::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, #00d4ff, transparent, #4ecdc4, transparent);
    animation: portalSpin 2s linear infinite;
    transform: translate(-50%, -50%);
    z-index: -1;
}

@keyframes portalSpin {
    from { transform: translate(-50%, -50%) rotate(0deg); }
    to { transform: translate(-50%, -50%) rotate(360deg); }
}

/* 成功收集效果 */
@keyframes collectSuccess {
    0% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
    50% {
        transform: scale(1.5) rotate(180deg);
        opacity: 0.8;
    }
    100% {
        transform: scale(0) rotate(360deg);
        opacity: 0;
    }
}

/* 时间扭曲效果 */
@keyframes timeWarp {
    0% {
        transform: scale(1) rotate(0deg);
        filter: hue-rotate(0deg);
    }
    25% {
        transform: scale(1.1) rotate(90deg);
        filter: hue-rotate(90deg);
    }
    50% {
        transform: scale(0.9) rotate(180deg);
        filter: hue-rotate(180deg);
    }
    75% {
        transform: scale(1.1) rotate(270deg);
        filter: hue-rotate(270deg);
    }
    100% {
        transform: scale(1) rotate(360deg);
        filter: hue-rotate(360deg);
    }
}

/* 能量充能效果 */
@keyframes energyCharge {
    0% {
        box-shadow: 0 0 5px currentColor;
    }
    50% {
        box-shadow: 0 0 20px currentColor, 0 0 30px currentColor;
    }
    100% {
        box-shadow: 0 0 5px currentColor;
    }
}

/* 游戏胜利庆祝效果 */
@keyframes celebration {
    0%, 100% {
        transform: scale(1) rotate(0deg);
    }
    25% {
        transform: scale(1.2) rotate(5deg);
    }
    50% {
        transform: scale(1.1) rotate(-5deg);
    }
    75% {
        transform: scale(1.3) rotate(3deg);
    }
}
