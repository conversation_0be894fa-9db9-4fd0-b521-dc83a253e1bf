/* UI界面样式 - SplitSecond Spark */

/* 暂停菜单 */
.pause-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 200;
    backdrop-filter: blur(5px);
}

.pause-content {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 400px;
    width: 90%;
}

.pause-content h2 {
    font-size: 2rem;
    margin-bottom: 30px;
    color: #ffffff;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.pause-buttons {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* 设置菜单 */
.settings-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 300;
    backdrop-filter: blur(5px);
}

.settings-content {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 40px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 500px;
    width: 90%;
}

.settings-content h2 {
    font-size: 2rem;
    margin-bottom: 30px;
    color: #ffffff;
    text-align: center;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.settings-options {
    margin-bottom: 30px;
}

.setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.setting-item label {
    font-size: 1rem;
    color: #ffffff;
    font-weight: 600;
    min-width: 100px;
}

.setting-item input[type="range"] {
    flex: 1;
    margin: 0 15px;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
}

.setting-item input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    background: linear-gradient(45deg, #00d4ff, #4ecdc4);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(0, 212, 255, 0.3);
}

.setting-item input[type="range"]::-moz-range-thumb {
    width: 18px;
    height: 18px;
    background: linear-gradient(45deg, #00d4ff, #4ecdc4);
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 10px rgba(0, 212, 255, 0.3);
}

.setting-item select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 5px;
    padding: 8px 12px;
    color: #ffffff;
    font-size: 1rem;
    outline: none;
    cursor: pointer;
}

.setting-item select option {
    background: #1a1a2e;
    color: #ffffff;
}

.volume-value {
    font-size: 0.9rem;
    color: #4ecdc4;
    font-weight: 600;
    min-width: 40px;
    text-align: right;
}

.settings-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
}

/* 帮助菜单 */
.help-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 300;
    backdrop-filter: blur(5px);
    overflow-y: auto;
}

.help-content {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 40px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    margin: 20px;
}

.help-content h2 {
    font-size: 2rem;
    margin-bottom: 30px;
    color: #ffffff;
    text-align: center;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.help-sections {
    margin-bottom: 30px;
}

.help-section {
    margin-bottom: 25px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.help-section h3 {
    font-size: 1.3rem;
    color: #4ecdc4;
    margin-bottom: 15px;
    text-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
}

.help-section p {
    font-size: 1rem;
    color: #e0e0e0;
    line-height: 1.6;
    margin-bottom: 10px;
}

.help-section ul {
    list-style: none;
    padding-left: 0;
}

.help-section li {
    font-size: 1rem;
    color: #e0e0e0;
    line-height: 1.6;
    margin-bottom: 8px;
    padding-left: 20px;
    position: relative;
}

.help-section li::before {
    content: "▶";
    position: absolute;
    left: 0;
    color: #4ecdc4;
    font-size: 0.8rem;
}

.help-section strong {
    color: #00d4ff;
    font-weight: 600;
}

/* 通知系统 */
.notification {
    position: fixed;
    top: 80px;
    right: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 15px 20px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    font-size: 1rem;
    z-index: 400;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left: 4px solid #4ecdc4;
}

.notification.warning {
    border-left: 4px solid #f39c12;
}

.notification.error {
    border-left: 4px solid #e74c3c;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .pause-content,
    .settings-content,
    .help-content {
        padding: 30px 20px;
        margin: 10px;
    }
    
    .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .setting-item label {
        min-width: auto;
    }
    
    .setting-item input[type="range"] {
        width: 100%;
        margin: 0;
    }
    
    .settings-buttons {
        flex-direction: column;
    }
    
    .help-content {
        max-height: 90vh;
    }
}

@media (max-width: 480px) {
    .pause-content h2,
    .settings-content h2,
    .help-content h2 {
        font-size: 1.5rem;
    }
    
    .help-section {
        padding: 15px;
    }
    
    .notification {
        right: 10px;
        left: 10px;
        transform: translateY(-100%);
    }
    
    .notification.show {
        transform: translateY(0);
    }
}
