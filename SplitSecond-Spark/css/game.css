/* 游戏界面样式 - SplitSecond Spark */

/* 游戏容器 */
.game-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #000;
    overflow: hidden;
}

/* 游戏画布 */
.game-canvas {
    display: block;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #0a0a1a 0%, #1a1a2e 50%, #2a2a3e 100%);
    image-rendering: pixelated;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
}

/* 游戏UI覆盖层 */
.game-ui {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 100;
}

.game-ui > * {
    pointer-events: auto;
}

/* 顶部状态栏 */
.top-bar {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 60px;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 100%);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    backdrop-filter: blur(5px);
}

/* 时间维度指示器 */
.time-dimension-indicator {
    display: flex;
    align-items: center;
    gap: 15px;
}

.time-label {
    font-size: 1rem;
    color: #ffffff;
    font-weight: 600;
}

.time-buttons {
    display: flex;
    gap: 8px;
}

.time-btn {
    padding: 8px 16px;
    font-size: 0.9rem;
    font-weight: 600;
    border: 2px solid transparent;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.time-btn.past {
    background: linear-gradient(45deg, #8b4513, #a0522d);
    color: #ffffff;
}

.time-btn.present {
    background: linear-gradient(45deg, #00d4ff, #4ecdc4);
    color: #ffffff;
}

.time-btn.future {
    background: linear-gradient(45deg, #9b59b6, #e74c3c);
    color: #ffffff;
}

.time-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
}

.time-btn.active {
    border-color: #ffffff;
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
    transform: scale(1.05);
}

/* 能量火花计数器 */
.spark-counter {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 16px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

.spark-icon {
    font-size: 1.2rem;
    animation: sparkGlow 2s ease-in-out infinite;
}

@keyframes sparkGlow {
    0%, 100% { 
        text-shadow: 0 0 5px #ffff00;
        transform: scale(1);
    }
    50% { 
        text-shadow: 0 0 15px #ffff00, 0 0 25px #ffff00;
        transform: scale(1.1);
    }
}

#spark-count {
    font-size: 1.1rem;
    font-weight: bold;
    color: #ffff00;
    min-width: 30px;
    text-align: center;
}

/* 暂停按钮 */
.pause-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.pause-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

/* 移动端控制器 */
.mobile-controls {
    position: fixed;
    bottom: 20px;
    left: 0;
    right: 0;
    display: none;
    justify-content: space-between;
    align-items: flex-end;
    padding: 0 20px;
    z-index: 1000;
    pointer-events: none;
}

.mobile-controls > * {
    pointer-events: auto;
}

.movement-pad {
    position: relative;
    width: 120px;
    height: 120px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    backdrop-filter: blur(10px);
}

.movement-center {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
}

.move-btn {
    position: absolute;
    width: 35px;
    height: 35px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    color: #ffffff;
    font-size: 1.2rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
}

.move-btn:active {
    background: rgba(255, 255, 255, 0.4);
    transform: scale(0.9);
}

.move-btn.up {
    top: 5px;
    left: 50%;
    transform: translateX(-50%);
}

.move-btn.down {
    bottom: 5px;
    left: 50%;
    transform: translateX(-50%);
}

.move-btn.left {
    left: 5px;
    top: 50%;
    transform: translateY(-50%);
}

.move-btn.right {
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.action-btn {
    padding: 12px 20px;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 25px;
    color: #ffffff;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);
}

.action-btn:active {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0.95);
}

/* 响应式设计 - 移动端显示控制器 */
@media (max-width: 768px) {
    .mobile-controls {
        display: flex;
    }
    
    .top-bar {
        padding: 0 15px;
        height: 50px;
    }
    
    .time-btn {
        padding: 6px 12px;
        font-size: 0.8rem;
    }
    
    .time-label {
        font-size: 0.9rem;
    }

    /* 禁用移动端的选择和缩放 */
    .game-canvas {
        touch-action: none;
        user-select: none;
        -webkit-user-select: none;
        -webkit-touch-callout: none;
    }

    /* 优化移动端按钮触摸体验 */
    .move-btn, .action-btn, .time-btn {
        -webkit-tap-highlight-color: transparent;
        touch-action: manipulation;
    }
}

@media (max-width: 480px) {
    .top-bar {
        padding: 0 10px;
        height: 45px;
    }
    
    .time-buttons {
        gap: 5px;
    }
    
    .time-btn {
        padding: 5px 10px;
        font-size: 0.7rem;
    }
    
    .movement-pad {
        width: 100px;
        height: 100px;
    }
    
    .movement-center {
        width: 30px;
        height: 30px;
    }
    
    .move-btn {
        width: 30px;
        height: 30px;
        font-size: 1rem;
    }
}
