# SplitSecond Spark - 时空火花

一款创新的时空探索网页游戏，玩家可以在不同的时间维度间穿梭，收集能量火花，探索神秘的时空世界。

## 🎮 游戏特色

### 核心玩法
- **时间维度穿梭**：在过去、现在、未来三个时间线之间自由切换
- **能量火花收集**：收集不同类型的能量火花，解锁新能力
- **空间探索**：在广阔的2D世界中自由移动和探索
- **动态世界**：每个时间维度都有独特的地图布局和挑战

### 技术特色
- **纯前端实现**：基于HTML5 Canvas和JavaScript
- **响应式设计**：完美支持PC和移动设备
- **本地存储**：使用localStorage保存游戏进度
- **PWA支持**：可安装为独立应用
- **粒子特效**：丰富的视觉效果和动画

## 🕹️ 操作指南

### PC端控制
- **移动**：WASD键或方向键
- **交互**：空格键或E键
- **时间切换**：点击顶部的时间维度按钮
- **暂停**：ESC键或P键
- **冲刺**：Shift键（解锁后）
- **能量爆发**：Q键（解锁后）

### 移动端控制
- **移动**：使用屏幕底部的虚拟摇杆
- **交互**：点击"交互"按钮
- **时间切换**：点击顶部的时间维度按钮
- **暂停**：点击暂停按钮

## 🌟 游戏系统

### 时间维度系统
1. **过去（Past）**
   - 主题色：棕色/金色
   - 特点：古老的废墟和记忆碎片
   - 火花类型：记忆火花、回响火花

2. **现在（Present）**
   - 主题色：青色/白色
   - 特点：现代化的结构和能量场
   - 火花类型：时间火花、能量火花

3. **未来（Future）**
   - 主题色：紫色/红色
   - 特点：科技感的水晶和等离子体
   - 火花类型：量子火花、虚空火花

### 能量火花类型
- **时间火花**（蓝色）：恢复能量，最常见
- **空间火花**（紫色）：提升移动速度
- **量子火花**（红色）：解锁冲刺能力
- **虚空火花**（黑色）：提供护盾保护
- **记忆火花**（金色）：解锁过去维度
- **回响火花**（青色）：解锁未来维度

### 玩家能力
- **基础移动**：360度自由移动
- **磁性收集**：靠近火花时自动吸引
- **冲刺**：快速移动一段距离（消耗能量）
- **能量爆发**：清除周围障碍（消耗能量）
- **时间感知**：查看不同维度的状态

## 🎨 视觉效果

### 粒子系统
- **火花特效**：收集时的爆发效果
- **时间切换**：维度转换的漩涡效果
- **移动拖尾**：玩家移动时的光迹
- **环境粒子**：增强沉浸感的背景效果

### 动画效果
- **脉冲发光**：火花的呼吸效果
- **旋转轨道**：火花的轨道运动
- **渐变过渡**：时间维度的颜色变化
- **缩放动画**：UI元素的交互反馈

## 🔧 技术架构

### 核心模块
- **游戏引擎**（game.js）：主游戏循环和状态管理
- **渲染系统**（renderer.js）：Canvas绘制和相机控制
- **输入管理**（input.js）：键盘、鼠标、触摸事件处理
- **物理世界**（world.js）：碰撞检测和空间查询
- **实体系统**（entities.js）：游戏对象的基础类

### 游戏系统
- **时间维度**（time-dimension.js）：时间穿梭机制
- **玩家系统**（player.js）：角色控制和状态管理
- **火花系统**（spark-system.js）：能量火花的生成和收集
- **粒子系统**（particles.js）：视觉特效处理
- **存储系统**（storage.js）：游戏数据持久化

### 工具模块
- **数学工具**（utils.js）：数学计算和辅助函数
- **初始化脚本**（init.js）：系统启动和错误处理

## 📱 兼容性

### 支持的浏览器
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### 支持的设备
- **桌面端**：Windows、macOS、Linux
- **移动端**：iOS 12+、Android 7+
- **平板端**：iPad、Android平板

### 性能要求
- **最低配置**：双核CPU、2GB RAM
- **推荐配置**：四核CPU、4GB RAM
- **网络要求**：无需网络连接（离线可玩）

## 🚀 部署说明

### 本地运行
```bash
# 克隆项目
git clone [项目地址]

# 进入项目目录
cd SplitSecond-Spark

# 启动本地服务器
python3 -m http.server 8000

# 或使用Node.js
npx serve .

# 访问游戏
# 打开浏览器访问 http://localhost:8000
```

### 静态部署
游戏支持静态部署到任何Web服务器：
- GitHub Pages
- Netlify
- Vercel
- 传统Web服务器

只需将所有文件上传到服务器根目录即可。

## 🎯 游戏目标

### 短期目标
- 收集50个能量火花
- 解锁所有时间维度
- 掌握所有玩家能力

### 长期目标
- 探索所有隐藏区域
- 收集所有类型的火花
- 达到最高分数记录

## 🔮 未来计划

### 即将推出
- 更多时间维度
- 新的火花类型
- 成就系统
- 排行榜功能

### 长期规划
- 多人合作模式
- 关卡编辑器
- 音效和背景音乐
- 更丰富的剧情

## 🐛 问题反馈

如果您在游戏过程中遇到任何问题，请：
1. 检查浏览器控制台是否有错误信息
2. 确认浏览器版本是否支持
3. 尝试清除浏览器缓存
4. 联系开发团队

## 📄 开源协议

本项目采用MIT开源协议，欢迎贡献代码和提出建议。

---

**享受您的时空探索之旅！** ✨

*SplitSecond Spark - 在时间的缝隙中寻找永恒的火花*
